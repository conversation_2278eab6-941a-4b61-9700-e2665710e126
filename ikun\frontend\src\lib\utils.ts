import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: string | Date) {
  return new Intl.DateTimeFormat("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
  }).format(new Date(date))
}

export function formatPrice(price: number) {
  return new Intl.NumberFormat("zh-CN", {
    style: "currency",
    currency: "CNY",
  }).format(price)
}

export function formatNumber(num: number) {
  return new Intl.NumberFormat("zh-CN").format(num)
}

export function truncateText(text: string, maxLength: number) {
  if (text.length <= maxLength) return text
  return text.slice(0, maxLength) + "..."
}

export function getStatusColor(status: string) {
  const statusColors = {
    active: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
    inactive: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
    pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
    running: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
    completed: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
    failed: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
    draft: "bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300",
  }
  return statusColors[status as keyof typeof statusColors] || statusColors.pending
}

export function getStatusText(status: string) {
  const statusTexts = {
    active: "活跃",
    inactive: "非活跃",
    pending: "待处理",
    running: "运行中",
    completed: "已完成",
    failed: "失败",
    draft: "草稿",
    paid: "已付款",
    shipped: "已发货",
    delivered: "已送达",
    cancelled: "已取消",
  }
  return statusTexts[status as keyof typeof statusTexts] || status
}
