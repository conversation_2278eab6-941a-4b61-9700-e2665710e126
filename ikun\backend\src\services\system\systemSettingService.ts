/**
 * 系统设置服务
 * 处理系统配置的业务逻辑
 */
import SystemSettingModel, { SystemSetting } from '../../models/system/SystemSettingModel';
import { logger } from '@/utils/logger';
import { BadRequestError, NotFoundError } from '@/middleware/error/errorHandler';
import { TranslationConfig } from '@/services/translation/config';

export class SystemSettingService {
  private systemSettingModel: SystemSettingModel;

  constructor() {
    this.systemSettingModel = new SystemSettingModel();
  }

  /**
   * 获取所有设置（按分类分组）
   */
  public async getAllSettings(): Promise<Record<string, SystemSetting[]>> {
    try {
      const settings = await this.systemSettingModel.findMany({
        orderBy: 'category, sort_order'
      });

      // 按分类分组
      const groupedSettings: Record<string, SystemSetting[]> = {};
      settings.forEach((setting: SystemSetting) => {
        if (!groupedSettings[setting.category]) {
          groupedSettings[setting.category] = [];
        }
        groupedSettings[setting.category].push(setting);
      });

      return groupedSettings;
    } catch (error) {
      logger.error('Error getting all settings:', error);
      throw error;
    }
  }

  /**
   * 根据分类获取设置
   */
  public async getSettingsByCategory(category: string): Promise<SystemSetting[]> {
    try {
      return await this.systemSettingModel.getSettingsByCategory(category);
    } catch (error) {
      logger.error('Error getting settings by category:', error);
      throw error;
    }
  }

  /**
   * 获取单个设置
   */
  public async getSetting(key: string): Promise<SystemSetting> {
    try {
      const setting = await this.systemSettingModel.findOne({ setting_key: key });
      
      if (!setting) {
        throw new NotFoundError(`Setting '${key}' not found`);
      }

      return setting;
    } catch (error) {
      logger.error('Error getting setting:', error);
      throw error;
    }
  }

  /**
   * 获取设置值
   */
  public async getSettingValue(key: string): Promise<any> {
    try {
      return await this.systemSettingModel.getSettingValue(key);
    } catch (error) {
      logger.error('Error getting setting value:', error);
      throw error;
    }
  }

  /**
   * 更新设置
   */
  public async updateSetting(key: string, value: any): Promise<SystemSetting> {
    try {
      // 检查设置是否存在且可编辑
      const setting = await this.getSetting(key);

      if (!setting.is_editable) {
        throw new BadRequestError(`Setting '${key}' is not editable`);
      }

      // 如果是翻译提供商配置更新，检查模型名称变更并同步翻译场景配置和Token统计
      if (key === 'translation_providers') {
        await this.syncTranslationScenariosAfterProviderUpdate(setting.setting_value, value);
        await this.syncTokenStatisticsAfterProviderUpdate(setting.setting_value, value);
      }

      // 更新设置值
      const success = await this.systemSettingModel.setSettingValue(key, value);

      if (!success) {
        throw new BadRequestError(`Failed to update setting '${key}'`);
      }

      // 如果是翻译相关配置，同步更新到TranslationConfig
      if (key === 'translation_providers' || key === 'translation_scenarios') {
        await this.syncTranslationConfigToMemory();
      }

      // 返回更新后的设置
      return await this.getSetting(key);
    } catch (error) {
      logger.error('Error updating setting:', error);
      throw error;
    }
  }

  /**
   * 同步更新翻译场景配置中的模型名称
   */
  private async syncTranslationScenariosAfterProviderUpdate(oldProviders: any, newProviders: any): Promise<void> {
    try {
      // 检测DeepSeek火山引擎模型名称变更
      const modelNameChanges = this.detectModelNameChanges(oldProviders, newProviders);

      if (modelNameChanges.length === 0) {
        return; // 没有模型名称变更，无需同步
      }

      // 获取当前的翻译场景配置
      const scenariosSetting = await this.getSetting('translation_scenarios');
      const scenarios = scenariosSetting.setting_value;

      if (!scenarios) {
        return; // 没有翻译场景配置，无需同步
      }

      let hasChanges = false;
      const updatedScenarios = JSON.parse(JSON.stringify(scenarios)); // 深拷贝

      // 遍历所有平台和场景
      Object.keys(updatedScenarios).forEach(platformCode => {
        const platform = updatedScenarios[platformCode];
        Object.keys(platform.scenarios).forEach(scenarioKey => {
          const scenario = platform.scenarios[scenarioKey];

          // 检查每种内容类型的配置
          ['title', 'description', 'selling_point'].forEach(contentType => {
            const config = scenario.content_types[contentType];

            // 检查是否是DeepSeek配置且使用了被重命名的模型
            if (config && typeof config === 'object' &&
                config.provider === 'deepseek_huoshan' &&
                config.sub_service) {

              // 查找是否有对应的模型名称变更
              const nameChange = modelNameChanges.find(change => change.oldName === config.sub_service);
              if (nameChange) {
                scenario.content_types[contentType] = {
                  ...config,
                  sub_service: nameChange.newName
                };
                hasChanges = true;
                logger.info(`Updated translation scenario: ${platformCode}.${scenarioKey}.${contentType} DeepSeek model name from ${nameChange.oldName} to ${nameChange.newName}`);
              }
            }

            // 检查是否是OpenAI兼容配置且使用了被重命名的提供商
            if (config && typeof config === 'object' &&
                config.provider === 'with_openai' &&
                config.sub_service) {

              // 查找是否有对应的提供商名称变更
              const nameChange = modelNameChanges.find(change => change.oldName === config.sub_service);
              if (nameChange) {
                scenario.content_types[contentType] = {
                  ...config,
                  sub_service: nameChange.newName
                };
                hasChanges = true;
                logger.info(`Updated translation scenario: ${platformCode}.${scenarioKey}.${contentType} OpenAI provider name from ${nameChange.oldName} to ${nameChange.newName}`);
              }
            }
          });
        });
      });

      // 如果有变更，更新翻译场景配置
      if (hasChanges) {
        await this.systemSettingModel.setSettingValue('translation_scenarios', updatedScenarios);
        logger.info('Translation scenarios synchronized after model name changes');
      }

      // 同步更新Token统计数据中的模型名称
      if (modelNameChanges.length > 0) {
        await this.syncTokenStatisticsAfterModelRename(modelNameChanges);
      }
    } catch (error) {
      logger.error('Error syncing translation scenarios after provider update:', error);
      // 不抛出错误，避免影响主要的配置更新流程
    }
  }

  /**
   * 检测模型名称变更
   */
  private detectModelNameChanges(oldProviders: any, newProviders: any): Array<{oldName: string, newName: string}> {
    const changes: Array<{oldName: string, newName: string}> = [];

    if (!oldProviders?.deepseek_huoshan || !newProviders?.deepseek_huoshan) {
      return changes;
    }

    // 检查单模型配置变更
    if (oldProviders.deepseek_huoshan.one_model && newProviders.deepseek_huoshan.one_model) {
      const oldModels = Array.isArray(oldProviders.deepseek_huoshan.one_model)
        ? oldProviders.deepseek_huoshan.one_model
        : [oldProviders.deepseek_huoshan.one_model];
      const newModels = Array.isArray(newProviders.deepseek_huoshan.one_model)
        ? newProviders.deepseek_huoshan.one_model
        : [newProviders.deepseek_huoshan.one_model];

      this.compareModelArrays(oldModels, newModels, changes);
    }

    // 检查多模型配置变更
    if (oldProviders.deepseek_huoshan.mult_models && newProviders.deepseek_huoshan.mult_models) {
      this.compareModelArrays(oldProviders.deepseek_huoshan.mult_models, newProviders.deepseek_huoshan.mult_models, changes);
    }

    // 检查OpenAI兼容提供商配置变更
    if (oldProviders.with_openai?.providers && newProviders.with_openai?.providers) {
      this.compareModelArrays(oldProviders.with_openai.providers, newProviders.with_openai.providers, changes);
    }

    return changes;
  }

  /**
   * 比较模型数组，找出名称变更
   */
  private compareModelArrays(oldModels: any[], newModels: any[], changes: Array<{oldName: string, newName: string}>): void {
    oldModels.forEach(oldModel => {
      const newModel = newModels.find(m => m.models_id === oldModel.models_id);
      if (newModel && newModel.models_name !== oldModel.models_name) {
        changes.push({
          oldName: oldModel.models_name,
          newName: newModel.models_name
        });
      }
    });
  }

  /**
   * 同步更新Token统计数据中的模型名称
   */
  private async syncTokenStatisticsAfterModelRename(modelNameChanges: Array<{oldName: string, newName: string}>): Promise<void> {
    try {
      // 获取当前的Token统计数据
      const tokenStatistics = await this.getTokenStatistics();
      let hasChanges = false;

      // 处理DeepSeek火山引擎模型名称变更
      for (const change of modelNameChanges) {
        // 检查deepseek_huoshan.models中是否有该模型
        if (tokenStatistics.deepseek_huoshan.models[change.oldName]) {
          // 将旧模型的统计数据迁移到新名称
          tokenStatistics.deepseek_huoshan.models[change.newName] = tokenStatistics.deepseek_huoshan.models[change.oldName];
          // 删除旧名称的统计数据
          delete tokenStatistics.deepseek_huoshan.models[change.oldName];
          hasChanges = true;
          logger.info(`Updated token statistics: DeepSeek model name from ${change.oldName} to ${change.newName}`);
        }

        // 检查openai_compatible.models中是否有该提供商
        if (tokenStatistics.openai_compatible.models[change.oldName]) {
          // 将旧提供商的统计数据迁移到新名称
          tokenStatistics.openai_compatible.models[change.newName] = tokenStatistics.openai_compatible.models[change.oldName];
          // 删除旧名称的统计数据
          delete tokenStatistics.openai_compatible.models[change.oldName];
          hasChanges = true;
          logger.info(`Updated token statistics: OpenAI provider name from ${change.oldName} to ${change.newName}`);
        }
      }

      // 如果有变更，更新Token统计数据
      if (hasChanges) {
        await this.updateTokenStatistics(tokenStatistics);
        logger.info('Token statistics synchronized after model name changes');
      }
    } catch (error) {
      logger.error('Error syncing token statistics after model rename:', error);
      // 不抛出错误，避免影响主要的配置更新流程
    }
  }

  /**
   * 同步更新Token统计数据中的模型添加和删除
   */
  private async syncTokenStatisticsAfterProviderUpdate(oldProviders: any, newProviders: any): Promise<void> {
    try {
      // 获取当前的Token统计数据
      const tokenStatistics = await this.getTokenStatistics();
      let hasChanges = false;

      // 处理DeepSeek火山引擎模型变更
      if (oldProviders.deepseek_huoshan && newProviders.deepseek_huoshan) {
        const oldModels = new Set(oldProviders.deepseek_huoshan.mult_models?.map((m: any) => m.models_name) || []);
        const newModels = new Set(newProviders.deepseek_huoshan.mult_models?.map((m: any) => m.models_name) || []);

        // 添加新模型的统计记录
        for (const modelName of newModels) {
          const modelNameStr = String(modelName);
          if (!oldModels.has(modelName) && !tokenStatistics.deepseek_huoshan.models[modelNameStr]) {
            tokenStatistics.deepseek_huoshan.models[modelNameStr] = {
              yesterday_input_tokens: 0,
              yesterday_output_tokens: 0,
              today_input_tokens: 0,
              today_output_tokens: 0,
              total_input_tokens: 0,
              total_output_tokens: 0
            };
            hasChanges = true;
            logger.info(`Added token statistics for new DeepSeek model: ${modelNameStr}`);
          }
        }

        // 删除已移除模型的统计记录
        for (const modelName of oldModels) {
          const modelNameStr = String(modelName);
          if (!newModels.has(modelName) && tokenStatistics.deepseek_huoshan.models[modelNameStr]) {
            delete tokenStatistics.deepseek_huoshan.models[modelNameStr];
            hasChanges = true;
            logger.info(`Removed token statistics for deleted DeepSeek model: ${modelNameStr}`);
          }
        }
      }

      // 处理OpenAI兼容服务提供商变更
      if (oldProviders.with_openai && newProviders.with_openai) {
        const oldProviderNames = new Set(oldProviders.with_openai.providers?.map((p: any) => p.models_name) || []);
        const newProviderNames = new Set(newProviders.with_openai.providers?.map((p: any) => p.models_name) || []);

        // 添加新提供商的统计记录
        for (const providerName of newProviderNames) {
          const providerNameStr = String(providerName);
          if (!oldProviderNames.has(providerName) && !tokenStatistics.openai_compatible.models[providerNameStr]) {
            tokenStatistics.openai_compatible.models[providerNameStr] = {
              yesterday_input_tokens: 0,
              yesterday_output_tokens: 0,
              today_input_tokens: 0,
              today_output_tokens: 0,
              total_input_tokens: 0,
              total_output_tokens: 0
            };
            hasChanges = true;
            logger.info(`Added token statistics for new OpenAI provider: ${providerNameStr}`);
          }
        }

        // 删除已移除提供商的统计记录
        for (const providerName of oldProviderNames) {
          const providerNameStr = String(providerName);
          if (!newProviderNames.has(providerName) && tokenStatistics.openai_compatible.models[providerNameStr]) {
            delete tokenStatistics.openai_compatible.models[providerNameStr];
            hasChanges = true;
            logger.info(`Removed token statistics for deleted OpenAI provider: ${providerNameStr}`);
          }
        }
      }

      // 如果有变更，更新Token统计数据
      if (hasChanges) {
        await this.updateTokenStatistics(tokenStatistics);
        logger.info('Token statistics synchronized after provider update');
      }
    } catch (error) {
      logger.error('Error syncing token statistics after provider update:', error);
      // 不抛出错误，避免影响主要的配置更新流程
    }
  }



  /**
   * 同步翻译配置到内存（TranslationConfig）
   */
  private async syncTranslationConfigToMemory(): Promise<void> {
    try {
      // 获取最新的翻译提供商配置
      const translationProviders = await this.getSettingValue('translation_providers') || {};

      // 获取最新的翻译场景配置
      const translationScenarios = await this.getSettingValue('translation_scenarios') || {};

      // 更新到TranslationConfig内存中
      const translationConfig = TranslationConfig.getInstance();
      translationConfig.updateAllConfigs(translationProviders, translationScenarios);

      logger.info('[SystemSettingService] 翻译配置已同步到内存');
    } catch (error) {
      logger.error('[SystemSettingService] 同步翻译配置到内存失败:', error);
      // 不抛出错误，避免影响主要的配置更新流程
    }
  }

  /**
   * 批量更新设置
   */
  public async batchUpdateSettings(updates: Array<{key: string, value: any}>): Promise<boolean> {
    try {
      // 验证所有设置都存在且可编辑
      for (const update of updates) {
        const setting = await this.getSetting(update.key);
        if (!setting.is_editable) {
          throw new BadRequestError(`Setting '${update.key}' is not editable`);
        }
      }

      // 批量更新
      return await this.systemSettingModel.batchUpdateSettings(updates);
    } catch (error) {
      logger.error('Error batch updating settings:', error);
      throw error;
    }
  }

  /**
   * 获取公开设置（前端可见）
   */
  public async getPublicSettings(): Promise<Record<string, any>> {
    try {
      const settings = await this.systemSettingModel.getPublicSettings();
      
      // 转换为键值对格式
      const publicSettings: Record<string, any> = {};
      settings.forEach((setting: SystemSetting) => {
        publicSettings[setting.setting_key] = setting.setting_value;
      });

      return publicSettings;
    } catch (error) {
      logger.error('Error getting public settings:', error);
      throw error;
    }
  }

  /**
   * 重置设置为默认值
   */
  public async resetToDefault(key: string): Promise<SystemSetting> {
    try {
      const setting = await this.getSetting(key);
      
      if (!setting.is_editable) {
        throw new BadRequestError(`Setting '${key}' is not editable`);
      }

      const success = await this.systemSettingModel.resetToDefault(key);
      
      if (!success) {
        throw new BadRequestError(`Failed to reset setting '${key}' or no default value available`);
      }

      return await this.getSetting(key);
    } catch (error) {
      logger.error('Error resetting setting:', error);
      throw error;
    }
  }

  /**
   * 获取翻译服务配置
   */
  public async getTranslationConfig(): Promise<{
    providers: any;
    scenarios: any;
    defaults: any;
  }> {
    try {
      const [providers, scenarios, defaults] = await Promise.all([
        this.getSettingValue('translation_providers'),
        this.getSettingValue('translation_scenarios'),
        this.getSettingValue('translation_defaults')
      ]);

      return {
        providers: providers || {},
        scenarios: scenarios || {},
        defaults: defaults || {}
      };
    } catch (error) {
      logger.error('Error getting translation config:', error);
      throw error;
    }
  }

  /**
   * 更新翻译服务配置
   */
  public async updateTranslationConfig(config: {
    providers?: any;
    scenarios?: any;
  }): Promise<boolean> {
    try {
      const updates = [];

      if (config.providers) {
        updates.push({ key: 'translation_providers', value: config.providers });
      }
      if (config.scenarios) {
        updates.push({ key: 'translation_scenarios', value: config.scenarios });
      }

      if (updates.length === 0) {
        return true;
      }

      const result = await this.batchUpdateSettings(updates);

      // 同步翻译配置到内存
      if (result) {
        await this.syncTranslationConfigToMemory();
      }

      return result;
    } catch (error) {
      logger.error('Error updating translation config:', error);
      throw error;
    }
  }

  /**
   * 获取Token统计数据
   */
  public async getTokenStatistics(): Promise<any> {
    try {
      return await this.getSettingValue('token_statistics') || {
        deepseek_huoshan: { models: {} },
        openai_compatible: { models: {} },
        global_statistics: {
          total_input_tokens: 0,
          total_output_tokens: 0,
          total_api_calls: 0
        }
      };
    } catch (error) {
      logger.error('Error getting token statistics:', error);
      throw error;
    }
  }

  /**
   * 更新Token统计数据
   */
  public async updateTokenStatistics(statistics: any): Promise<boolean> {
    try {
      return await this.systemSettingModel.setSettingValue('token_statistics', statistics);
    } catch (error) {
      logger.error('Error updating token statistics:', error);
      throw error;
    }
  }

  /**
   * 增加Token使用量
   */
  public async incrementTokenUsage(
    provider: 'deepseek_huoshan' | 'openai_compatible',
    modelName: string,
    inputTokens: number,
    outputTokens: number
  ): Promise<boolean> {
    try {
      const statistics = await this.getTokenStatistics();

      // 确保模型统计存在
      if (!statistics[provider].models[modelName]) {
        statistics[provider].models[modelName] = {
          yesterday_input_tokens: 0,
          yesterday_output_tokens: 0,
          today_input_tokens: 0,
          today_output_tokens: 0,
          total_input_tokens: 0,
          total_output_tokens: 0
        };
      }

      // 更新模型统计
      statistics[provider].models[modelName].today_input_tokens += inputTokens;
      statistics[provider].models[modelName].today_output_tokens += outputTokens;
      statistics[provider].models[modelName].total_input_tokens += inputTokens;
      statistics[provider].models[modelName].total_output_tokens += outputTokens;

      // 更新全局统计
      statistics.global_statistics.total_input_tokens += inputTokens;
      statistics.global_statistics.total_output_tokens += outputTokens;
      statistics.global_statistics.total_api_calls += 1;

      return await this.updateTokenStatistics(statistics);
    } catch (error) {
      logger.error('Error incrementing token usage:', error);
      throw error;
    }
  }

  /**
   * 重置今日Token统计（每日凌晨调用）
   */
  public async resetDailyTokenStats(): Promise<boolean> {
    try {
      const statistics = await this.getTokenStatistics();

      // 重置DeepSeek火山引擎模型的今日统计
      for (const modelName in statistics.deepseek_huoshan.models) {
        const model = statistics.deepseek_huoshan.models[modelName];
        model.yesterday_input_tokens = model.today_input_tokens;
        model.yesterday_output_tokens = model.today_output_tokens;
        model.today_input_tokens = 0;
        model.today_output_tokens = 0;
      }

      // 重置兼容OpenAI模型的今日统计
      for (const modelName in statistics.openai_compatible.models) {
        const model = statistics.openai_compatible.models[modelName];
        model.yesterday_input_tokens = model.today_input_tokens;
        model.yesterday_output_tokens = model.today_output_tokens;
        model.today_input_tokens = 0;
        model.today_output_tokens = 0;
      }

      return await this.updateTokenStatistics(statistics);
    } catch (error) {
      logger.error('Error resetting daily token stats:', error);
      throw error;
    }
  }

  /**
   * 获取系统基础配置
   */
  public async getSystemBasicConfig(): Promise<any> {
    try {
      return await this.getSettingValue('system_basic') || {};
    } catch (error) {
      logger.error('Error getting system basic config:', error);
      throw error;
    }
  }

  /**
   * 更新系统基础配置
   */
  public async updateSystemBasicConfig(config: any): Promise<boolean> {
    try {
      return await this.systemSettingModel.setSettingValue('system_basic', config);
    } catch (error) {
      logger.error('Error updating system basic config:', error);
      throw error;
    }
  }

  /**
   * 获取所有分类
   */
  public async getCategories(): Promise<string[]> {
    try {
      return await this.systemSettingModel.getCategories();
    } catch (error) {
      logger.error('Error getting categories:', error);
      throw error;
    }
  }

  /**
   * 创建新设置
   */
  public async createSetting(data: Partial<SystemSetting>): Promise<SystemSetting> {
    try {
      // 验证必填字段
      if (!data.setting_key || !data.category || !data.display_name) {
        throw new BadRequestError('setting_key, category, and display_name are required');
      }

      return await this.systemSettingModel.createSetting(data);
    } catch (error) {
      logger.error('Error creating setting:', error);
      throw error;
    }
  }

  /**
   * 删除设置
   */
  public async deleteSetting(key: string): Promise<boolean> {
    try {
      const setting = await this.getSetting(key);
      
      if (!setting.is_editable) {
        throw new BadRequestError(`Setting '${key}' cannot be deleted`);
      }

      await this.systemSettingModel.delete(setting.id);
      return true;
    } catch (error) {
      logger.error('Error deleting setting:', error);
      throw error;
    }
  }
}

export const systemSettingService = new SystemSettingService();
