{"name": "ikun-erp-backend", "version": "1.0.0", "description": "IKUN ERP Backend API Server", "main": "dist/server.js", "scripts": {"dev": "nodemon -r tsconfig-paths/register src/server.ts", "build": "rimraf dist && tsc", "build:watch": "tsc --watch", "start": "node dist/server.js", "start:prod": "cross-env NODE_ENV=production node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write \"src/**/*.{ts,js,json}\"", "format:check": "prettier --check \"src/**/*.{ts,js,json}\"", "type-check": "tsc --noEmit", "db:migrate": "node dist/database/migrate.js", "db:seed": "node dist/database/seed.js", "db:reset": "npm run db:migrate && npm run db:seed", "clean": "rimraf dist coverage", "prepare": "npm run build"}, "keywords": ["erp", "ecommerce", "api", "nodejs", "typescript", "mysql"], "author": "IKUN ERP Team", "license": "MIT", "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "mysql2": "^3.6.5", "pino": "^8.16.2", "pino-pretty": "^10.2.3", "uuid": "^9.0.1", "winston": "^3.11.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/lodash": "^4.14.202", "@types/morgan": "^1.9.9", "@types/multer": "^1.4.13", "@types/node": "^20.10.4", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "rimraf": "^5.0.5", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}