'use client'

import { useState, useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import { useTheme } from 'next-themes'
import { useAuth } from '@/hooks/useAuth'
import { cn } from '@/lib/utils'
import Link from 'next/link'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Package,
  Store,
  ShoppingCart,
  BarChart3,
  Settings,
  Upload,
  ChevronDown,
  Search,
  Bell,
  User,
  Sun,
  Moon,
  Monitor
} from 'lucide-react'

// 定义导航结构
const navigationConfig = [
  {
    id: 'home',
    label: '首页',
    icon: BarChart3,
    color: 'bg-blue-500',
    directPath: '/', // 直接跳转路径
    categories: []
  },
  {
    id: 'products',
    label: '产品管理',
    icon: Package,
    color: 'bg-blue-500',
    categories: [
      {
        id: 'scraping',
        label: '采集',
        items: [
          { id: 'scraping', label: '采集箱', path: '/scraping' },
        ]
      },
      {
        id: 'catalog',
        label: '目录管理',
        items: [
          { id: 'categories-list', label: '本地目录', path: '/products/catalog' },
          { id: 'mapping-management', label: '映射管理', path: '/products/mapping' },
        ]
      },
      {
        id: 'dropship',
        label: '产品管理',
        items: [
          { id: 'dropship-list', label: '铺货产品', path: '/products/dropship' },
          { id: 'pod-list', label: 'POD产品', path: '/products/pod' },
        ]
      }
    ]
  },
  {
    id: 'upload',
    label: '刊登在线',
    icon: Upload,
    color: 'bg-green-500',
    categories: [
      {
        id: 'wortenmain',
        label: 'Worten',
        items: [
          { id: 'worten', label: 'Worten刊登', path: '/uploadproduct/worten/listing' },
          { id: 'wortenonly', label: '在线同步', path: '/uploadproduct/worten/online' },
        ]
      },
      {
        id: 'phhmain',
        label: 'PHH',
        items: [
          { id: 'phh', label: 'PHH刊登', path: '/uploadproduct/phh/listing' },
          { id: 'phhonly', label: '在线同步', path: '/uploadproduct/phh/online' },
        ]
      }
    ]
  },
  {
    id: 'orders',
    label: '订单管理',
    icon: ShoppingCart,
    color: 'bg-orange-500',
    categories: [
      {
        id: 'order-processing',
        label: '订单处理',
        items: [
          { id: 'orders-list', label: '订单列表', path: '/orders' },
          { id: 'fulfillment', label: '订单履行', path: '/orders/fulfillment' },
        ]
      }
    ]
  },
    {
    id: 'stores',
    label: '店铺管理',
    icon: Store,
    color: 'bg-purple-500',
    categories: [
      {
        id: 'store-config',
        label: '店铺配置',
        items: [
          { id: 'stores-list', label: '店铺列表', path: '/stores' },
          { id: 'platforms', label: '平台配置', path: '/stores/platforms' },
        ]
      }
    ]
  },
  {
    id: 'analytics',
    label: '数据分析',
    icon: BarChart3,
    color: 'bg-indigo-500',
    categories: [
      {
        id: 'reports',
        label: '报表分析',
        items: [
          { id: 'dashboard', label: '数据看板', path: '/dashboard' },
          { id: 'sales', label: '销售分析', path: '/analytics/sales' },
        ]
      }
    ]
  },
  //任务中心
  {
    id: 'tasks',
    label: '任务中心',
    icon: Monitor,
    color: 'bg-pink-500',
    categories: [
      {
        id: 'translation',
        label: '翻译任务',
        items: [
          { id: 'translation-list', label: '翻译列表', path: '/tasks/translation' },
        ]
      },
      {
        id: 'pod',
        label: 'POD任务',
        items: [
          { id: 'pod-list', label: 'POD列表', path: '/tasks/pod' },
        ]
      },
      {
        id: 'scheduled',
        label: '定时任务',
        items: [
          { id: 'scheduled-list', label: '定时列表', path: '/tasks/scheduled' },
        ]
      }
    ]
  },
  //系统设置
  {
    id: 'system',
    label: '系统设置',
    icon: Settings,
    color: 'bg-gray-500',
    categories: [
      {
        id: 'settings',
        label: '系统配置',
        items: [
          { id: 'users', label: '用户管理', path: '/system/users' },
          { id: 'settings', label: '系统设置', path: '/system/settings' },
        ]
      }
    ]
  }
]

interface TopNavigationProps {
  className?: string
}

export function TopNavigation({ className }: TopNavigationProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { theme, setTheme } = useTheme()
  const { user, logout } = useAuth()
  const [activeTab, setActiveTab] = useState('home')
  const [hoveredTab, setHoveredTab] = useState<string | null>(null)
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)

  // 监听路径变化，更新活跃的导航项
  useEffect(() => {
    // 根据当前路径确定活跃的导航项
    if (pathname === '/') {
      setActiveTab('home')
    } else if (pathname.startsWith('/products')) {
      setActiveTab('products')
    } else if (pathname.startsWith('/uploadproduct')) {
      setActiveTab('upload')
    } else if (pathname.startsWith('/stores')) {
      setActiveTab('stores')
    } else if (pathname.startsWith('/orders')) {
      setActiveTab('orders')
    } else if (pathname.startsWith('/analytics') || pathname.startsWith('/dashboard')) {
      setActiveTab('analytics')
    } else if (pathname.startsWith('/tasks')) {
      setActiveTab('tasks')
    } else if (pathname.startsWith('/system')) {
      setActiveTab('system')
    }
  }, [pathname])



  // 处理退出登录
  const handleLogout = async () => {
    try {
      await logout()
      router.push('/login')
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  return (
    <div className={cn("border-b bg-background", className)}>
      {/* 顶部主导航 */}
      <div className="flex items-center justify-between px-4 md:px-6 py-3 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
        {/* Logo和主导航 */}
        <div className="flex items-center space-x-4 md:space-x-8">
          <div className="flex items-center space-x-3">
            <div className="w-9 h-9 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center shadow-sm">
              <span className="text-white font-bold text-sm">IK</span>
            </div>
            <span className="font-bold text-lg md:text-xl text-gray-900 dark:text-white">IKUN ERP</span>
          </div>

          {/* 移动端菜单按钮 */}
          <button
            className="md:hidden p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
            onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
          >
            <Package className="w-5 h-5" />
          </button>

          {/* 主导航标签 */}
          <nav className="hidden md:flex items-center space-x-2">
            {navigationConfig.map((nav) => {
              const Icon = nav.icon
              const isActive = activeTab === nav.id
              const isHovered = hoveredTab === nav.id
              return (
                <div
                  key={nav.id}
                  className="relative"
                  onMouseEnter={() => {
                    if (nav.categories.length > 0) {
                      setHoveredTab(nav.id)
                    }
                  }}
                  onMouseLeave={() => setHoveredTab(null)}
                >
                  <button
                    onClick={() => {
                      if (nav.directPath) {
                        setHoveredTab(null)
                        router.push(nav.directPath)
                      }
                    }}
                    className={cn(
                      "flex items-center space-x-2 px-3 md:px-4 py-2 rounded-lg transition-all duration-200 font-medium",
                      isActive
                        ? "bg-blue-600 text-white shadow-sm"
                        : "text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white hover:bg-gray-100 dark:hover:bg-gray-800"
                    )}
                  >
                    <Icon className="w-4 h-4" />
                    <span className="text-sm">{nav.label}</span>
                  </button>

                  {/* 悬停下拉菜单 - 左侧分类右侧功能布局 */}
                  {isHovered && nav.categories.length > 0 && (
                    <div className="absolute top-full left-0 mt-0 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 shadow-lg z-50">
                      <div className="p-4 min-w-[300px] md:min-w-[450px]">
                        <div className="space-y-5">
                          {nav.categories.map((category) => (
                            <div key={category.id} className="flex items-start">
                              <div className="w-16 md:w-20 flex-shrink-0">
                                <div className="text-sm font-semibold text-gray-900 dark:text-gray-100 py-1">
                                  {category.label}
                                </div>
                              </div>
                              <div className="flex-1 grid grid-cols-2 md:grid-cols-3 gap-x-4 md:gap-x-6 gap-y-2">
                                {category.items.map((item) => (
                                  <Link
                                    key={item.id}
                                    href={item.path}
                                    onClick={() => setHoveredTab(null)}
                                    className="text-left text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 transition-colors whitespace-nowrap py-1 h-7 flex items-center"
                                  >
                                    {item.label}
                                  </Link>
                                ))}
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </nav>
        </div>

        {/* 右侧操作区 */}
        <div className="flex items-center space-x-2">
          {/* 通知按钮 */}
          <button className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
            <Bell className="w-4 h-4" />
          </button>

          {/* 主题切换按钮 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className="p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
                {theme === 'dark' ? (
                  <Moon className="w-4 h-4" />
                ) : theme === 'light' ? (
                  <Sun className="w-4 h-4" />
                ) : (
                  <Monitor className="w-4 h-4" />
                )}
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-32">
              <DropdownMenuItem onClick={() => setTheme('light')} className="flex items-center gap-2">
                <Sun className="w-4 h-4" />
                浅色模式
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme('dark')} className="flex items-center gap-2">
                <Moon className="w-4 h-4" />
                深色模式
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme('system')} className="flex items-center gap-2">
                <Monitor className="w-4 h-4" />
                跟随系统
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* 用户菜单 */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className="flex items-center space-x-2 p-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors">
                <User className="w-4 h-4" />
                <span className="text-sm hidden md:inline">{user?.username || '用户'}</span>
                <ChevronDown className="w-3 h-3" />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuItem onClick={() => router.push('/profile')}>
                个人设置
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleLogout} className="text-red-600 dark:text-red-400">
                退出登录
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>





      {/* 移动端导航菜单 */}
      {mobileMenuOpen && (
        <div className="md:hidden bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
          <div className="px-4 py-3 space-y-3">
            {navigationConfig.map((nav) => {
              const Icon = nav.icon
              return (
                <div key={nav.id}>
                  {nav.directPath ? (
                    <button
                      onClick={() => {
                        setMobileMenuOpen(false)
                        router.push(nav.directPath)
                      }}
                      className="flex items-center space-x-3 w-full px-3 py-2 text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors"
                    >
                      <Icon className="w-5 h-5" />
                      <span className="font-medium">{nav.label}</span>
                    </button>
                  ) : (
                    <div>
                      <div className="flex items-center space-x-3 px-3 py-2 text-gray-900 dark:text-gray-100 font-semibold">
                        <Icon className="w-5 h-5" />
                        <span>{nav.label}</span>
                      </div>
                      <div className="ml-8 space-y-1">
                        {nav.categories.map((category) => (
                          <div key={category.id}>
                            <div className="px-3 py-1 text-sm font-medium text-gray-600 dark:text-gray-400">
                              {category.label}
                            </div>
                            <div className="space-y-1">
                              {category.items.map((item) => (
                                <Link
                                  key={item.id}
                                  href={item.path}
                                  onClick={() => setMobileMenuOpen(false)}
                                  className="block w-full text-left px-3 py-1 text-sm text-blue-600 dark:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded transition-colors"
                                >
                                  {item.label}
                                </Link>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>
        </div>
      )}
    </div>
  )
}
