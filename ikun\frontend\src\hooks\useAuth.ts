/**
 * 认证Hook
 */

import { useState, useEffect, useCallback } from 'react';
import { authApi } from '@/lib/auth-api';
import { User, LoginRequest, AuthState } from '@/types/auth';

const STORAGE_KEYS = {
  TOKEN: 'auth_token',
  REFRESH_TOKEN: 'auth_refresh_token',
  USER: 'auth_user'
};

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    token: null,
    refreshToken: null,
    isAuthenticated: false,
    isLoading: true
  });

  // 清除认证状态
  const clearAuthState = useCallback(() => {
    if (typeof window === 'undefined') return;

    localStorage.removeItem(STORAGE_KEYS.TOKEN);
    localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN);
    localStorage.removeItem(STORAGE_KEYS.USER);
  }, []);

  // 保存认证状态到localStorage
  const saveAuthState = useCallback((user: User, token: string, refreshToken: string) => {
    if (typeof window === 'undefined') return;

    localStorage.setItem(STORAGE_KEYS.TOKEN, token);
    localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, refreshToken);
    localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
  }, []);

  // 从localStorage加载认证状态
  const loadAuthState = useCallback(async () => {
    if (typeof window === 'undefined') return;

    try {
      const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
      const refreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN);
      const userStr = localStorage.getItem(STORAGE_KEYS.USER);

      if (token && refreshToken && userStr) {
        const user = JSON.parse(userStr) as User;

        // 验证token有效性
        try {
          // 调用后端验证token
          await authApi.getProfile();

          // Token有效，设置认证状态
          setAuthState({
            user,
            token,
            refreshToken,
            isAuthenticated: true,
            isLoading: false
          });
        } catch (error) {
          console.warn('Token validation failed, clearing auth state:', error);
          // Token无效，清除认证状态
          clearAuthState();
          setAuthState({
            user: null,
            token: null,
            refreshToken: null,
            isAuthenticated: false,
            isLoading: false
          });
        }
      } else {
        setAuthState(prev => ({ ...prev, isLoading: false }));
      }
    } catch (error) {
      console.error('Failed to load auth state:', error);
      // 发生错误时也清除认证状态
      clearAuthState();
      setAuthState({
        user: null,
        token: null,
        refreshToken: null,
        isAuthenticated: false,
        isLoading: false
      });
    }
  }, [clearAuthState]);

  // 登录
  const login = useCallback(async (credentials: LoginRequest) => {
    try {
      setAuthState(prev => ({ ...prev, isLoading: true }));

      const response = await authApi.login(credentials);
      const { user, token, refreshToken } = response;

      if (!user || !token || !refreshToken) {
        console.error('Missing required fields in response:', { user: !!user, token: !!token, refreshToken: !!refreshToken });
        throw new Error('Invalid response format');
      }

      saveAuthState(user, token, refreshToken);

      setAuthState({
        user,
        token,
        refreshToken,
        isAuthenticated: true,
        isLoading: false
      });
      return { success: true };
    } catch (error) {
      console.error('Login error:', error);
      setAuthState(prev => ({ ...prev, isLoading: false }));
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Login failed'
      };
    }
  }, [saveAuthState]);

  // 登出
  const logout = useCallback(async () => {
    try {
      await authApi.logout();
    } catch (error) {
      console.error('Logout API call failed:', error);
    } finally {
      clearAuthState();
      setAuthState({
        user: null,
        token: null,
        refreshToken: null,
        isAuthenticated: false,
        isLoading: false
      });
    }
  }, [clearAuthState]);

  // 刷新token
  const refreshToken = useCallback(async () => {
    if (!authState.refreshToken) return false;

    try {
      const response = await authApi.refreshToken({ 
        refreshToken: authState.refreshToken 
      });
      
      const newToken = response.token;
      
      if (typeof window !== 'undefined') {
        localStorage.setItem(STORAGE_KEYS.TOKEN, newToken);
      }
      
      setAuthState(prev => ({
        ...prev,
        token: newToken
      }));
      
      return true;
    } catch (error) {
      console.error('Token refresh failed:', error);
      await logout();
      return false;
    }
  }, [authState.refreshToken, logout]);

  // 获取用户信息
  const fetchProfile = useCallback(async () => {
    if (!authState.isAuthenticated) return;

    try {
      const user = await authApi.getProfile();
      
      if (typeof window !== 'undefined') {
        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user));
      }
      
      setAuthState(prev => ({
        ...prev,
        user
      }));
    } catch (error) {
      console.error('Failed to fetch profile:', error);
      // 如果获取用户信息失败，可能token已过期
      await logout();
    }
  }, [authState.isAuthenticated, logout]);

  // 初始化时加载认证状态
  useEffect(() => {
    loadAuthState().catch(error => {
      console.error('Failed to initialize auth state:', error);
    });
  }, [loadAuthState]);

  return {
    ...authState,
    login,
    logout,
    refreshToken,
    fetchProfile
  };
}
