/**
 * Upload Product Service
 * Business logic for product listing management
 */

import { uploadproductModel } from '@/models/uploadproduct/uploadproductModel';
import { logger } from '@/utils/logger';
import { BadRequestError, NotFoundError } from '@/middleware/error/errorHandler';

/**
 * Helper function to safely parse JSON fields
 */
const safeJsonParse = (value: any, defaultValue: any = {}) => {
  if (!value) return defaultValue;

  // If it's already an object, return it directly
  if (typeof value === 'object') return value;

  // If it's a string, try to parse it
  if (typeof value === 'string') {
    try {
      return JSON.parse(value);
    } catch (error) {
      logger.warn(`Failed to parse JSON field: ${value}`, error);
      return defaultValue;
    }
  }

  return defaultValue;
};

interface ListingQuery {
  platform?: string;
  page?: string;
  limit?: string;
  search?: string;
  status?: string;
  translation_status?: string;
  store_id?: string;
}

interface CategoryQuery {
  platform_code?: string;
  parent_category_id?: string;
  is_leaf?: string;
}

interface AttributeQuery {
  platform_code?: string;
  category_id?: string;
  translation_status?: string;
}

class UploadProductService {
  // Get product listings with pagination and filters
  public async getListings(query: ListingQuery) {
    try {
      const page = parseInt(query.page || '1');
      const limit = parseInt(query.limit || '20');
      const offset = (page - 1) * limit;

      // Build where conditions with table aliases
      const conditions: string[] = [];
      const params: any[] = [];

      if (query.platform) {
        conditions.push('l.platform_code = ?');
        params.push(query.platform);
      }

      if (query.status) {
        conditions.push('l.status = ?');
        params.push(query.status);
      }

      if (query.translation_status) {
        conditions.push('l.listings_translation_status = ?');
        params.push(query.translation_status);
      }

      if (query.store_id) {
        conditions.push('l.store_id = ?');
        params.push(parseInt(query.store_id));
      }

      if (query.search) {
        conditions.push('(l.sku LIKE ? OR l.ean LIKE ? OR p.english_title LIKE ?)');
        const searchTerm = `%${query.search}%`;
        params.push(searchTerm, searchTerm, searchTerm);
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      // Get total count with JOIN
      const countQuery = `
        SELECT COUNT(*) as total
        FROM uploadproduct_listings l
        LEFT JOIN product_dropship p ON l.dropship_product_id = p.id
        ${whereClause}
      `;
      
      const countResult = await uploadproductModel.query(countQuery, params);
      const total = countResult[0]?.total || 0;

      // Get listings with product information
      const listingsQuery = `
        SELECT
          l.id,
          l.dropship_product_id,
          l.store_id,
          l.platform_code,
          l.sku,
          l.ean,
          l.upstores_sku,
          l.upstores_ean,
          l.multi_titles,
          l.multi_descriptions,
          l.multi_selling_point,
          l.platform_category_id,
          l.platform_attributes,
          l.platform_data,
          l.discounted_price,
          l.original_price,
          l.discount_end_date,
          l.discount_start_date,
          l.stock_quantity,
          l.discount,
          l.currency,
          l.status,
          l.listing_id,
          l.listings_translation_status as translation_status,
          l.attributes_status,
          l.error_message,
          l.last_sync_at,
          l.created_at,
          l.uplisting_at,

          /* 从product_dropship表获取基本信息（优先使用product_dropship表的数据） */
          COALESCE(p.english_title, l.english_title) as english_title,
          p.english_description,
          p.selling_point,
          COALESCE(p.image1, l.image1) as image1,
          p.image2,
          p.image3,
          p.image4,
          p.image5,
          p.category,
          p.cost_price,
          p.package_weight,
          p.package_length,
          p.package_width,
          p.package_height,
          p.purchase_link,
          p.remarks
        FROM uploadproduct_listings l
        LEFT JOIN product_dropship p ON l.dropship_product_id = p.id
        ${whereClause}
        ORDER BY l.uplisting_at DESC
        LIMIT ${Number(limit)} OFFSET ${Number(offset)}
      `;

      const listings = await uploadproductModel.query(listingsQuery, params);

      // Parse JSON fields safely and handle field name mapping
      const processedListings = listings.map((listing: any) => {
        const processed = {
          ...listing,
          multi_titles: safeJsonParse(listing.multi_titles, {}),
          multi_descriptions: safeJsonParse(listing.multi_descriptions, {}),
          multi_selling_points: safeJsonParse(listing.multi_selling_point, {}), // 字段名映射
          platform_attributes: safeJsonParse(listing.platform_attributes, {}),
          platform_data: safeJsonParse(listing.platform_data, {}),
          image1: listing.image1 || null,

          // 解析产品基本信息中的JSON字段
          selling_point: typeof listing.selling_point === 'string'
            ? safeJsonParse(listing.selling_point, [])
            : (listing.selling_point || [])
        };

        // 删除原始的数据库字段名
        delete processed.multi_selling_point;

        return processed;
      });

      const totalPages = Math.ceil(total / limit);

      return {
        items: processedListings,
        pagination: {
          page,
          limit,
          total,
          totalPages
        }
      };
    } catch (error) {
      logger.error('Error getting listings:', error);
      throw error;
    }
  }

  // Create new product listing
  public async createListing(data: any) {
    try {
      // Validate required fields
      if (!data.sku || !data.ean || !data.english_title || !data.platform_code) {
        throw new BadRequestError('SKU, EAN, English title, and platform code are required');
      }

      const listing = await uploadproductModel.createListing(data);
      
      logger.info('Product listing created:', {
        listingId: listing.id,
        sku: listing.sku,
        platform: listing.platform_code
      });

      return listing;
    } catch (error) {
      logger.error('Error creating listing:', error);
      throw error;
    }
  }

  // Get listing by ID
  public async getListingById(id: number) {
    try {
      const listing = await uploadproductModel.getListingById(id);
      
      if (!listing) {
        throw new NotFoundError(`Product listing with ID ${id} not found`);
      }

      // Parse JSON fields safely
      return {
        ...listing,
        multi_titles: safeJsonParse(listing.multi_titles, {}),
        multi_descriptions: safeJsonParse(listing.multi_descriptions, {}),
        platform_attributes: safeJsonParse(listing.platform_attributes, {}),
        image1: listing.image1 || null
      };
    } catch (error) {
      logger.error('Error getting listing by ID:', error);
      throw error;
    }
  }

  // Update listing
  public async updateListing(id: number, data: any) {
    try {
      const existingListing = await uploadproductModel.getListingById(id);

      if (!existingListing) {
        throw new NotFoundError(`Product listing with ID ${id} not found`);
      }

      // 分离数据：基本产品信息 vs 上架信息
      const productFields = ['english_title', 'english_description', 'selling_point', 'image1', 'image2', 'image3', 'image4', 'image5', 'ean', 'category', 'cost_price', 'package_weight', 'package_length', 'package_width', 'package_height', 'purchase_link', 'remarks'];
      const listingFields = ['multi_titles', 'multi_descriptions', 'multi_selling_points', 'platform_category_id', 'platform_attributes', 'platform_data', 'upstores_sku', 'upstores_ean', 'discounted_price', 'original_price', 'stock_quantity', 'discount', 'discount_start_date', 'discount_end_date', 'currency', 'status', 'translation_status', 'listings_translation_status', 'attributes_status', 'listing_id', 'error_message'];

      const productData: any = {};
      const listingData: any = {};

      // 分离数据到对应的表
      Object.keys(data).forEach(key => {
        if (productFields.includes(key)) {
          productData[key] = data[key];
        } else if (listingFields.includes(key)) {
          listingData[key] = data[key];
        }
      });

      // 如果有产品基本信息需要更新，更新 product_dropship 表
      if (Object.keys(productData).length > 0 && existingListing.dropship_product_id) {
        logger.info('Updating product_dropship data:', {
          productId: existingListing.dropship_product_id,
          fields: Object.keys(productData)
        });

        // 调用产品服务更新基本信息
        const { productService } = await import('@/services/products/productService');
        await productService.updateProduct(existingListing.dropship_product_id, productData);

        // 同步更新 uploadproduct_listings 表中的冗余字段
        const redundantFields: any = {};
        if (productData.sku) redundantFields.sku = productData.sku;
        if (productData.ean) redundantFields.ean = productData.ean;
        if (productData.english_title) redundantFields.english_title = productData.english_title;
        if (productData.image1) redundantFields.image1 = productData.image1;

        if (Object.keys(redundantFields).length > 0) {
          logger.info('Syncing redundant fields in uploadproduct_listings:', {
            listingId: id,
            fields: Object.keys(redundantFields)
          });
          Object.assign(listingData, redundantFields);
        }
      }

      // 更新上架信息
      const updatedListing = await uploadproductModel.updateListing(id, listingData);

      logger.info('Product listing updated:', {
        listingId: id,
        sku: updatedListing.sku,
        updatedProductFields: Object.keys(productData),
        updatedListingFields: Object.keys(listingData)
      });

      return updatedListing;
    } catch (error) {
      logger.error('Error updating listing:', error);
      throw error;
    }
  }

  // Delete listing
  public async deleteListing(id: number) {
    try {
      const existingListing = await uploadproductModel.getListingById(id);
      
      if (!existingListing) {
        throw new NotFoundError(`Product listing with ID ${id} not found`);
      }

      await uploadproductModel.deleteListing(id);
      
      logger.info('Product listing deleted:', { listingId: id });
    } catch (error) {
      logger.error('Error deleting listing:', error);
      throw error;
    }
  }

  // Get categories
  public async getCategories(query: CategoryQuery) {
    try {
      return await uploadproductModel.getCategories(query);
    } catch (error) {
      logger.error('Error getting categories:', error);
      throw error;
    }
  }

  // Get attributes
  public async getAttributes(query: AttributeQuery) {
    try {
      return await uploadproductModel.getAttributes(query);
    } catch (error) {
      logger.error('Error getting attributes:', error);
      throw error;
    }
  }
}

export const uploadproductService = new UploadProductService();
