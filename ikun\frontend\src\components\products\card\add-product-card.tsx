'use client'

import { useState } from 'react'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { CategorySelector } from '../category-selector'
import {
  Plus,
  Save,
  RotateCcw,
  Image as ImageIcon,
  Package,
  Tag,
  FileText
} from 'lucide-react'

// 铺货产品数据结构定义（从主文件复制）
interface DropshipProduct {
  id: number // 全局ID，从10000001开始
  source: string // 来源：excel导入/手动添加/采集自平台
  sku: string // *产品SKU，唯一，创建后不可修改（必填）
  ean: string // *产品EAN，国际产品代码（必填）
  category: string // *产品类目（英文，三级类目）（必填）
  englishTitle: string // *英文标题（必填）
  englishDescription: string // *英文描述（必填）
  image1: string // *主图（必须）（必填）
  image2?: string // 图片2
  image3?: string // 图片3
  image4?: string // 图片4
  image5?: string // 图片5
  costPrice: number // 成本价（人民币）
  packageWeight: number // 包装重量（g）
  packageLength: number // 包装长度（cm）
  packageWidth: number // 包装宽度（cm）
  packageHeight: number // 包装高度（cm）
  purchaseLink?: string // 采购链接
  createdAt: string // 创建时间
  updatedAt: string // 修改时间
  claimTime?: string // 认领时间
  claimPlatform?: string // 认领平台
  listingCount: number // 刊登次数
  remarks?: string // 备注
  status: 'active' | 'inactive' | 'draft' // 状态：活跃、禁用、草稿
}

interface AddProductCardProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSave: (createData: any) => void
}

export function AddProductCard({ open, onOpenChange, onSave }: AddProductCardProps) {
  const [formData, setFormData] = useState({
    source: '手动添加',
    sku: '',
    ean: '',
    category: '', // 存储类目路径，如 "clothing/mens/tshirts"
    categoryDisplayPath: '', // 存储显示路径，如 "服装（Clothing）-男装（Mens）-T恤（Tshirts）"
    englishTitle: '',
    sellingPoints: ['', '', '', '', ''], // 5个英文卖点
    englishDescription: '',
    image1: '',
    image2: '',
    image3: '',
    image4: '',
    image5: '',
    costPrice: 0,
    packageWeight: 0,
    packageLength: 0,
    packageWidth: 0,
    packageHeight: 0,
    purchaseLink: '',
    remarks: '',
    status: 'draft' as const
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  // 验证必填字段
  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.sku.trim()) newErrors.sku = 'SKU为必填项'
    if (!formData.ean.trim()) newErrors.ean = 'EAN为必填项'
    if (!formData.category.trim()) newErrors.category = '产品类目为必填项'
    if (!formData.englishTitle.trim()) newErrors.englishTitle = '英文标题为必填项'
    if (!formData.englishDescription.trim()) newErrors.englishDescription = '英文描述为必填项'
    if (!formData.image1.trim()) newErrors.image1 = '主图为必填项'
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = () => {
    if (validateForm()) {
      // 转换字段名以匹配后端API期望的格式
      const createData = {
        source: String(formData.source),
        sku: String(formData.sku),
        ean: String(formData.ean),
        category: String(formData.category),
        english_title: String(formData.englishTitle),
        selling_point: formData.sellingPoints.filter(point => point.trim() !== ''), // 过滤空卖点
        english_description: String(formData.englishDescription),
        image1: String(formData.image1),
        image2: String(formData.image2 || ''),
        image3: String(formData.image3 || ''),
        image4: String(formData.image4 || ''),
        image5: String(formData.image5 || ''),
        cost_price: Number(formData.costPrice) || 0,
        package_weight: Number(formData.packageWeight) || 0,
        package_length: Number(formData.packageLength) || 0,
        package_width: Number(formData.packageWidth) || 0,
        package_height: Number(formData.packageHeight) || 0,
        purchase_link: String(formData.purchaseLink || ''),
        remarks: String(formData.remarks || ''),
        status: formData.status
      }

      onSave(createData)
      handleReset()
      onOpenChange(false)
    }
  }

  const handleReset = () => {
    setFormData({
      source: '手动添加',
      sku: '',
      ean: '',
      category: '',
      categoryDisplayPath: '',
      englishTitle: '',
      sellingPoints: ['', '', '', '', ''],
      englishDescription: '',
      image1: '',
      image2: '',
      image3: '',
      image4: '',
      image5: '',
      costPrice: 0,
      packageWeight: 0,
      packageLength: 0,
      packageWidth: 0,
      packageHeight: 0,
      purchaseLink: '',
      remarks: '',
      status: 'draft'
    })
    setErrors({})
  }

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // 清除该字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  // 处理类目选择
  const handleCategoryChange = (categoryPath: string, displayPath: string) => {
    setFormData(prev => ({
      ...prev,
      category: categoryPath,
      categoryDisplayPath: displayPath
    }))
    // 清除类目字段的错误
    if (errors.category) {
      setErrors(prev => ({ ...prev, category: '' }))
    }
  }

  // 处理卖点更新
  const updateSellingPoint = (index: number, value: string) => {
    const newSellingPoints = [...formData.sellingPoints]
    newSellingPoints[index] = value
    setFormData(prev => ({ ...prev, sellingPoints: newSellingPoints }))
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Plus className="w-5 h-5" />
            手动添加铺货产品
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="basic" className="w-full flex flex-col flex-1 min-h-0">
          <TabsList className="grid w-full grid-cols-4 flex-shrink-0">
            <TabsTrigger value="basic" className="flex items-center gap-1">
              <Tag className="w-4 h-4" />
              基本信息
            </TabsTrigger>
            <TabsTrigger value="images" className="flex items-center gap-1">
              <ImageIcon className="w-4 h-4" />
              产品图片
            </TabsTrigger>
            <TabsTrigger value="package" className="flex items-center gap-1">
              <Package className="w-4 h-4" />
              包装信息
            </TabsTrigger>
            <TabsTrigger value="other" className="flex items-center gap-1">
              <FileText className="w-4 h-4" />
              其他信息
            </TabsTrigger>
          </TabsList>

          {/* 基本信息标签页 */}
          <TabsContent value="basic" className="flex-1 overflow-y-auto min-h-[400px]">
            <Card className="h-full">
              <CardContent className="space-y-4 pt-6 h-full overflow-y-auto">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="sku">产品SKU *</Label>
                    <Input
                      id="sku"
                      value={formData.sku}
                      onChange={(e) => updateFormData('sku', e.target.value)}
                      placeholder="如：IK-YF-001"
                      className={errors.sku ? 'border-red-500' : ''}
                    />
                    <div className="h-5">
                      {errors.sku && <p className="text-sm text-red-500">{errors.sku}</p>}
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="ean">产品EAN *</Label>
                    <Input
                      id="ean"
                      value={formData.ean}
                      onChange={(e) => updateFormData('ean', e.target.value)}
                      placeholder="13位国际产品代码"
                      className={errors.ean ? 'border-red-500' : ''}
                    />
                    <div className="h-5">
                      {errors.ean && <p className="text-sm text-red-500">{errors.ean}</p>}
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">产品类目 *</Label>
                  <CategorySelector
                    value={formData.category}
                    onValueChange={handleCategoryChange}
                    placeholder="选择产品类目"
                    error={!!errors.category}
                  />
                  <div className="h-5">
                    {errors.category && <p className="text-sm text-red-500">{errors.category}</p>}
                    {formData.categoryDisplayPath && !errors.category && (
                      <p className="text-sm text-muted-foreground">
                      </p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="englishTitle">英文标题 *</Label>
                  <Input
                    id="englishTitle"
                    value={formData.englishTitle}
                    onChange={(e) => updateFormData('englishTitle', e.target.value)}
                    placeholder="产品英文标题"
                    className={errors.englishTitle ? 'border-red-500' : ''}
                  />
                  <div className="h-5">
                    {errors.englishTitle && <p className="text-sm text-red-500">{errors.englishTitle}</p>}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="englishDescription">英文描述 *</Label>
                  <Textarea
                    id="englishDescription"
                    value={formData.englishDescription}
                    onChange={(e) => updateFormData('englishDescription', e.target.value)}
                    placeholder="产品英文描述"
                    rows={4}
                    className={errors.englishDescription ? 'border-red-500' : ''}
                  />
                  <div className="h-5">
                    {errors.englishDescription && <p className="text-sm text-red-500">{errors.englishDescription}</p>}
                  </div>
                </div>

                {/* 英文卖点 */}
                <div className="space-y-3">
                  <Label>五点描述</Label>
                  <div className="space-y-2">
                    {[0, 1, 2, 3, 4].map((index) => (
                      <div key={index} className="space-y-1">
                        <Label htmlFor={`selling_point_${index}`} className="text-sm text-muted-foreground">
                          卖点 {index + 1}
                        </Label>
                        <Input
                          id={`selling_point_${index}`}
                          value={formData.sellingPoints[index]}
                          onChange={(e) => updateSellingPoint(index, e.target.value)}
                        />
                      </div>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="costPrice">成本价（¥）</Label>
                    <Input
                      id="costPrice"
                      type="number"
                      step="0.01"
                      value={formData.costPrice}
                      onChange={(e) => updateFormData('costPrice', parseFloat(e.target.value) || 0)}
                      placeholder="0.00"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="status">产品状态</Label>
                    <Select value={formData.status} onValueChange={(value) => updateFormData('status', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="draft">草稿</SelectItem>
                        <SelectItem value="active">活跃</SelectItem>
                        <SelectItem value="inactive">禁用</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 产品图片标签页 */}
          <TabsContent value="images" className="flex-1 overflow-y-auto min-h-[400px]">
            <Card className="h-full">
              <CardContent className="space-y-4 pt-6 h-full overflow-y-auto">
                <div className="space-y-2">
                  <Label htmlFor="image1">主图 *</Label>
                  <Input
                    id="image1"
                    value={formData.image1}
                    onChange={(e) => updateFormData('image1', e.target.value)}
                    placeholder="主图URL或路径"
                    className={errors.image1 ? 'border-red-500' : ''}
                  />
                  <div className="h-5">
                    {errors.image1 && <p className="text-sm text-red-500">{errors.image1}</p>}
                  </div>
                </div>

                {[2, 3, 4, 5].map((num) => (
                  <div key={num} className="space-y-2">
                    <Label htmlFor={`image${num}`}>图片{num}</Label>
                    <Input
                      id={`image${num}`}
                      value={formData[`image${num}` as keyof typeof formData] as string}
                      onChange={(e) => updateFormData(`image${num}`, e.target.value)}
                      placeholder={`图片${num} URL或路径（可选）`}
                    />
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          {/* 包装信息标签页 */}
          <TabsContent value="package" className="flex-1 overflow-y-auto min-h-[400px]">
            <Card className="h-full">
              <CardContent className="space-y-4 pt-6 h-full overflow-y-auto">
                <div className="space-y-2">
                  <Label htmlFor="packageWeight">包装重量（g）</Label>
                  <Input
                    id="packageWeight"
                    type="number"
                    value={formData.packageWeight}
                    onChange={(e) => updateFormData('packageWeight', parseInt(e.target.value) || 0)}
                    placeholder="0"
                  />
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="packageLength">长度（cm）</Label>
                    <Input
                      id="packageLength"
                      type="number"
                      value={formData.packageLength}
                      onChange={(e) => updateFormData('packageLength', parseInt(e.target.value) || 0)}
                      placeholder="0"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="packageWidth">宽度（cm）</Label>
                    <Input
                      id="packageWidth"
                      type="number"
                      value={formData.packageWidth}
                      onChange={(e) => updateFormData('packageWidth', parseInt(e.target.value) || 0)}
                      placeholder="0"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="packageHeight">高度（cm）</Label>
                    <Input
                      id="packageHeight"
                      type="number"
                      value={formData.packageHeight}
                      onChange={(e) => updateFormData('packageHeight', parseInt(e.target.value) || 0)}
                      placeholder="0"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 其他信息标签页 */}
          <TabsContent value="other" className="flex-1 overflow-y-auto min-h-[400px]">
            <Card className="h-full">
              <CardContent className="space-y-4 pt-6 h-full overflow-y-auto">
                <div className="space-y-2">
                  <Label htmlFor="purchaseLink">采购链接</Label>
                  <Input
                    id="purchaseLink"
                    value={formData.purchaseLink}
                    onChange={(e) => updateFormData('purchaseLink', e.target.value)}
                    placeholder="产品采购链接（可选）"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="remarks">备注</Label>
                  <Textarea
                    id="remarks"
                    value={formData.remarks}
                    onChange={(e) => updateFormData('remarks', e.target.value)}
                    placeholder="产品备注信息（可选）"
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 底部操作按钮 */}
        <div className="flex items-center justify-between pt-4 border-t flex-shrink-0">
          <div className="flex items-center gap-2">
            <Badge variant="outline">来源: {formData.source}</Badge>
            <Badge variant={formData.status === 'draft' ? 'secondary' : 'default'}>
              {formData.status === 'draft' ? '草稿' : formData.status === 'active' ? '活跃' : '禁用'}
            </Badge>
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleReset}>
              <RotateCcw className="w-4 h-4 mr-1" />
              重置
            </Button>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button onClick={handleSave}>
              <Save className="w-4 h-4 mr-1" />
              保存
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
