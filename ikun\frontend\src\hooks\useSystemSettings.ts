'use client'

import { useState } from 'react'
import { apiClient } from '@/lib/api'
import { toast } from 'sonner'

// 系统设置类型定义
export interface SystemSetting {
  id: number
  setting_key: string
  setting_value: any
  category: string
  display_name: string
  description?: string
  is_public: boolean
  is_editable: boolean
  validation_rules?: any
  default_value?: any
  sort_order: number
  created_at: string
  updated_at: string
}

export interface TranslationProvider {
  name: string
  baseUrl: string
  apiKey?: string
  model?: string
  timeout: number
  enabled: boolean
  description?: string
  prompts?: {
    title?: string
    description?: string
    selling_point?: string
  }
}

export interface TranslationScenario {
  name: string
  description: string
  provider: string
  fallback_provider?: string
  enabled: boolean
  rules?: Record<string, string>
}

export interface TranslationConfig {
  providers: Record<string, TranslationProvider>
  scenarios: Record<string, TranslationScenario>
  defaults: {
    default_source_lang: string
    default_target_langs: string[]
    auto_translate: boolean
    translation_timeout: number
    max_concurrent_requests: number
    retry_attempts: number
    retry_delay: number
  }
}

export interface SystemBasicConfig {
  company_name: string
  system_title: string
  proxy_host: string
  proxy_port: string
  proxy_username: string
  proxy_password: string
}

export interface TokenModelStats {
  yesterday_input_tokens: number
  yesterday_output_tokens: number
  today_input_tokens: number
  today_output_tokens: number
  total_input_tokens: number
  total_output_tokens: number
}

// Translation Service Config Types
export interface TranslationServiceConfig {
  provider: string
  sub_service: string | null
}

// Translation Scenarios Types
export interface TranslationScenarios {
  [platformCode: string]: {
    platform_name: string
    enabled: boolean
    scenarios: {
      form_editing: TranslationScenario
      batch_translation: TranslationScenario
      translation_task: TranslationScenario
    }
  }
}

export interface TranslationScenario {
  name: string
  description: string
  enabled: boolean
  content_types: {
    title: TranslationServiceConfig
    description: TranslationServiceConfig
    selling_point: TranslationServiceConfig
  }
}

export interface TokenStatistics {
  deepseek_huoshan: {
    models: Record<string, TokenModelStats>
  }
  openai_compatible: {
    models: Record<string, TokenModelStats>
  }
  global_statistics: {
    total_input_tokens: number
    total_output_tokens: number
    total_api_calls: number
  }
}

export function useSystemSettings() {
  const [settings, setSettings] = useState<Record<string, SystemSetting[]>>({})
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // 获取所有设置
  const fetchAllSettings = async () => {
    try {
      setLoading(true)
      setError(null)

      const settingsData = await apiClient.get<Record<string, SystemSetting[]>>('/system/settings')
      console.log('Settings Data:', settingsData) // 调试信息

      // API客户端已经自动提取了data字段，直接使用
      setSettings(settingsData || {})
    } catch (error: any) {

      const errorMessage = error.response?.data?.message || error.message || '获取系统设置失败'
      setError(errorMessage)
      toast.error(errorMessage)
    } finally {
      setLoading(false)
    }
  }

  // 根据分类获取设置
  const fetchSettingsByCategory = async (category: string) => {
    try {
      setLoading(true)
      setError(null)
      
      const data = await apiClient.get<SystemSetting[]>(`/system/settings/category/${category}`)
      return data
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取设置失败'
      setError(errorMessage)
      toast.error(errorMessage)
      return []
    } finally {
      setLoading(false)
    }
  }

  // 获取单个设置  非常重要
  const fetchSetting = async (key: string) => {
    try {
      const data = await apiClient.get<SystemSetting>(`/system/settings/${key}`)
      return data
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取设置失败'
      toast.error(errorMessage)
      throw error
    }
  }

  // 更新单个设置  非常重要
  const updateSetting = async (key: string, value: any) => {
    try {
      const data = await apiClient.put<SystemSetting>(`/system/settings/${key}`, { value })
      toast.success('设置更新成功')
      return data
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '更新设置失败'
      toast.error(errorMessage)
      throw error
    }
  }

  // 批量更新设置
  const batchUpdateSettings = async (updates: Array<{key: string, value: any}>) => {
    try {
      await apiClient.put('/system/settings/batch', { settings: updates })
      toast.success('设置批量更新成功')
      return true
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '批量更新设置失败'
      toast.error(errorMessage)
      throw error
    }
  }

  // 重置设置为默认值
  const resetToDefault = async (key: string) => {
    try {
      const data = await apiClient.post<SystemSetting>(`/system/settings/${key}/reset`)
      toast.success('设置已重置为默认值')
      return data
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '重置设置失败'
      toast.error(errorMessage)
      throw error
    }
  }

  // 获取翻译配置
  const fetchTranslationConfig = async (): Promise<TranslationConfig> => {
    try {
      const data = await apiClient.get<TranslationConfig>('/system/settings/translation/config')
      return data
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取翻译配置失败'
      toast.error(errorMessage)
      throw error
    }
  }

  // 更新翻译配置
  const updateTranslationConfig = async (config: Partial<TranslationConfig>) => {
    try {
      await apiClient.put('/system/settings/translation/config', config)
      toast.success('翻译配置更新成功')
      return true
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '更新翻译配置失败'
      toast.error(errorMessage)
      throw error
    }
  }

  // 获取系统基础配置
  const fetchSystemBasicConfig = async (): Promise<SystemBasicConfig> => {
    try {
      const data = await apiClient.get<SystemBasicConfig>('/system/settings/basic')
      return data
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取系统配置失败'
      toast.error(errorMessage)
      throw error
    }
  }

  // 更新系统基础配置
  const updateSystemBasicConfig = async (config: Partial<SystemBasicConfig>) => {
    try {
      await apiClient.put('/system/settings/basic', config)
      toast.success('系统配置更新成功')
      return true
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '更新系统配置失败'
      toast.error(errorMessage)
      throw error
    }
  }

  // 获取公开设置
  const fetchPublicSettings = async () => {
    try {
      const data = await apiClient.get<Record<string, any>>('/system/settings/public')
      return data
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取公开设置失败'
      toast.error(errorMessage)
      throw error
    }
  }

  // 获取所有分类
  const fetchCategories = async () => {
    try {
      const data = await apiClient.get<string[]>('/system/settings/categories')
      return data
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取分类失败'
      toast.error(errorMessage)
      throw error
    }
  }

  // 获取Token统计数据
  const fetchTokenStatistics = async (): Promise<TokenStatistics> => {
    try {
      const data = await apiClient.get<TokenStatistics>('/system/settings/token-statistics')
      return data
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '获取Token统计失败'
      toast.error(errorMessage)
      throw error
    }
  }

  // 更新Token统计数据
  const updateTokenStatistics = async (statistics: TokenStatistics) => {
    try {
      await apiClient.put('/system/settings/token-statistics', statistics)
      toast.success('Token统计更新成功')
      return true
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '更新Token统计失败'
      toast.error(errorMessage)
      throw error
    }
  }

  // 重置今日Token统计
  const resetDailyTokenStats = async () => {
    try {
      await apiClient.post('/system/settings/token-statistics/reset-daily')
      toast.success('今日Token统计已重置')
      return true
    } catch (error: any) {
      const errorMessage = error.response?.data?.message || '重置Token统计失败'
      toast.error(errorMessage)
      throw error
    }
  }

  // 注释：移除自动获取所有设置，改为按需调用

  return {
    settings,
    loading,
    error,
    fetchAllSettings,
    fetchSettingsByCategory,
    fetchSetting,
    updateSetting,
    batchUpdateSettings,
    resetToDefault,
    fetchTranslationConfig,
    updateTranslationConfig,
    fetchSystemBasicConfig,
    updateSystemBasicConfig,
    fetchPublicSettings,
    fetchCategories,
    fetchTokenStatistics,
    updateTokenStatistics,
    resetDailyTokenStats
  }
}
