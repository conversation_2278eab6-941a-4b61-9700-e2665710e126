# ===================================
# Node.js & NPM
# ===================================
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# ===================================
# Environment Variables
# ===================================
.env
.env.*
!.env.example
.env.local
.env.development.local
.env.test.local
.env.production.local

# ===================================
# Build Output
# ===================================
dist/
build/
out/
.output/
.vercel/
.netlify/

# ===================================
# Logs
# ===================================
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# ===================================
# Runtime Data
# ===================================
pids/
*.pid
*.seed
*.pid.lock

# ===================================
# Coverage & Testing
# ===================================
coverage/
*.lcov
.nyc_output
test-results/
playwright-report/
test-results/
coverage/

# ===================================
# TypeScript
# ===================================
*.tsbuildinfo
next-env.d.ts

# ===================================
# Package Managers
# ===================================
.npm
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
.pnpm-store/

# ===================================
# Cache
# ===================================
.eslintcache
.cache
.parcel-cache
.next/cache/
.turbo/

# ===================================
# IDE & Editors
# ===================================
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.c9/
*.launch
.settings/
.vscode-test

# ===================================
# OS Generated Files
# ===================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# ===================================
# Database Files
# ===================================
*.sqlite
*.sqlite3
*.db
*.db-journal
*.db-wal
*.db-shm

# ===================================
# Application Specific
# ===================================
# File uploads
uploads/
temp/
tmp/
public/uploads/

# Excel files
*.xlsx
*.xls
*.csv
!templates/*.xlsx
!templates/*.xls

# Backup files
*.backup
*.bak
*.old

# ===================================
# Security & Certificates
# ===================================
*.pem
*.key
*.crt
*.p12
*.pfx

# ===================================
# Docker
# ===================================
.dockerignore
Dockerfile.prod
docker-compose.override.yml

# ===================================
# Monitoring & Analytics
# ===================================
.sentryclirc

# ===================================
# Miscellaneous
# ===================================
*.tgz
.yarn-integrity
.tern-port
.cache/
.serverless/
.fusebox/
.dynamodb/
