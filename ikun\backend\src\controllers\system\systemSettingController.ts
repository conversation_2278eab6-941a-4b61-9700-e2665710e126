/**
 * 系统设置控制器
 * 处理系统设置相关的HTTP请求
 */

import { Request, Response } from 'express';
import { systemSettingService } from '@/services/system/systemSettingService';
import { logger } from '@/utils/logger';
import { getBeijingTimeISO } from '@/utils/time';
import { BadRequestError } from '@/middleware/error/errorHandler';

export class SystemSettingController {
  /**
   * 获取所有设置
   * GET /api/v1/system/settings
   */
  public async getAllSettings(req: Request, res: Response): Promise<void> {
    try {
      const settings = await systemSettingService.getAllSettings();

      res.json({
        code: 200,
        message: 'successfully',
        data: settings,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      logger.error('Error getting all settings:', error);
      res.status(500).json({
        code: 500,
        message: 'Failed to get settings',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: getBeijingTimeISO()
      });
    }
  }

  /**
   * 根据分类获取设置
   * GET /api/v1/system/settings/category/:category
   */
  public async getSettingsByCategory(req: Request, res: Response): Promise<void> {
    try {
      const { category } = req.params;
      const settings = await systemSettingService.getSettingsByCategory(category);

      res.json({
        code: 200,
        message: 'Settings retrieved successfully',
        data: settings,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      logger.error('Error getting settings by category:', error);
      res.status(500).json({
        code: 500,
        message: 'Failed to get settings',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: getBeijingTimeISO()
      });
    }
  }

  /**
   * 获取单个设置
   * GET /api/v1/system/settings/:key
   */
  public async getSetting(req: Request, res: Response): Promise<void> {
    try {
      const { key } = req.params;
      const setting = await systemSettingService.getSetting(key);

      res.json({
        code: 200,
        message: 'Setting retrieved successfully',
        data: setting,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      logger.error('Error getting setting:', error);
      const statusCode = error instanceof Error && error.message.includes('not found') ? 404 : 500;
      res.status(statusCode).json({
        code: statusCode,
        message: 'Failed to get setting',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: getBeijingTimeISO()
      });
    }
  }

  /**
   * 更新设置
   * PUT /api/v1/system/settings/:key
   */
  public async updateSetting(req: Request, res: Response): Promise<void> {
    try {
      const { key } = req.params;
      const { value } = req.body;

      if (value === undefined) {
        throw new BadRequestError('Setting value is required');
      }

      const setting = await systemSettingService.updateSetting(key, value);

      res.json({
        code: 200,
        message: 'Setting updated successfully',
        data: setting,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      logger.error('Error updating setting:', error);
      const statusCode = error instanceof BadRequestError ? 400 : 500;
      res.status(statusCode).json({
        code: statusCode,
        message: 'Failed to update setting',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: getBeijingTimeISO()
      });
    }
  }

  /**
   * 批量更新设置
   * PUT /api/v1/system/settings/batch
   */
  public async batchUpdateSettings(req: Request, res: Response): Promise<void> {
    try {
      const { settings } = req.body;

      if (!Array.isArray(settings) || settings.length === 0) {
        throw new BadRequestError('Settings array is required');
      }

      // 验证每个设置项的格式
      for (const setting of settings) {
        if (!setting.key || setting.value === undefined) {
          throw new BadRequestError('Each setting must have key and value');
        }
      }

      const success = await systemSettingService.batchUpdateSettings(settings);

      res.json({
        code: 200,
        message: 'Settings updated successfully',
        data: { success },
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      logger.error('Error batch updating settings:', error);
      const statusCode = error instanceof BadRequestError ? 400 : 500;
      res.status(statusCode).json({
        code: statusCode,
        message: 'Failed to update settings',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: getBeijingTimeISO()
      });
    }
  }

  /**
   * 获取公开设置
   * GET /api/v1/system/settings/public
   */
  public async getPublicSettings(req: Request, res: Response): Promise<void> {
    try {
      const settings = await systemSettingService.getPublicSettings();

      res.json({
        code: 200,
        message: 'Public settings retrieved successfully',
        data: settings,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      logger.error('Error getting public settings:', error);
      res.status(500).json({
        code: 500,
        message: 'Failed to get public settings',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: getBeijingTimeISO()
      });
    }
  }

  /**
   * 重置设置为默认值
   * POST /api/v1/system/settings/:key/reset
   */
  public async resetToDefault(req: Request, res: Response): Promise<void> {
    try {
      const { key } = req.params;
      const setting = await systemSettingService.resetToDefault(key);

      res.json({
        code: 200,
        message: 'Setting reset to default successfully',
        data: setting,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      logger.error('Error resetting setting:', error);
      const statusCode = error instanceof BadRequestError ? 400 : 500;
      res.status(statusCode).json({
        code: statusCode,
        message: 'Failed to reset setting',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: getBeijingTimeISO()
      });
    }
  }

  /**
   * 获取翻译配置
   * GET /api/v1/system/settings/translation/config
   */
  public async getTranslationConfig(req: Request, res: Response): Promise<void> {
    try {
      const config = await systemSettingService.getTranslationConfig();

      res.json({
        code: 200,
        message: 'Translation config retrieved successfully',
        data: config,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      logger.error('Error getting translation config:', error);
      res.status(500).json({
        code: 500,
        message: 'Failed to get translation config',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: getBeijingTimeISO()
      });
    }
  }

  /**
   * 更新翻译配置
   * PUT /api/v1/system/settings/translation/config
   */
  public async updateTranslationConfig(req: Request, res: Response): Promise<void> {
    try {
      const config = req.body;
      const success = await systemSettingService.updateTranslationConfig(config);

      res.json({
        code: 200,
        message: 'Translation config updated successfully',
        data: { success },
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      logger.error('Error updating translation config:', error);
      const statusCode = error instanceof BadRequestError ? 400 : 500;
      res.status(statusCode).json({
        code: statusCode,
        message: 'Failed to update translation config',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: getBeijingTimeISO()
      });
    }
  }

  /**
   * 获取系统基础配置
   * GET /api/v1/system/settings/basic
   */
  public async getSystemBasicConfig(req: Request, res: Response): Promise<void> {
    try {
      const config = await systemSettingService.getSystemBasicConfig();

      res.json({
        code: 200,
        message: 'System basic config retrieved successfully',
        data: config,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      logger.error('Error getting system basic config:', error);
      res.status(500).json({
        code: 500,
        message: 'Failed to get system basic config',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: getBeijingTimeISO()
      });
    }
  }

  /**
   * 更新系统基础配置
   * PUT /api/v1/system/settings/basic
   */
  public async updateSystemBasicConfig(req: Request, res: Response): Promise<void> {
    try {
      const config = req.body;
      const success = await systemSettingService.updateSystemBasicConfig(config);

      res.json({
        code: 200,
        message: 'System basic config updated successfully',
        data: { success },
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      logger.error('Error updating system basic config:', error);
      res.status(500).json({
        code: 500,
        message: 'Failed to update system basic config',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: getBeijingTimeISO()
      });
    }
  }

  /**
   * 获取所有分类
   * GET /api/v1/system/settings/categories
   */
  public async getCategories(req: Request, res: Response): Promise<void> {
    try {
      const categories = await systemSettingService.getCategories();

      res.json({
        code: 200,
        message: 'Categories retrieved successfully',
        data: categories,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      logger.error('Error getting categories:', error);
      res.status(500).json({
        code: 500,
        message: 'Failed to get categories',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: getBeijingTimeISO()
      });
    }
  }

  /**
   * 获取Token统计数据
   * GET /api/v1/system/settings/token-statistics
   */
  public async getTokenStatistics(req: Request, res: Response): Promise<void> {
    try {
      const statistics = await systemSettingService.getTokenStatistics();

      res.json({
        code: 200,
        message: 'Token statistics retrieved successfully',
        data: statistics,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      logger.error('Error getting token statistics:', error);
      res.status(500).json({
        code: 500,
        message: 'Failed to get token statistics',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: getBeijingTimeISO()
      });
    }
  }

  /**
   * 更新Token统计数据
   * PUT /api/v1/system/settings/token-statistics
   */
  public async updateTokenStatistics(req: Request, res: Response): Promise<void> {
    try {
      const statistics = req.body;
      const success = await systemSettingService.updateTokenStatistics(statistics);

      res.json({
        code: 200,
        message: 'Token statistics updated successfully',
        data: { success },
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      logger.error('Error updating token statistics:', error);
      res.status(500).json({
        code: 500,
        message: 'Failed to update token statistics',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: getBeijingTimeISO()
      });
    }
  }

  /**
   * 重置今日Token统计
   * POST /api/v1/system/settings/token-statistics/reset-daily
   */
  public async resetDailyTokenStats(req: Request, res: Response): Promise<void> {
    try {
      const success = await systemSettingService.resetDailyTokenStats();

      res.json({
        code: 200,
        message: 'Daily token statistics reset successfully',
        data: { success },
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      logger.error('Error resetting daily token stats:', error);
      res.status(500).json({
        code: 500,
        message: 'Failed to reset daily token statistics',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: getBeijingTimeISO()
      });
    }
  }

  /**
   * 增加Token使用量
   * POST /api/v1/system/settings/token-usage/increment
   */
  public async incrementTokenUsage(req: Request, res: Response): Promise<void> {
    try {
      const { provider, modelName, inputTokens, outputTokens } = req.body;

      if (!provider || !modelName || inputTokens === undefined || outputTokens === undefined) {
        res.status(400).json({
          code: 400,
          message: 'Missing required parameters: provider, modelName, inputTokens, outputTokens',
          timestamp: getBeijingTimeISO()
        });
        return;
      }

      await systemSettingService.incrementTokenUsage(provider, modelName, inputTokens, outputTokens);
      res.json({
        code: 200,
        message: 'Token统计量增加成功！',
        data: {
          provider,
          modelName,
          inputTokens,
          outputTokens
        },
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      logger.error('Error incrementing token usage:', error);
      res.status(500).json({
        code: 500,
        message: 'Token统计失败！',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: getBeijingTimeISO()
      });
    }
  }
}

export const systemSettingController = new SystemSettingController();
