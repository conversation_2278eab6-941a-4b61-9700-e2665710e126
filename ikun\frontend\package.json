{"name": "ikun-erp-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "analyze": "cross-env ANALYZE=true next build", "clean": "rimraf .next out dist"}, "dependencies": {"@hookform/resolvers": "^3.3.2", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@tanstack/react-query": "^5.8.4", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "cmdk": "^1.1.1", "date-fns": "^2.30.0", "js-cookie": "^3.0.5", "lodash": "^4.17.21", "lucide-react": "^0.292.0", "next": "14.0.3", "next-themes": "^0.2.1", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.1", "sonner": "^2.0.5", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.22.4"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/lodash": "^4.14.202", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "autoprefixer": "^10.0.1", "cross-env": "^7.0.3", "eslint": "^8", "eslint-config-next": "14.0.3", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.0.1", "postcss": "^8", "prettier": "^3.1.0", "prettier-plugin-tailwindcss": "^0.5.7", "rimraf": "^5.0.5", "tailwindcss": "^3.3.0", "typescript": "^5"}}