'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Package, ShoppingCart, TrendingUp, Users } from 'lucide-react'
import { formatNumber } from '@/lib/utils'

// 模拟数据，实际应该从API获取
const stats = [
  {
    title: '总产品数',
    value: 1234,
    change: '+12%',
    changeType: 'positive' as const,
    icon: Package,
  },
  {
    title: '活跃产品',
    value: 856,
    change: '+8%',
    changeType: 'positive' as const,
    icon: TrendingUp,
  },
  {
    title: '待处理订单',
    value: 23,
    change: '-5%',
    changeType: 'negative' as const,
    icon: ShoppingCart,
  },
  {
    title: '店铺数量',
    value: 5,
    change: '+1',
    changeType: 'positive' as const,
    icon: Users,
  },
]

export function DashboardStats() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {stats.map((stat) => (
        <Card key={stat.title}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {stat.title}
            </CardTitle>
            <stat.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatNumber(stat.value)}</div>
            <p className={`text-xs ${
              stat.changeType === 'positive' 
                ? 'text-green-600' 
                : 'text-red-600'
            }`}>
              {stat.change} 较上月
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
