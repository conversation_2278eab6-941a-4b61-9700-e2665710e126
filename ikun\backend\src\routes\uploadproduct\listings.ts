/**
 * Upload Product Listings Routes
 * Product listing management endpoints
 */

import { Router } from 'express';
import { uploadproductController } from '@/controllers/uploadproduct/uploadproductController';

const router = Router();

// GET /api/v1/uploadproduct/listings - Get product listings
router.get('/', uploadproductController.getListings.bind(uploadproductController));

// POST /api/v1/uploadproduct/listings - Create product listing
router.post('/', uploadproductController.createListing.bind(uploadproductController));

// GET /api/v1/uploadproduct/listings/:id - Get product listing details
router.get('/:id', uploadproductController.getListingById.bind(uploadproductController));

// PUT /api/v1/uploadproduct/listings/:id - Update product listing
router.put('/:id', uploadproductController.updateListing.bind(uploadproductController));

// DELETE /api/v1/uploadproduct/listings/:id - Delete product listing
router.delete('/:id', uploadproductController.deleteListing.bind(uploadproductController));

export default router;
