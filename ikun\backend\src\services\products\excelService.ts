/**
 * Excel Service
 * Handles Excel import/export operations for products
 */

import * as XLSX from 'xlsx';
import { productService } from './productService';
import { categoryService } from './categoryService';
import { logger } from '@/utils/logger';
import { getBeijingTimeISO, getBeijingTime } from '@/utils/time';
import { CreateProductRequest, SearchQuery } from '@/types';

interface ImportResult {
  filename: string;
  success: boolean;
  total: number;
  imported: number;
  errors: string[];
}

interface ExcelRow {
  操作: string;
  'SKU*': string;
  'EAN*': string;
  '产品类目*': string;
  '英文标题*': string;
  '英文卖点1': string;
  '英文卖点2': string;
  '英文卖点3': string;
  '英文卖点4': string;
  '英文卖点5': string;
  '英文描述': string;
  '图片1': string;
  '图片2': string;
  '图片3': string;
  '图片4': string;
  '图片5': string;
  '成本价': number;
  '包装重量(g)': number;
  '包装长度(cm)': number;
  '包装宽度(cm)': number;
  '包装高度(cm)': number;
  '采购链接': string;
  '备注': string;
  '产品状态': string;
}

class ExcelService {
  // 生成导入模板
  public async generateTemplate(): Promise<Buffer> {
    try {
      // 获取所有分类用于下拉选择
      const categories = await categoryService.getAllCategories();
      const categoryOptions = this.formatCategoriesForDropdown(categories);

      // 创建工作簿
      const workbook = XLSX.utils.book_new();

      // 创建主数据表
      const headers = [
        '操作', 'SKU*', 'EAN*', '产品类目*', '英文标题*', '英文卖点1', '英文卖点2', '英文卖点3', '英文卖点4', '英文卖点5', '英文描述',
        '图片1', '图片2', '图片3', '图片4', '图片5',
        '成本价', '包装重量(g)', '包装长度(cm)', '包装宽度(cm)', '包装高度(cm)',
        '采购链接', '备注', '产品状态'
      ];

      // 示例数据
      const sampleData = [
        [
          'update', 'gfd4t65r4w646', '1234567890123', 'clothing/mens/tshirts',
          'Sample Product Title', '高品质材料 🌟', '舒适透气 💨', '时尚设计 ✨', '耐用持久 💪', '性价比高 💰', 'Sample product description',
          'https://example.com/image1.jpg', '', '', '', '',
          29.99, 200, 25, 20, 2,
          'https://example.com/purchase-link', '示例备注', 'active'
        ]
      ];

      const worksheetData = [headers, ...sampleData];
      const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

      // 设置列宽
      const colWidths = [
        { wch: 10 }, { wch: 15 }, { wch: 15 }, { wch: 40 }, { wch: 30 },
        { wch: 20 }, { wch: 20 }, { wch: 20 }, { wch: 20 }, { wch: 20 }, { wch: 30 },
        { wch: 30 }, { wch: 30 }, { wch: 30 }, { wch: 30 }, { wch: 30 },
        { wch: 10 }, { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 12 },
        { wch: 30 }, { wch: 20 }, { wch: 10 }
      ];
      worksheet['!cols'] = colWidths;

      // 添加数据验证（下拉选择）
      if (!worksheet['!dataValidation']) {
        worksheet['!dataValidation'] = [];
      }

      // 操作列下拉选择
      worksheet['!dataValidation'].push({
        sqref: 'A2:A1000',
        type: 'list',
        formula1: '"update,delete"'
      });

      // 产品状态下拉选择（列位置调整为X列，因为增加了5个卖点列）
      worksheet['!dataValidation'].push({
        sqref: 'X2:X1000',
        type: 'list',
        formula1: '"draft,active,inactive"'
      });

      XLSX.utils.book_append_sheet(workbook, worksheet, '产品数据');

      // 创建分类选择表
      const categoryHeaders = ['分类显示路径', '分类编码（填写此列）'];
      const categoryData = categoryOptions.map(cat => [cat.displayPath, cat.categoryPath]);
      const categoryWorksheetData = [categoryHeaders, ...categoryData];
      const categoryWorksheet = XLSX.utils.aoa_to_sheet(categoryWorksheetData);
      categoryWorksheet['!cols'] = [{ wch: 50 }, { wch: 30 }];
      XLSX.utils.book_append_sheet(workbook, categoryWorksheet, '分类选择');

      // 创建说明表
      const instructionData = [
        ['字段说明'],
        ['操作: update=更新产品, delete=删除产品'],
        ['SKU*: 产品SKU，必填，唯一标识'],
        ['EAN*: 产品EAN码，必填'],
        ['产品类目*: 必须填写三级分类编码，如：clothing/mens/tshirts'],
        ['英文标题*: 产品英文标题，必填'],
        ['英文卖点1-5: 产品英文卖点，支持emoji表情，如：高品质材料 🌟'],
        ['英文描述: 产品英文描述'],
        ['图片1-5: 产品图片URL链接'],
        ['成本价: 产品成本价格'],
        ['包装信息: 包装的重量和尺寸'],
        ['采购链接: 产品采购链接'],
        ['备注: 产品备注信息'],
        ['产品状态: draft=草稿, active=活跃, inactive=禁用'],
        [''],
        ['注意事项:'],
        ['1. 带*号的字段为必填项'],
        ['2. SKU必须唯一，重复的SKU会导致导入失败'],
        ['3. 产品类目必须填写分类编码，请参考"分类选择"表中的"分类编码"列'],
        ['4. 分类编码格式：一级/二级/三级，如：clothing/mens/tshirts'],
        ['5. 图片链接必须是有效的URL'],
        ['6. 操作为delete时，只需要填写SKU字段']
      ];
      const instructionWorksheet = XLSX.utils.aoa_to_sheet(instructionData);
      instructionWorksheet['!cols'] = [{ wch: 80 }];
      XLSX.utils.book_append_sheet(workbook, instructionWorksheet, '使用说明');

      // 生成Excel文件
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      return buffer;
    } catch (error) {
      logger.error('Generate template failed:', error);
      throw new Error('生成模板失败');
    }
  }

  // 从Excel导入产品
  public async importFromExcel(files: Express.Multer.File[], validateOnly: boolean = false): Promise<ImportResult[]> {
    const results: ImportResult[] = [];

    for (const file of files) {
      const result: ImportResult = {
        filename: file.originalname,
        success: false,
        total: 0,
        imported: 0,
        errors: []
      };

      try {
        // 解析Excel文件
        const workbook = XLSX.read(file.buffer, { type: 'buffer' });
        const sheetName = workbook.SheetNames[0];
        const worksheet = workbook.Sheets[sheetName];
        const data = XLSX.utils.sheet_to_json<ExcelRow>(worksheet);

        result.total = data.length;

        if (validateOnly) {
          // 验证模式：逐行验证但不执行数据库操作
          await this.validateExcelData(data, result);
        } else {
          // 导入模式：批量处理
          await this.batchImportExcelData(data, result);
        }

        result.success = result.errors.length === 0;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        result.errors.push(`文件解析失败: ${errorMessage}`);
      }

      results.push(result);
    }

    return results;
  }

  // 导出产品到Excel
  public async exportToExcel(query: SearchQuery & {
    status?: 'draft' | 'active' | 'inactive';
    category_id?: number;
    source?: string;
    start_date?: string;
    end_date?: string;
    exportAll?: boolean;
    selectedIds?: number[];
  }): Promise<Buffer> {
    try {
      let products: any[] = [];

      // 根据导出类型获取产品数据
      if (query.exportAll === false && query.selectedIds && query.selectedIds.length > 0) {
        // 导出选中的产品
        logger.info(`Exporting selected products: ${query.selectedIds.length} items`);
        products = await this.getProductsByIds(query.selectedIds);
      } else {
        // 导出所有产品（应用筛选条件）
        logger.info('Exporting all products with filters');
        const { exportAll, selectedIds, ...searchQuery } = query;
        const result = await productService.getProducts({
          ...searchQuery,
          limit: 50000 // 增加限制以支持大量导出
        });
        products = result.items;
      }

      // 获取分类数据用于格式化
      const categories = await categoryService.getAllCategories();

      // 准备导出数据
      const headers = [
        '操作', 'SKU', 'EAN', '产品类目', '英文标题', '英文卖点1', '英文卖点2', '英文卖点3', '英文卖点4', '英文卖点5', '英文描述',
        '图片1', '图片2', '图片3', '图片4', '图片5',
        '成本价', '包装重量(g)', '包装长度(cm)', '包装宽度(cm)', '包装高度(cm)',
        '采购链接', '备注', '产品状态', '创建时间', '更新时间'
      ];

      const exportData = products.map((product: any) => {
        // 解析卖点数据 - 处理可能是字符串或数组的情况
        let sellingPoints: string[] = [];
        if (product.selling_point) {
          if (typeof product.selling_point === 'string') {
            try {
              sellingPoints = JSON.parse(product.selling_point);
            } catch (error) {
              logger.warn(`Failed to parse selling_point for product ${product.id}:`, error);
              sellingPoints = [];
            }
          } else if (Array.isArray(product.selling_point)) {
            sellingPoints = product.selling_point;
          }
        }

        const sp1 = sellingPoints[0] || '';
        const sp2 = sellingPoints[1] || '';
        const sp3 = sellingPoints[2] || '';
        const sp4 = sellingPoints[3] || '';
        const sp5 = sellingPoints[4] || '';

        return [
          'update',
          product.sku,
          product.ean,
          this.formatCategoryPath(product.category, categories),
          product.english_title,
          sp1, sp2, sp3, sp4, sp5,
          product.english_description || '',
          product.image1 || '',
          product.image2 || '',
          product.image3 || '',
          product.image4 || '',
          product.image5 || '',
          product.cost_price || 0,
          product.package_weight || 0,
          product.package_length || 0,
          product.package_width || 0,
          product.package_height || 0,
          product.purchase_link || '',
          product.remarks || '',
          product.status,
          product.created_at,
          product.updated_at
        ];
      });

      // 创建工作簿
      const workbook = XLSX.utils.book_new();
      const worksheetData = [headers, ...exportData];
      const worksheet = XLSX.utils.aoa_to_sheet(worksheetData);

      // 设置列宽
      const colWidths = [
        { wch: 10 }, { wch: 15 }, { wch: 15 }, { wch: 40 }, { wch: 30 },
        { wch: 20 }, { wch: 20 }, { wch: 20 }, { wch: 20 }, { wch: 20 }, { wch: 30 },
        { wch: 30 }, { wch: 30 }, { wch: 30 }, { wch: 30 }, { wch: 30 },
        { wch: 10 }, { wch: 12 }, { wch: 12 }, { wch: 12 }, { wch: 12 },
        { wch: 30 }, { wch: 20 }, { wch: 10 }, { wch: 20 }, { wch: 20 }
      ];
      worksheet['!cols'] = colWidths;

      XLSX.utils.book_append_sheet(workbook, worksheet, '产品数据');

      // 生成Excel文件
      const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
      return buffer;
    } catch (error) {
      logger.error('Export to Excel failed:', error);
      throw new Error('导出Excel失败');
    }
  }

  // 验证Excel数据（不执行数据库操作）
  private async validateExcelData(data: ExcelRow[], result: ImportResult): Promise<void> {
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      try {
        await this.validateExcelRow(row);
        result.imported++;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        result.errors.push(`行 ${i + 2}: ${errorMessage}`);
      }
    }
  }

  // 批量导入Excel数据
  private async batchImportExcelData(data: ExcelRow[], result: ImportResult): Promise<void> {
    const BATCH_SIZE = 100; // 每批处理100条记录
    const validProducts: CreateProductRequest[] = [];
    const updateProducts: { id: number; data: CreateProductRequest }[] = [];
    const deleteSkus: string[] = [];

    // 第一步：验证所有数据并分类
    for (let i = 0; i < data.length; i++) {
      const row = data[i];
      try {
        const operation = row.操作?.toLowerCase();
        const sku = row['SKU*'];

        if (!sku) {
          throw new Error('SKU不能为空');
        }

        if (operation === 'delete') {
          deleteSkus.push(sku);
          continue;
        }

        // 验证必填字段
        this.validateRequiredFields(row);

        // 构建产品数据
        const productData = this.buildProductData(row);

        // 检查产品是否存在
        const existingProduct = await productService.getProductBySku(sku);
        if (existingProduct) {
          updateProducts.push({ id: existingProduct.id, data: productData });
        } else {
          validProducts.push(productData);
        }

        result.imported++;
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        result.errors.push(`行 ${i + 2}: ${errorMessage}`);
      }
    }

    // 第二步：批量执行数据库操作
    try {
      // 批量删除
      if (deleteSkus.length > 0) {
        await this.batchDeleteProducts(deleteSkus);
      }

      // 批量创建新产品
      if (validProducts.length > 0) {
        await this.batchCreateProducts(validProducts, BATCH_SIZE);
      }

      // 批量更新现有产品
      if (updateProducts.length > 0) {
        await this.batchUpdateProducts(updateProducts, BATCH_SIZE);
      }
    } catch (error) {
      logger.error('Batch import failed:', error);
      throw error;
    }
  }

  // 验证Excel行数据（不执行数据库操作）
  private async validateExcelRow(row: ExcelRow): Promise<void> {
    const sku = row['SKU*'];
    if (!sku) {
      throw new Error('SKU不能为空');
    }

    const operation = row.操作?.toLowerCase();
    if (operation === 'delete') {
      return; // 删除操作只需要SKU
    }

    // 验证必填字段
    this.validateRequiredFields(row);

    // 验证分类路径格式
    this.parseCategoryPath(row['产品类目*']);
  }

  // 验证必填字段
  private validateRequiredFields(row: ExcelRow): void {
    if (!row['EAN*']) {
      throw new Error('EAN不能为空');
    }
    if (!row['产品类目*']) {
      throw new Error('产品类目不能为空');
    }
    if (!row['英文标题*']) {
      throw new Error('英文标题不能为空');
    }
  }

  // 构建产品数据
  private buildProductData(row: ExcelRow): CreateProductRequest {
    const categoryPath = this.parseCategoryPath(row['产品类目*']);

    // 构建卖点数组
    const sellingPoints = [
      row['英文卖点1'] || '',
      row['英文卖点2'] || '',
      row['英文卖点3'] || '',
      row['英文卖点4'] || '',
      row['英文卖点5'] || ''
    ].filter(point => point.trim() !== ''); // 过滤空卖点

    return {
      sku: row['SKU*'],
      ean: row['EAN*'],
      category: categoryPath,
      english_title: row['英文标题*'],
      selling_point: sellingPoints,
      english_description: row['英文描述'] || '',
      image1: row['图片1'] || '',
      image2: row['图片2'] || '',
      image3: row['图片3'] || '',
      image4: row['图片4'] || '',
      image5: row['图片5'] || '',
      cost_price: row['成本价'] || 0,
      package_weight: row['包装重量(g)'] || 0,
      package_length: row['包装长度(cm)'] || 0,
      package_width: row['包装宽度(cm)'] || 0,
      package_height: row['包装高度(cm)'] || 0,
      purchase_link: row['采购链接'] || '',
      remarks: row['备注'] || '',
      status: (row['产品状态'] as 'draft' | 'active' | 'inactive') || 'draft',
      source: 'excel'
    };
  }

  // 批量删除产品
  private async batchDeleteProducts(skus: string[]): Promise<void> {
    for (const sku of skus) {
      try {
        const existingProduct = await productService.getProductBySku(sku);
        if (existingProduct) {
          await productService.deleteProduct(existingProduct.id);
        }
      } catch (error) {
        logger.warn('Failed to delete product:', { sku, error });
      }
    }
  }

  // 批量创建产品
  private async batchCreateProducts(products: CreateProductRequest[], batchSize: number): Promise<void> {
    for (let i = 0; i < products.length; i += batchSize) {
      const batch = products.slice(i, i + batchSize);
      try {
        await productService.importProducts(batch);
        logger.info(`Batch created ${batch.length} products (${i + 1}-${i + batch.length})`);
      } catch (error) {
        logger.error('Batch create failed:', { batchStart: i, batchSize: batch.length, error });
        // 如果批量失败，尝试逐个创建
        await this.fallbackCreateProducts(batch);
      }
    }
  }

  // 批量更新产品
  private async batchUpdateProducts(updates: { id: number; data: CreateProductRequest }[], batchSize: number): Promise<void> {
    for (let i = 0; i < updates.length; i += batchSize) {
      const batch = updates.slice(i, i + batchSize);
      try {
        // 并发更新（但限制并发数）
        await Promise.all(batch.map(({ id, data }) => productService.updateProduct(id, data)));
        logger.info(`Batch updated ${batch.length} products (${i + 1}-${i + batch.length})`);
      } catch (error) {
        logger.error('Batch update failed:', { batchStart: i, batchSize: batch.length, error });
        // 如果批量失败，尝试逐个更新
        await this.fallbackUpdateProducts(batch);
      }
    }
  }

  // 回退：逐个创建产品
  private async fallbackCreateProducts(products: CreateProductRequest[]): Promise<void> {
    for (const product of products) {
      try {
        await productService.createProduct(product);
      } catch (error) {
        logger.warn('Failed to create single product:', { sku: product.sku, error });
      }
    }
  }

  // 回退：逐个更新产品
  private async fallbackUpdateProducts(updates: { id: number; data: CreateProductRequest }[]): Promise<void> {
    for (const { id, data } of updates) {
      try {
        await productService.updateProduct(id, data);
      } catch (error) {
        logger.warn('Failed to update single product:', { id, sku: data.sku, error });
      }
    }
  }

  // 根据ID获取产品列表
  private async getProductsByIds(ids: number[]): Promise<any[]> {
    try {
      const products = await Promise.all(
        ids.map(async (id) => {
          try {
            return await productService.getProductById(id);
          } catch (error) {
            logger.warn(`Failed to get product ${id}:`, error);
            return null;
          }
        })
      );

      // 过滤掉获取失败的产品
      return products.filter(product => product !== null);
    } catch (error) {
      logger.error('Failed to get products by IDs:', error);
      throw new Error('获取选中产品失败');
    }
  }

  // 处理Excel行数据（保留原方法用于兼容）
  private async processExcelRow(row: ExcelRow, validateOnly: boolean = false): Promise<void> {
    const operation = row.操作?.toLowerCase();
    const sku = row['SKU*'];

    if (!sku) {
      throw new Error('SKU不能为空');
    }

    if (operation === 'delete') {
      // 删除产品
      const existingProduct = await productService.getProductBySku(sku);
      if (existingProduct) {
        await productService.deleteProduct(existingProduct.id);
      }
      return;
    }

    // 验证必填字段
    if (!row['EAN*']) {
      throw new Error('EAN不能为空');
    }
    if (!row['产品类目*']) {
      throw new Error('产品类目不能为空');
    }
    if (!row['英文标题*']) {
      throw new Error('英文标题不能为空');
    }

    // 解析类目路径
    const categoryPath = this.parseCategoryPath(row['产品类目*']);

    // 构建产品数据
    const productData: CreateProductRequest = {
      sku,
      ean: row['EAN*'],
      category: categoryPath,
      english_title: row['英文标题*'],
      english_description: row['英文描述'] || '',
      image1: row['图片1'] || '',
      image2: row['图片2'] || '',
      image3: row['图片3'] || '',
      image4: row['图片4'] || '',
      image5: row['图片5'] || '',
      cost_price: row['成本价'] || 0,
      package_weight: row['包装重量(g)'] || 0,
      package_length: row['包装长度(cm)'] || 0,
      package_width: row['包装宽度(cm)'] || 0,
      package_height: row['包装高度(cm)'] || 0,
      purchase_link: row['采购链接'] || '',
      remarks: row['备注'] || '',
      status: (row['产品状态'] as 'draft' | 'active' | 'inactive') || 'draft',
      source: 'excel'
    };

    // 如果只是验证模式，跳过实际的数据库操作
    if (validateOnly) {
      // 只检查SKU是否存在，用于验证
      await productService.getProductBySku(sku);
      return;
    }

    // 检查产品是否存在
    const existingProduct = await productService.getProductBySku(sku);
    if (existingProduct) {
      // 更新产品
      await productService.updateProduct(existingProduct.id, productData);
    } else {
      // 创建新产品
      await productService.createProduct(productData);
    }
  }

  // 格式化分类为下拉选择格式
  private formatCategoriesForDropdown(categories: any[]): Array<{ displayPath: string; categoryPath: string }> {
    const result: Array<{ displayPath: string; categoryPath: string }> = [];

    const traverse = (cats: any[], parentDisplayPath = '') => {
      for (const cat of cats) {
        const displayPath = parentDisplayPath
          ? `${parentDisplayPath} - ${cat.chinese_name}（${cat.english_name}）`
          : `${cat.chinese_name}（${cat.english_name}）`;

        if (cat.children && cat.children.length > 0) {
          traverse(cat.children, displayPath);
        } else {
          // 只添加叶子节点（三级分类），使用数据库中的 category_path
          result.push({
            displayPath,
            categoryPath: cat.category_path || ''
          });
        }
      }
    };

    traverse(categories);
    return result;
  }

  // 格式化分类路径显示
  private formatCategoryPath(categoryPath: string, categories: any[]): string {
    // 这里应该实现将API路径转换为显示路径的逻辑
    // 类似前端的 formatCategoryPath 函数
    return categoryPath; // 临时返回原路径
  }

  // 解析分类路径
  private parseCategoryPath(categoryPath: string): string {
    // 现在直接使用分类编码，如：clothing/mens/tshirts
    // 验证格式是否正确
    if (!categoryPath || !categoryPath.includes('/')) {
      throw new Error('分类编码格式错误，应为：一级/二级/三级，如：clothing/mens/tshirts');
    }

    const parts = categoryPath.split('/');
    if (parts.length !== 3) {
      throw new Error('必须选择三级分类，格式：一级/二级/三级');
    }

    return categoryPath.toLowerCase();
  }
}

export const excelService = new ExcelService();
