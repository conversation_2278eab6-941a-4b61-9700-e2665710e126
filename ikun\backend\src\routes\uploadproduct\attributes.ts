/**
 * Upload Product Category Attributes Routes
 * Platform category attribute management endpoints
 */

import { Router } from 'express';
import { uploadproductController } from '@/controllers/uploadproduct/uploadproductController';

const router = Router();

// GET /api/v1/uploadproduct/attributes - Get category attributes
router.get('/', uploadproductController.getAttributes.bind(uploadproductController));

export default router;
