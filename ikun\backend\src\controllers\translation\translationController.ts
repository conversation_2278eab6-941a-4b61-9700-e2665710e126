/**
 * 翻译控制器
 */

import { Request, Response, NextFunction } from 'express';
import { logger } from '@/utils/logger';
import { getBeijingTimeISO } from '@/utils/time';
import { translationService, TranslationService } from '@/services/translation';
import { LanguageCode } from '@/services/translation/types';
import { BadRequestError, InternalServerError } from '@/middleware/error/customErrors';

export class TranslationController {
  /**
   * 单文本翻译
   * POST /api/v1/translation/text
   */
  public async translateText(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { text, sourceLang, targetLang, contentType, provider, platform, source } = req.body;

      // 验证必需参数
      if (!text || !sourceLang || !targetLang) {
        throw new BadRequestError('缺少必需参数: text, sourceLang, targetLang');
      }

      // 验证内容类型
      const validContentTypes = ['title', 'description', 'selling_point'];
      const validatedContentType = contentType && validContentTypes.includes(contentType) 
        ? contentType 
        : 'title';

      logger.info('Text translation request:', {
        sourceLang,
        targetLang,
        contentType: validatedContentType,
        textLength: text.length,
        provider
      });

      const result = await translationService.translateText(
        text,
        sourceLang as LanguageCode,
        targetLang as LanguageCode,
        validatedContentType,
        provider,
        platform,
        source
      );

      res.json({
        code: 200,
        message: result.success ? 'Translation successful' : 'Translation failed',
        data: result,
        timestamp: getBeijingTimeISO()
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 表单提交批量翻译
   * POST /api/v1/translation/batch/forediting
   */
  public async translateBatch(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { text, sourceLang, targetLangs, contentType, provider, platform, source } = req.body;

      // 验证必需参数
      if (!text || !sourceLang || !targetLangs || !Array.isArray(targetLangs)) {
        throw new BadRequestError('缺少必需参数: text, sourceLang, targetLangs');
      }

      // 验证内容类型
      const validContentTypes = ['title', 'description', 'selling_point'];
      const validatedContentType = contentType && validContentTypes.includes(contentType)
        ? contentType
        : 'title';

      logger.info('收到批量翻译请求:', {
        sourceLang,
        targetLangs,
        contentType: validatedContentType,
        textLength: text.length,
        provider,
        platform
      });

      const result = await translationService.translateBatch(
        text,
        sourceLang as LanguageCode,
        targetLangs as LanguageCode[],
        validatedContentType,
        provider,
        platform,
        source
      );

      res.json({
        code: 200,
        message: result.success ? 'Batch translation successful' : 'Batch translation failed',
        data: result,
        timestamp: getBeijingTimeISO()
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 产品批量翻译（基于SKU）
   * POST /api/v1/translation/batch/forbatch
   */
  public async translateProductBatch(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { platform, source, sourceLang, targetLangs, productids, provider } = req.body;

      logger.info('收到产品批量翻译请求:', {
        platform,
        source,
        sourceLang,
        targetLangs,
        productCount: productids.split(',').length,
        provider
      });

      // 解析产品ID列表
      const productIdList = productids.split(',').map((id: string) => parseInt(id.trim(), 10));

      // 调用翻译服务
      const result = await translationService.translateProductBatch(
        productIdList,
        sourceLang as LanguageCode,
        targetLangs as LanguageCode[],
        platform,
        source,
        provider
      );

      res.json({
        code: 200,
        message: result.success ? 'Product batch translation completed' : 'Product batch translation failed',
        data: result,
        timestamp: getBeijingTimeISO()
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取翻译配置
   * GET /api/v1/translation/config
   */
  public async getConfig(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const configInstance = TranslationService.getInstance().getConfig();
      const availableProviders = translationService.getAvailableProviders();

      res.json({
        code: 200,
        message: 'Translation config retrieved successfully',
        data: {
          ...configInstance,
          availableProviders
        },
        timestamp: getBeijingTimeISO()
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 获取翻译统计信息
   * GET /api/v1/translation/stats
   */
  public async getStats(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const stats = translationService.getStats();

      res.json({
        code: 200,
        message: 'Translation stats retrieved successfully',
        data: stats,
        timestamp: getBeijingTimeISO()
      });

    } catch (error) {
      next(error);
    }
  }

  /**
   * 健康检查
   * GET /api/v1/translation/health
   */
  public async healthCheck(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { provider } = req.query;
      
      const result = await translationService.healthCheck(provider as string);

      res.json({
        code: 200,
        message: 'Health check completed',
        data: result,
        timestamp: getBeijingTimeISO()
      });

    } catch (error) {
      next(error);
    }
  }


  /**
   * 重置统计信息
   * POST /api/v1/translation/stats/reset
   */
  public async resetStats(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      translationService.resetStats();

      res.json({
        code: 200,
        message: 'Translation stats reset successfully',
        data: null,
        timestamp: getBeijingTimeISO()
      });

    } catch (error) {
      next(error);
    }
  }
}

// 导出控制器实例
export const translationController = new TranslationController();
