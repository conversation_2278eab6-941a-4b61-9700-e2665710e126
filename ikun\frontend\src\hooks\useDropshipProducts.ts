import { useState, useCallback } from 'react'
import { DropshipProduct } from '@/types'

interface UseDropshipProductsReturn {
  dropshipProducts: DropshipProduct[]
  loading: boolean
  error: string | null
  pagination: {
    page: number
    limit: number
    total: number
  }
  fetchDropshipProducts: (params?: any) => Promise<void>
}

export function useDropshipProducts(): UseDropshipProductsReturn {
  const [dropshipProducts, setDropshipProducts] = useState<DropshipProduct[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 50, // 获取更多产品用于选择
    total: 0
  })

  const fetchDropshipProducts = useCallback(async (params: any = {}) => {
    setLoading(true)
    setError(null)

    try {
      const token = localStorage.getItem('auth_token')
      if (!token) {
        throw new Error('请先登录')
      }

      const queryParams = new URLSearchParams({
        page: params.page?.toString() || pagination.page.toString(),
        limit: params.limit?.toString() || pagination.limit.toString(),
        status: 'active', // 只获取活跃的产品
        ...params
      })

      const response = await fetch(`/api/v1/products/dropship?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const data = await response.json()


      // 检查API返回格式：使用code字段判断成功
      if (data.code === 200) {
        setDropshipProducts(data.data.items || [])
        setPagination({
          page: data.data.pagination?.page || 1,
          limit: data.data.pagination?.limit || 50,
          total: data.data.pagination?.total || 0
        })
      } else {
        throw new Error(data.message || '获取铺货产品列表失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取铺货产品列表失败'
      setError(errorMessage)
      console.error('获取铺货产品列表失败:', err)
    } finally {
      setLoading(false)
    }
  }, [pagination.page, pagination.limit])

  return {
    dropshipProducts,
    loading,
    error,
    pagination,
    fetchDropshipProducts
  }
}
