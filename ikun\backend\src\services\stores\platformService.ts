/**
 * Platform Service
 * 平台管理服务层
 */

import { Platform } from '@/types';
import { database } from '@/database/connection';
import { logger } from '@/utils/logger';
import { NotFoundError, ConflictError } from '@/middleware/error/errorHandler';

class PlatformService {
  /**
   * 获取平台列表
   */
  public async getPlatforms(): Promise<Platform[]> {
    try {
      const platforms = await database.executeQuery<Platform>(`
        SELECT
          id, platform_code, platform_name, platform_name_en,
          logo_url, status, sort_order, config_fields, description,
          created_at, updated_at
        FROM stores_platforms
        WHERE status = 'active'
        ORDER BY sort_order ASC, created_at ASC
      `);

      return platforms;
    } catch (error) {
      logger.error('Failed to get platforms:', error);
      throw error;
    }
  }

  /**
   * 根据平台代码获取平台详情
   */
  public async getPlatformByCode(platformCode: string): Promise<Platform> {
    try {
      const platforms = await database.executeQuery<Platform>(`
        SELECT
          id, platform_code, platform_name, platform_name_en,
          logo_url, status, sort_order, config_fields, description,
          created_at, updated_at
        FROM stores_platforms
        WHERE platform_code = ?
      `, [platformCode]);

      if (platforms.length === 0) {
        throw new NotFoundError(`Platform with code ${platformCode} not found`);
      }

      return platforms[0];
    } catch (error) {
      logger.error('Failed to get platform by code:', error);
      throw error;
    }
  }

  /**
   * 创建平台
   */
  public async createPlatform(platformData: Partial<Platform>): Promise<Platform> {
    try {
      // 检查平台代码是否已存在
      const existing = await this.checkPlatformExists(platformData.platform_code!);
      if (existing) {
        throw new ConflictError(`Platform with code ${platformData.platform_code} already exists`);
      }

      const result = await database.executeUpdate(`
        INSERT INTO platforms (
          platform_code, platform_name, platform_name_en,
          logo_url, status, sort_order, config_fields, description
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        platformData.platform_code,
        platformData.platform_name,
        platformData.platform_name_en,
        platformData.logo_url || null,
        platformData.status || 'active',
        platformData.sort_order || 0,
        platformData.config_fields ? JSON.stringify(platformData.config_fields) : null,
        platformData.description || null
      ]);

      return await this.getPlatformById(result.insertId as number);
    } catch (error) {
      logger.error('Failed to create platform:', error);
      throw error;
    }
  }

  /**
   * 更新平台
   */
  public async updatePlatform(platformCode: string, updateData: Partial<Platform>): Promise<Platform> {
    try {
      const platform = await this.getPlatformByCode(platformCode);

      await database.executeUpdate(`
        UPDATE platforms SET
          platform_name = ?,
          platform_name_en = ?,
          logo_url = ?,
          status = ?,
          sort_order = ?,
          config_fields = ?,
          description = ?
        WHERE platform_code = ?
      `, [
        updateData.platform_name || platform.platform_name,
        updateData.platform_name_en || platform.platform_name_en,
        updateData.logo_url !== undefined ? updateData.logo_url : platform.logo_url,
        updateData.status || platform.status,
        updateData.sort_order !== undefined ? updateData.sort_order : platform.sort_order,
        updateData.config_fields ? JSON.stringify(updateData.config_fields) : platform.config_fields,
        updateData.description !== undefined ? updateData.description : platform.description,
        platformCode
      ]);

      return await this.getPlatformByCode(platformCode);
    } catch (error) {
      logger.error('Failed to update platform:', error);
      throw error;
    }
  }

  /**
   * 更新平台状态
   */
  public async updatePlatformStatus(platformCode: string, status: 'active' | 'inactive'): Promise<Platform> {
    try {
      await this.getPlatformByCode(platformCode); // 检查平台是否存在

      await database.executeUpdate(`
        UPDATE platforms SET status = ? WHERE platform_code = ?
      `, [status, platformCode]);

      return await this.getPlatformByCode(platformCode);
    } catch (error) {
      logger.error('Failed to update platform status:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取平台
   */
  private async getPlatformById(id: number): Promise<Platform> {
    const platforms = await database.executeQuery<Platform>(`
      SELECT
        id, platform_code, platform_name, platform_name_en,
        logo_url, status, sort_order, config_fields, description,
        created_at, updated_at
      FROM stores_platforms
      WHERE id = ?
    `, [id]);

    if (platforms.length === 0) {
      throw new NotFoundError(`Platform with id ${id} not found`);
    }

    return platforms[0];
  }

  /**
   * 检查平台是否存在
   */
  private async checkPlatformExists(platformCode: string): Promise<boolean> {
    try {
      await this.getPlatformByCode(platformCode);
      return true;
    } catch (error) {
      if (error instanceof NotFoundError) {
        return false;
      }
      throw error;
    }
  }
}

export const platformService = new PlatformService();
