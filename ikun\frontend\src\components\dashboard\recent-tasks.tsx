'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { formatDate, getStatusColor, getStatusText } from '@/lib/utils'

// 模拟数据
const recentTasks = [
  {
    id: '1',
    type: 'translation',
    storeName: '亚马逊美国店',
    status: 'running',
    progress: 65,
    totalItems: 100,
    processedItems: 65,
    createdAt: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    type: 'publish',
    storeName: 'eBay英国店',
    status: 'completed',
    progress: 100,
    totalItems: 50,
    processedItems: 50,
    createdAt: '2024-01-14T15:20:00Z',
  },
  {
    id: '3',
    type: 'scraping',
    storeName: 'Shopify店铺',
    status: 'pending',
    progress: 0,
    totalItems: 200,
    processedItems: 0,
    createdAt: '2024-01-13T09:15:00Z',
  },
]

const taskTypeMap = {
  translation: '翻译任务',
  publish: '发布任务',
  scraping: '采集任务',
}

export function RecentTasks() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>最近任务</CardTitle>
        <CardDescription>
          任务执行状态和进度
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recentTasks.map((task) => (
            <div
              key={task.id}
              className="flex items-center justify-between p-3 border rounded-lg"
            >
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-2">
                  <p className="text-sm font-medium">
                    {taskTypeMap[task.type as keyof typeof taskTypeMap]}
                  </p>
                  <Badge 
                    variant="outline" 
                    className={getStatusColor(task.status)}
                  >
                    {getStatusText(task.status)}
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground mb-2">
                  {task.storeName} • {formatDate(task.createdAt)}
                </p>
                <div className="flex items-center gap-2">
                  <Progress value={task.progress} className="flex-1" />
                  <span className="text-xs text-muted-foreground">
                    {task.processedItems}/{task.totalItems}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
