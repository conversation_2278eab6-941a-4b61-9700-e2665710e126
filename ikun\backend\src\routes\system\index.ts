/**
 * System Module Routes
 * System configuration and management endpoints
 */

import { Router } from 'express';
import settingsRoutes from './settings';

const router = Router();

// 系统设置路由
router.use('/settings', settingsRoutes);

// 系统信息路由
router.get('/', (req, res) => {
  res.json({
    message: 'IKUN ERP System API',
    version: '1.0.0',
    modules: {
      settings: '/api/v1/system/settings'
    },
    status: 'active'
  });
});

export default router;
