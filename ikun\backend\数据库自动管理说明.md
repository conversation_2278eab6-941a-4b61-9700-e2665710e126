# 数据库自动管理功能说明

## 📋 功能概述

IKUN ERP后端系统实现了完全自动化的数据库管理功能，包括：

1. **自动创建数据表** - 启动时检查并创建缺失的数据表
2. **自动字段更新** - 检测模型变更并自动添加新字段
3. **自动索引管理** - 创建必要的数据库索引
4. **自动外键约束** - 建立表间关联关系

## 🔧 配置位置

### 数据库连接配置
**文件位置**: `src/config/config.ts`

```typescript
database: {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306', 10),
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '123456',
  database: process.env.DB_NAME || 'ikun',
  connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '10', 10),
  timeout: parseInt(process.env.DB_TIMEOUT || '60000', 10)
}
```

### 环境变量配置
**文件位置**: `.env` (复制 `.env.example` 并修改)

```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=123456
DB_NAME=ikun
DB_CONNECTION_LIMIT=10
DB_TIMEOUT=60000
```

## 🚀 自动功能实现

### 1. 启动时自动检查和创建表

当后端服务启动时，系统会自动：

1. **连接数据库** - 测试数据库连接是否正常
2. **检查表结构** - 对比当前数据库与模型定义
3. **创建缺失表** - 自动创建不存在的数据表
4. **添加新字段** - 为现有表添加新增的字段
5. **创建索引** - 建立必要的数据库索引
6. **设置外键** - 建立表间关联关系

### 2. 数据表结构定义

**文件位置**: `src/database/migration.ts`

系统预定义了以下数据表：

- ✅ **users** - 用户表
- ✅ **product_categories** - 产品分类表
- ✅ **product_dropship** - 铺货产品表
- ✅ **stores_platforms** - 平台配置表
- ✅ **stores_updata** - 统一店铺表
- ✅ **orders_updata** - 订单表

### 3. 字段自动更新机制

当您在模型中添加新字段时：

```typescript
// 例如：在 dropship_products 表中添加新字段
{
  name: 'new_field',
  type: 'VARCHAR(100)',
  nullable: true,
  comment: '新增字段说明'
}
```

系统会在下次启动时自动：
1. 检测到新字段
2. 生成 ALTER TABLE 语句
3. 执行字段添加操作
4. 记录操作日志

## 📊 支持的字段类型

### 基础数据类型
- `BIGINT` - 大整数 (主键推荐)
- `INT` - 整数
- `TINYINT` - 小整数
- `VARCHAR(n)` - 变长字符串
- `TEXT` - 长文本
- `DECIMAL(m,n)` - 精确小数
- `BOOLEAN` - 布尔值
- `TIMESTAMP` - 时间戳
- `JSON` - JSON数据

### 枚举类型
```typescript
type: "ENUM('value1', 'value2', 'value3')"
```

### 字段属性
- `nullable: boolean` - 是否允许NULL
- `default: string | number` - 默认值
- `autoIncrement: boolean` - 自动递增
- `primaryKey: boolean` - 主键
- `unique: boolean` - 唯一约束
- `comment: string` - 字段注释

## 🔗 关联关系管理

### 外键约束
```typescript
foreignKeys: [
  {
    name: 'fk_products_category',
    column: 'category_id',
    referencedTable: 'product_categories',
    referencedColumn: 'id',
    onDelete: 'SET NULL',
    onUpdate: 'CASCADE'
  }
]
```

### 索引管理
```typescript
indexes: [
  {
    name: 'idx_products_sku',
    columns: ['sku'],
    unique: true
  },
  {
    name: 'idx_products_category',
    columns: ['category_id', 'status']
  }
]
```

## 🛠️ 如何添加新表或字段

### 添加新数据表

1. **编辑迁移文件**: `src/database/migration.ts`
2. **添加表定义**:

```typescript
{
  tableName: 'new_table',
  columns: [
    { name: 'id', type: 'BIGINT', autoIncrement: true, primaryKey: true },
    { name: 'name', type: 'VARCHAR(200)', nullable: false },
    { name: 'created_at', type: 'TIMESTAMP', default: 'CURRENT_TIMESTAMP' }
  ],
  indexes: [
    { name: 'idx_new_table_name', columns: ['name'] }
  ]
}
```

3. **重启服务** - 系统会自动创建新表

### 添加新字段

1. **修改表定义** - 在对应表的 `columns` 数组中添加新字段
2. **重启服务** - 系统会自动添加新字段

```typescript
// 在现有表中添加新字段
{
  name: 'new_column',
  type: 'VARCHAR(100)',
  nullable: true,
  default: "'default_value'",
  comment: '新字段说明'
}
```

## 📝 日志记录

系统会详细记录所有数据库操作：

```
[INFO] Starting database migrations...
[INFO] Creating table: users
[INFO] Table users created successfully
[INFO] Checking table structure: dropship_products
[INFO] Adding column new_field to table dropship_products
[INFO] Column new_field added to table dropship_products
[INFO] Database migrations completed successfully
```

## ⚠️ 注意事项

### 安全提醒
1. **生产环境** - 请务必备份数据库后再更新
2. **字段删除** - 系统不会自动删除字段，需手动处理
3. **数据类型变更** - 不支持自动修改现有字段类型
4. **外键约束** - 删除表前请先处理外键关系

### 最佳实践
1. **测试环境验证** - 先在测试环境验证迁移脚本
2. **渐进式更新** - 分批次添加字段，避免一次性大量变更
3. **备份策略** - 定期备份数据库
4. **监控日志** - 关注迁移过程的日志输出

## 🔍 故障排除

### 常见问题

**1. 数据库连接失败**
```
Error: connect ECONNREFUSED 127.0.0.1:3306
```
- 检查MySQL服务是否启动
- 验证数据库连接配置
- 确认数据库用户权限

**2. 表创建失败**
```
Error: Table 'xxx' already exists
```
- 检查表是否已存在
- 验证表结构定义
- 查看详细错误日志

**3. 字段添加失败**
```
Error: Duplicate column name 'xxx'
```
- 字段可能已存在
- 检查字段名称拼写
- 验证数据类型兼容性

### 手动修复

如需手动管理数据库：

```sql
-- 检查表结构
DESCRIBE table_name;

-- 添加字段
ALTER TABLE table_name ADD COLUMN new_field VARCHAR(100) NULL;

-- 创建索引
CREATE INDEX idx_name ON table_name (column_name);

-- 添加外键
ALTER TABLE table_name ADD CONSTRAINT fk_name 
FOREIGN KEY (column_name) REFERENCES other_table(id);
```

## 📞 技术支持

如遇到数据库相关问题，请：

1. 查看服务器启动日志
2. 检查数据库连接配置
3. 验证MySQL服务状态
4. 联系开发团队获取支持

---

**最后更新**: 2024年1月  
**版本**: v1.0  
**维护团队**: IKUN ERP开发团队
