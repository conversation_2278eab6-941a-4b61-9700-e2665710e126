/**
 * Products Hooks
 * 产品相关的React Hooks
 */

import { useState, useEffect, useCallback } from 'react';
import { productsApi } from '@/lib/api';
import { DropshipProduct, PaginatedResponse, SearchParams } from '@/types';

// 产品列表Hook
export function useProducts(initialParams?: SearchParams) {
  const [products, setProducts] = useState<DropshipProduct[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 20,
    totalPages: 0
  });

  // 获取产品列表
  const fetchProducts = useCallback(async (params?: SearchParams) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await productsApi.getProducts({
        page: 1,
        limit: 20,
        ...initialParams,
        ...params
      });
      
      setProducts(response.items);
      setPagination({
        total: response.total,
        page: response.page,
        limit: response.limit,
        totalPages: response.totalPages
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取产品列表失败');
      setProducts([]);
    } finally {
      setLoading(false);
    }
  }, [initialParams]);

  // 创建产品
  const createProduct = useCallback(async (productData: Partial<DropshipProduct>) => {
    setLoading(true);
    setError(null);
    
    try {
      const newProduct = await productsApi.createProduct(productData);
      setProducts(prev => [newProduct, ...prev]);
      return newProduct;
    } catch (err) {
      setError(err instanceof Error ? err.message : '创建产品失败');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // 更新产品
  const updateProduct = useCallback(async (id: number, productData: Partial<DropshipProduct>) => {
    setLoading(true);
    setError(null);
    
    try {
      const updatedProduct = await productsApi.updateProduct(id, productData);
      setProducts(prev => prev.map(p => p.id === id ? updatedProduct : p));
      return updatedProduct;
    } catch (err) {
      setError(err instanceof Error ? err.message : '更新产品失败');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // 删除产品
  const deleteProduct = useCallback(async (id: number) => {
    setLoading(true);
    // 注意：删除操作的错误不应该设置全局error状态，因为这会影响整个产品列表的显示
    // setError(null); // 删除这行，不清除全局错误状态

    try {
      const response = await productsApi.deleteProduct(id);

      // 检查业务逻辑是否成功
      if (!response.success) {
        // 创建包含完整响应数据的错误对象
        const error = new Error(response.message);
        (error as any).response = { data: response };
        throw error;
      }

      // 删除成功，从本地状态中移除
      setProducts(prev => prev.filter(p => p.id !== id));
    } catch (err) {
      // 删除操作的错误不设置全局error状态，让组件层面处理
      // setError(err instanceof Error ? err.message : '删除产品失败');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // 批量导入产品
  const importProducts = useCallback(async (productsData: Partial<DropshipProduct>[]) => {
    setLoading(true);
    setError(null);
    
    try {
      const importedProducts = await productsApi.importProducts(productsData);
      setProducts(prev => [...importedProducts, ...prev]);
      return importedProducts;
    } catch (err) {
      setError(err instanceof Error ? err.message : '导入产品失败');
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // 初始化加载
  useEffect(() => {
    fetchProducts();
  }, [fetchProducts]);

  return {
    products,
    loading,
    error,
    pagination,
    fetchProducts,
    createProduct,
    updateProduct,
    deleteProduct,
    importProducts,
    refetch: fetchProducts
  };
}

// 单个产品Hook
export function useProduct(id: number | null) {
  const [product, setProduct] = useState<DropshipProduct | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProduct = useCallback(async (productId: number) => {
    setLoading(true);
    setError(null);
    
    try {
      const productData = await productsApi.getProduct(productId);
      setProduct(productData);
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取产品详情失败');
      setProduct(null);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (id) {
      fetchProduct(id);
    }
  }, [id, fetchProduct]);

  return {
    product,
    loading,
    error,
    refetch: id ? () => fetchProduct(id) : undefined
  };
}

// 按分类获取产品Hook
export function useProductsByCategory(categoryId: number | null, params?: SearchParams) {
  const [products, setProducts] = useState<DropshipProduct[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 20,
    totalPages: 0
  });

  const fetchProductsByCategory = useCallback(async (catId: number, searchParams?: SearchParams) => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await productsApi.getProductsByCategory(catId, {
        page: 1,
        limit: 20,
        ...params,
        ...searchParams
      });
      
      setProducts(response.items);
      setPagination({
        total: response.total,
        page: response.page,
        limit: response.limit,
        totalPages: response.totalPages
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : '获取分类产品失败');
      setProducts([]);
    } finally {
      setLoading(false);
    }
  }, [params]);

  useEffect(() => {
    if (categoryId) {
      fetchProductsByCategory(categoryId);
    }
  }, [categoryId, fetchProductsByCategory]);

  return {
    products,
    loading,
    error,
    pagination,
    refetch: categoryId ? (searchParams?: SearchParams) => fetchProductsByCategory(categoryId, searchParams) : undefined
  };
}
