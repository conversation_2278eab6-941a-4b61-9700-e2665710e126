/**
 * IKUN ERP Backend Server
 * Main entry point for the application
 */

import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import morgan from 'morgan';
import compression from 'compression';
import dotenv from 'dotenv';
import path from 'path';

import { config } from '@/config/config';
import { logger } from '@/utils/logger';
import { getBeijingTimeISO } from '@/utils/time';
import { errorHandler } from '@/middleware/error/errorHandler';
import { notFoundHandler } from '@/middleware/common/notFoundHandler';
import { rateLimiter } from '@/middleware/security/rateLimiter';
import { authMiddleware } from '@/middleware/auth/authMiddleware';
import { database } from '@/database/connection';

// Import routes
import authRoutes from '@/routes/auth';
import productsRoutes from '@/routes/products';
import storesRoutes from '@/routes/stores';
import scrapingRoutes from '@/routes/scraping';
import orderRoutes from '@/routes/orders';
import uploadRoutes from '@/routes/upload';
import uploadProductRoutes from '@/routes/uploadproduct';
import analyticsRoutes from '@/routes/analytics';
import systemRoutes from '@/routes/system';
import translationRoutes from '@/routes/translation';


// Load environment variables
dotenv.config();

class Server {
  private app: express.Application;
  private port: number;

  constructor() {
    this.app = express();
    this.port = config.server.port;
    this.initializeMiddlewares();
    this.initializeRoutes();
    this.initializeErrorHandling();
  }

  private initializeMiddlewares(): void {
    // Security middleware
    this.app.use(helmet());
    
    // CORS configuration
    this.app.use(cors({
      origin: config.cors.origin,
      credentials: true,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
    }));

    // Compression middleware
    this.app.use(compression());

    // Logging middleware
    if (config.env === 'development') {
      this.app.use(morgan('dev'));
    } else {
      this.app.use(morgan('combined'));
    }

    // Body parsing middleware
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));

    // Static files
    this.app.use('/static', express.static(path.join(__dirname, '../uploads')));

    // Rate limiting
    this.app.use(rateLimiter);
  }

  private initializeRoutes(): void {
    const apiPrefix = `${config.api.prefix}/${config.api.version}`;

    // Health check
    this.app.get('/health', (_req, res) => {
      res.status(200).json({
        status: 'OK',
        timestamp: getBeijingTimeISO(),
        version: config.api.version,
        environment: config.env
      });
    });

    // Timezone test endpoint
    this.app.get('/test-timezone', async (_req, res) => {
      try {
        const dbResult = await database.query('SELECT NOW() as server_time, UTC_TIMESTAMP() as utc_time');

        res.json({
          message: 'Timezone Test Results',
          database: {
            server_time: dbResult[0].server_time,
            utc_time: dbResult[0].utc_time,
            timezone_setting: config.database.timezone
          },
          nodejs: {
            current_time: new Date(),
            iso_string: new Date().toISOString(),
            locale_string: new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
            timezone_offset: new Date().getTimezoneOffset()
          },
          environment: {
            TZ: process.env.TZ,
            NODE_ENV: config.env
          }
        });
      } catch (error) {
        res.status(500).json({
          error: 'Database connection failed',
          message: (error as Error).message
        });
      }
    });

    // API routes
    this.app.use(`${apiPrefix}/auth`, authRoutes);
    this.app.use(`${apiPrefix}`, authMiddleware, productsRoutes); // includes /categories and /products
    this.app.use(`${apiPrefix}/stores`, authMiddleware, storesRoutes);
    this.app.use(`${apiPrefix}/scraping`, authMiddleware, scrapingRoutes);
    this.app.use(`${apiPrefix}/orders`, authMiddleware, orderRoutes);
    this.app.use(`${apiPrefix}/upload`, authMiddleware, uploadRoutes);
    this.app.use(`${apiPrefix}/uploadproduct`, authMiddleware, uploadProductRoutes);
    this.app.use(`${apiPrefix}/analytics`, authMiddleware, analyticsRoutes);
    this.app.use(`${apiPrefix}/dashboard`, authMiddleware, analyticsRoutes);
    this.app.use(`${apiPrefix}/system`, authMiddleware, systemRoutes);
    this.app.use(`${apiPrefix}/translation`, authMiddleware, translationRoutes);

    // API documentation
    this.app.get(`${apiPrefix}/docs`, (_req, res) => {
      res.json({
        message: 'IKUN ERP API Documentation',
        version: config.api.version,
        endpoints: {
          auth: `${apiPrefix}/auth`,
          categories: `${apiPrefix}/categories`,
          products: `${apiPrefix}/products`,
          stores: `${apiPrefix}/stores`,
          scraping: `${apiPrefix}/scraping`,
          orders: `${apiPrefix}/orders`,
          upload: `${apiPrefix}/upload`,
          uploadproduct: `${apiPrefix}/uploadproduct`,
          analytics: `${apiPrefix}/analytics`,
          system: `${apiPrefix}/system`
        }
      });
    });
  }

  private initializeErrorHandling(): void {
    // 404 handler
    this.app.use(notFoundHandler);

    // Global error handler
    this.app.use(errorHandler);
  }

  /**
   * MT翻译服务预热
   */
  private async warmupMTTranslation(): Promise<void> {
    try {
      setTimeout(async () => {
        try {
          // 从数据库获取MT服务配置并更新到TranslationConfig
          const { SystemSettingService } = await import('@/services/system/systemSettingService');
          const { TranslationConfig } = await import('@/services/translation/config');
          const systemSettingService = new SystemSettingService();
          const translationProviders = await systemSettingService.getSettingValue('translation_providers') || {};
          const translationScenarios = await systemSettingService.getSettingValue('translation_scenarios') || {};
          const translationConfig = TranslationConfig.getInstance();
          translationConfig.updateAllConfigs(translationProviders, translationScenarios);
          const mtranConfig = translationProviders.mtran;

          if (!mtranConfig?.baseUrl) {
            return;
          }

          const controller = new AbortController();
          const timeoutId = setTimeout(() => controller.abort(), 15000);

          const response = await fetch(`${mtranConfig.baseUrl}/translate`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            signal: controller.signal,
            body: JSON.stringify({
              from: 'en',
              to: 'zh',
              text: 'Hello'
            })
          });

          clearTimeout(timeoutId);
          if (response.ok) {
            logger.info('✅ MT翻译服务预热成功');
          }
        } catch (error) {
        }
      }, 3000);
    } catch (error) {
    }
  }

  public async start(): Promise<void> {
    try {
      // Initialize database (test connection + run migrations)
      await database.initialize();
      logger.info('Database initialized successfully');

      // Start server
      this.app.listen(this.port, config.server.host, () => {
        logger.info(`🚀 IKUN ERP Server is running on http://${config.server.host}:${this.port}`);
        logger.info(`📚 API Documentation: http://${config.server.host}:${this.port}/api/${config.api.version}/docs`);
        logger.info(`🏥 Health Check: http://${config.server.host}:${this.port}/health`);
        logger.info(`🌍 Environment: ${config.env}`);
        logger.info(`📊 Database tables checked and updated automatically`);
        // MT翻译服务预热（异步执行，不阻塞服务启动）
        this.warmupMTTranslation();
      });
    } catch (error) {
      logger.error('⭐服务器启动错误:', error);
      process.exit(1);
    }
  }

  public getApp(): express.Application {
    return this.app;
  }
}

// Create and start server
const server = new Server();

// Graceful shutdown
process.on('SIGTERM', () => {
  logger.info('SIGTERM received, shutting down gracefully');
  process.exit(0);
});

process.on('SIGINT', () => {
  logger.info('SIGINT received, shutting down gracefully');
  process.exit(0);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', { promise, reason });
  process.exit(1);
});

// Start the server
if (require.main === module) {
  server.start();
}

export default server;
