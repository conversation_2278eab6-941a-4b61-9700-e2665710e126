/**
 * Authentication Controller
 * Handles user authentication, login, logout, and token management
 */

import { Request, Response, NextFunction } from 'express';
import { AuthenticatedRequest } from '@/middleware/auth/authMiddleware';
import { Models } from '@/models';
import { config } from '@/config/config';
import { logger } from '@/utils/logger';
import { BadRequestError, UnauthorizedError } from '@/middleware/error/errorHandler';
import { JWTUtils } from '@/utils/jwt';
import { getBeijingTimeISO } from '@/utils/time';
import * as jwt from 'jsonwebtoken';

class AuthController {
  private userModel = Models.User();

  // POST /api/v1/auth/login
  public async login(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { username, password } = req.body;

      if (!username || !password) {
        throw new BadRequestError('Username and password are required');
      }

      // 验证用户凭据
      const user = await this.userModel.verifyPassword(username, password);
      if (!user) {
        throw new UnauthorizedError('Invalid username or password');
      }

      // 生成JWT token
      const token = JWTUtils.generateAccessToken({
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      });

      // 生成refresh token
      const refreshToken = JWTUtils.generateRefreshToken(user.id);

      logger.info('User logged in successfully:', {
        userId: user.id,
        username: user.username
      });

      res.json({
        code: 200,
        message: 'Login successful',
        data: {
          user: {
            id: user.id,
            username: user.username,
            email: user.email,
            role: user.role,
            lastLogin: user.last_login
          },
          token,
          refreshToken,
          expiresIn: config.jwt.expiresIn
        },
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // POST /api/v1/auth/logout
  public async logout(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      // 在实际应用中，这里可以将token加入黑名单
      // 目前只是简单返回成功响应

      if (req.user) {
        logger.info('User logged out:', {
          userId: req.user.id,
          username: req.user.username
        });
      }

      res.json({
        code: 200,
        message: 'Logout successful',
        data: null,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // POST /api/v1/auth/refresh
  public async refreshToken(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { refreshToken } = req.body;

      if (!refreshToken) {
        throw new BadRequestError('Refresh token is required');
      }

      // 验证refresh token
      const decoded = JWTUtils.verifyToken(refreshToken);

      if (decoded.type !== 'refresh') {
        throw new UnauthorizedError('Invalid refresh token');
      }

      // 获取用户信息
      const user = await this.userModel.findById(decoded.id);
      if (!user || !user.is_active) {
        throw new UnauthorizedError('User not found or inactive');
      }

      // 生成新的access token
      const newToken = JWTUtils.generateAccessToken({
        id: user.id,
        username: user.username,
        email: user.email,
        role: user.role
      });

      res.json({
        code: 200,
        message: 'Token refreshed successfully',
        data: {
          token: newToken,
          expiresIn: config.jwt.expiresIn
        },
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      if (error instanceof jwt.JsonWebTokenError || error instanceof jwt.TokenExpiredError) {
        next(new UnauthorizedError('Invalid or expired refresh token'));
      } else {
        next(error);
      }
    }
  }

  // GET /api/v1/auth/profile
  public async getProfile(req: AuthenticatedRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      if (!req.user) {
        throw new UnauthorizedError('Authentication required');
      }

      // 获取完整的用户信息
      const user = await this.userModel.findById(req.user.id);
      if (!user) {
        throw new UnauthorizedError('User not found');
      }

      res.json({
        code: 200,
        message: 'Profile retrieved successfully',
        data: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          lastLogin: user.last_login,
          isActive: user.is_active,
          createdAt: user.created_at,
          updatedAt: user.updated_at
        },
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // POST /api/v1/auth/register
  public async register(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { username, email, password, role = 'user' } = req.body;

      // 创建用户
      const user = await this.userModel.createUser({
        username,
        email,
        password,
        role: role as 'admin' | 'user' | 'viewer'
      });

      logger.info('User registered successfully:', {
        userId: user.id,
        username: user.username
      });

      res.status(201).json({
        code: 201,
        message: 'User registered successfully',
        data: {
          id: user.id,
          username: user.username,
          email: user.email,
          role: user.role,
          createdAt: user.created_at
        },
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }
}

export const authController = new AuthController();
