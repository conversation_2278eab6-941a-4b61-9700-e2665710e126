/**
 * API Client
 * 前端API调用客户端
 */

// API基础配置
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api/v1';

// API响应类型
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
  timestamp: string;
}

// 分页响应类型
interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// 请求配置
interface RequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE';
  headers?: Record<string, string>;
  body?: any;
}

// API客户端类
class ApiClient {
  private baseURL: string;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
  }

  // 获取认证token
  private getAuthToken(): string | null {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('auth_token');
    }
    return null;
  }

  // 通用请求方法
  private async request<T>(endpoint: string, config: RequestConfig = {}): Promise<T> {
    const { method = 'GET', headers = {}, body } = config;

    const url = `${this.baseURL}${endpoint}`;

    const requestHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
      ...headers,
    };

    // 添加认证token
    const token = this.getAuthToken();
    if (token) {
      requestHeaders['Authorization'] = `Bearer ${token}`;
    }

    const requestConfig: RequestInit = {
      method,
      headers: requestHeaders,
    };

    if (body && method !== 'GET') {
      requestConfig.body = JSON.stringify(body);
    }

    try {
      const response = await fetch(url, requestConfig);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('HTTP Error:', response.status, response.statusText, errorData);

        // 处理401未授权错误
        if (response.status === 401) {
          // 清除本地存储的认证信息
          if (typeof window !== 'undefined') {
            localStorage.removeItem('auth_token');
            localStorage.removeItem('auth_refresh_token');
            localStorage.removeItem('auth_user');
          }

          // 如果不是登录页面，重定向到登录页面
          if (typeof window !== 'undefined' && !window.location.pathname.includes('/login')) {
            window.location.href = '/login';
          }
        }

        // 创建包含完整错误数据的错误对象
        const error = new Error(errorData.message || `HTTP ${response.status}: ${response.statusText}`);
        (error as any).response = { data: errorData };
        (error as any).status = response.status;
        throw error;
      }

      const data: ApiResponse<T> = await response.json();

      // 检查业务状态码 (200-299 都是成功状态码)
      if (data.code < 200 || data.code >= 300) {
        console.error('Business Logic Error:', data);
        throw new Error(data.message || 'Request failed');
      }

      // 对于某些操作（如删除），我们需要返回完整的响应对象而不是嵌套的data字段
      // 如果响应包含success字段，说明这是一个业务操作结果，返回完整对象
      if ('success' in data) {
        return data as T;
      }

      return data.data;
    } catch (error) {
      throw error;
    }
  }

  // GET请求
  async get<T>(endpoint: string, params?: Record<string, any>): Promise<T> {
    let url = endpoint;
    if (params) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          searchParams.append(key, String(value));
        }
      });
      const queryString = searchParams.toString();
      if (queryString) {
        url += `?${queryString}`;
      }
    }
    return this.request<T>(url, { method: 'GET' });
  }

  // POST请求
  async post<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, { method: 'POST', body: data });
  }

  // PUT请求
  async put<T>(endpoint: string, data?: any): Promise<T> {
    return this.request<T>(endpoint, { method: 'PUT', body: data });
  }

  // DELETE请求
  async delete<T>(endpoint: string): Promise<T> {
    return this.request<T>(endpoint, { method: 'DELETE' });
  }
}

// 创建API客户端实例
export const apiClient = new ApiClient(API_BASE_URL);

// 产品相关API
export const productsApi = {
  // 获取产品列表
  getProducts: (params?: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
    category_id?: number;
    source?: string;
    start_date?: string;
    end_date?: string;
  }) => apiClient.get<PaginatedResponse<any>>('/products', params),

  // 获取产品详情
  getProduct: (id: number) => apiClient.get<any>(`/products/${id}`),

  // 创建产品
  createProduct: (data: any) => apiClient.post<any>('/products', data),

  // 更新产品
  updateProduct: (id: number, data: any) => apiClient.put<any>(`/products/${id}`, data),

  // 删除产品
  deleteProduct: (id: number) => apiClient.delete<any>(`/products/${id}`),

  // 批量导入产品
  importProducts: (products: any[]) => apiClient.post<any[]>('/products/import', { products }),

  // 按分类获取产品
  getProductsByCategory: (categoryId: number, params?: any) => 
    apiClient.get<PaginatedResponse<any>>(`/products/category/${categoryId}`, params),
};

// 分类相关API
export const categoriesApi = {
  // 获取分类列表
  getCategories: (params?: {
    page?: number;
    limit?: number;
    level?: number;
    parent_id?: number;
    status?: string;
  }) => apiClient.get<PaginatedResponse<any>>('/categories', params),

  // 获取分类树
  getCategoryTree: () => apiClient.get<any[]>('/categories/tree'),

  // 获取指定层级的分类
  getCategoriesByLevel: (level: number) => apiClient.get<any[]>(`/categories/level/${level}`),

  // 获取分类详情
  getCategory: (id: number) => apiClient.get<any>(`/categories/${id}`),

  // 创建分类
  createCategory: (data: any) => apiClient.post<any>('/categories', data),

  // 更新分类
  updateCategory: (id: number, data: any) => apiClient.put<any>(`/categories/${id}`, data),

  // 更新分类状态
  updateCategoryStatus: (id: number, status: string) => 
    apiClient.put<any>(`/categories/${id}/status`, { status }),

  // 删除分类
  deleteCategory: (id: number) => apiClient.delete<any>(`/categories/${id}`),

  // 获取子分类
  getCategoryChildren: (id: number) => apiClient.get<any[]>(`/categories/${id}/children`),

  // 批量创建分类
  batchCreateCategories: (categories: any[]) => 
    apiClient.post<any[]>('/categories/batch', { categories }),
};

// 导出类型
export type { ApiResponse, PaginatedResponse };
