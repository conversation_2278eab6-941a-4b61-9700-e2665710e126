'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { AlertTriangle, Info, CheckCircle, XCircle, HelpCircle } from 'lucide-react'

export interface ConfirmDialogOptions {
  title?: string
  description?: string
  confirmText?: string
  cancelText?: string
  variant?: 'default' | 'destructive' | 'warning' | 'info' | 'success'
  icon?: boolean
}

interface ConfirmDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: () => void | Promise<void>
  onCancel?: () => void
  options?: ConfirmDialogOptions
}

const variantConfig = {
  default: {
    icon: HelpCircle,
    iconColor: 'text-blue-500',
    confirmVariant: 'default' as const,
  },
  destructive: {
    icon: AlertTriangle,
    iconColor: 'text-red-500',
    confirmVariant: 'destructive' as const,
  },
  warning: {
    icon: AlertTriangle,
    iconColor: 'text-yellow-500',
    confirmVariant: 'default' as const,
  },
  info: {
    icon: Info,
    iconColor: 'text-blue-500',
    confirmVariant: 'default' as const,
  },
  success: {
    icon: CheckCircle,
    iconColor: 'text-green-500',
    confirmVariant: 'default' as const,
  },
}

export function ConfirmDialog({
  open,
  onOpenChange,
  onConfirm,
  onCancel,
  options = {}
}: ConfirmDialogProps) {
  const [loading, setLoading] = useState(false)

  const {
    title = '确认操作',
    description = '您确定要执行此操作吗？',
    confirmText = '确认',
    cancelText = '取消',
    variant = 'default',
    icon = true,
  } = options

  const config = variantConfig[variant]
  const IconComponent = config.icon

  const handleConfirm = async () => {
    setLoading(true)
    try {
      await onConfirm()
      onOpenChange(false)
    } catch (error) {
      console.error('确认操作失败:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleCancel = () => {
    if (onCancel) {
      onCancel()
    }
    onOpenChange(false)
  }

  // 重置loading状态当对话框关闭时
  useEffect(() => {
    if (!open) {
      setLoading(false)
    }
  }, [open])

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            {icon && (
              <IconComponent className={`w-5 h-5 ${config.iconColor}`} />
            )}
            {title}
          </DialogTitle>
          {description && (
            <DialogDescription className="text-left">
              {description}
            </DialogDescription>
          )}
        </DialogHeader>
        
        <DialogFooter className="flex-row justify-end gap-2 sm:gap-2">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={loading}
          >
            {cancelText}
          </Button>
          <Button
            variant={config.confirmVariant}
            onClick={handleConfirm}
            disabled={loading}
          >
            {loading ? '处理中...' : confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// 全局确认对话框Hook
interface ConfirmState {
  open: boolean
  resolve?: (value: boolean) => void
  options?: ConfirmDialogOptions
}

let globalConfirmState: ConfirmState = { open: false }
let setGlobalConfirmState: ((state: ConfirmState) => void) | null = null

export function useConfirm() {
  const confirm = (options?: ConfirmDialogOptions): Promise<boolean> => {
    return new Promise((resolve) => {
      if (setGlobalConfirmState) {
        setGlobalConfirmState({
          open: true,
          resolve,
          options,
        })
      } else {
        // 如果没有全局状态管理器，回退到原生confirm
        resolve(window.confirm(options?.description || '您确定要执行此操作吗？'))
      }
    })
  }

  return { confirm }
}

// 全局确认对话框提供者组件
export function GlobalConfirmProvider({ children }: { children: React.ReactNode }) {
  const [state, setState] = useState<ConfirmState>({ open: false })

  // 注册全局状态设置器
  useEffect(() => {
    setGlobalConfirmState = setState
    return () => {
      setGlobalConfirmState = null
    }
  }, [])

  const handleConfirm = () => {
    if (state.resolve) {
      state.resolve(true)
    }
    setState({ open: false })
  }

  const handleCancel = () => {
    if (state.resolve) {
      state.resolve(false)
    }
    setState({ open: false })
  }

  const handleOpenChange = (open: boolean) => {
    if (!open && state.resolve) {
      state.resolve(false)
    }
    setState(prev => ({ ...prev, open }))
  }

  return (
    <>
      {children}
      <ConfirmDialog
        open={state.open}
        onOpenChange={handleOpenChange}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
        options={state.options}
      />
    </>
  )
}
