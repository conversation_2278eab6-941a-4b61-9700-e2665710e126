"use client"

import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from "@/components/ui/toast"
import { useToast } from "@/hooks/use-toast"

export function Toaster() {
  const { toasts } = useToast()

  return (
    <ToastProvider>
      {toasts.map(function ({ id, title, description, action, variant, ...props }) {
        return (
          <Toast key={id} variant={variant} {...props}>
            <div className="flex items-center justify-center text-center w-full">
              {title ? (
                <ToastTitle className="text-sm font-medium whitespace-nowrap">
                  {title}
                </ToastTitle>
              ) : description ? (
                <ToastDescription className="text-sm font-medium whitespace-nowrap">
                  {description}
                </ToastDescription>
              ) : null}
            </div>
            {action}
          </Toast>
        )
      })}
      <ToastViewport />
    </ToastProvider>
  )
}
