/**
 * Amazon Store Validation Schemas
 * Joi validation rules for Amazon store-related requests
 */

import <PERSON><PERSON> from 'joi';

export const amazonValidation = {
  // GET /api/v1/stores/amazon
  getStores: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    status: Joi.string().valid('active', 'inactive', 'suspended').optional(),
    marketplace: Joi.string().trim().max(10).optional() // US, UK, DE, etc.
  }),

  // POST /api/v1/stores/amazon
  createStore: Joi.object({
    store_name: Joi.string().trim().min(1).max(200).required(),
    marketplace: Joi.string().trim().min(2).max(10).required(), // US, UK, DE, etc.
    seller_id: Joi.string().trim().min(1).max(50).required(),
    access_key: Joi.string().trim().min(1).max(100).required(),
    secret_key: Joi.string().trim().min(1).max(100).required(),
    role_arn: Joi.string().trim().max(200).optional(),
    refresh_token: Joi.string().trim().max(500).optional(),
    status: Joi.string().valid('active', 'inactive').default('active'),
    description: Joi.string().trim().max(1000).optional().allow('')
  }),

  // PUT /api/v1/stores/amazon/:id
  updateStore: Joi.object({
    store_name: Joi.string().trim().min(1).max(200).optional(),
    access_key: Joi.string().trim().min(1).max(100).optional(),
    secret_key: Joi.string().trim().min(1).max(100).optional(),
    role_arn: Joi.string().trim().max(200).optional(),
    refresh_token: Joi.string().trim().max(500).optional(),
    status: Joi.string().valid('active', 'inactive', 'suspended').optional(),
    description: Joi.string().trim().max(1000).optional().allow('')
  }),

  // GET /api/v1/stores/amazon/:id/products
  getStoreProducts: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    status: Joi.string().valid('active', 'inactive', 'incomplete').optional(),
    search: Joi.string().trim().max(255).optional()
  }),

  // POST /api/v1/stores/amazon/:id/products/publish
  publishProducts: Joi.object({
    product_ids: Joi.array().items(
      Joi.number().integer().positive().required()
    ).min(1).max(50).required(),
    publish_options: Joi.object({
      fulfillment_channel: Joi.string().valid('FBA', 'FBM').default('FBM'),
      condition: Joi.string().valid('New', 'Used', 'Refurbished').default('New'),
      price_strategy: Joi.string().valid('fixed', 'competitive', 'dynamic').default('fixed'),
      markup_percentage: Joi.number().min(0).max(1000).optional()
    }).optional()
  }),

  // Common parameter validation
  storeId: Joi.object({
    id: Joi.number().integer().positive().required()
  })
};
