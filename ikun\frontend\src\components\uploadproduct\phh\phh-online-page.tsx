'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  RefreshCw, 
  Download, 
  Upload, 
  Search,
  Filter,
  Eye,
  Edit,
  Trash2,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react'

interface OnlineProduct {
  id: string
  sku: string
  title: string
  price: number
  stock: number
  status: 'active' | 'inactive' | 'pending'
  lastSync: string
  platformProductId: string
}

export function PHHOnlinePage() {
  const [products, setProducts] = useState<OnlineProduct[]>([])
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [statusFilter, setStatusFilter] = useState<string>('all')

  // 模拟数据
  useEffect(() => {
    setProducts([
      {
        id: '1',
        sku: 'IKUN-PHH-001',
        title: 'Professional Gaming Mouse',
        price: 59.99,
        stock: 15,
        status: 'active',
        lastSync: '2025-07-04 14:30:00',
        platformProductId: 'PHH-98765'
      },
      {
        id: '2',
        sku: 'IKUN-PHH-002',
        title: 'Mechanical Keyboard RGB',
        price: 149.99,
        stock: 8,
        status: 'active',
        lastSync: '2025-07-04 12:15:00',
        platformProductId: 'PHH-98766'
      }
    ])
  }, [])

  const handleSync = async () => {
    setLoading(true)
    // 模拟同步过程
    setTimeout(() => {
      setLoading(false)
    }, 2000)
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge variant="default" className="bg-green-100 text-green-800"><CheckCircle className="w-3 h-3 mr-1" />在售</Badge>
      case 'inactive':
        return <Badge variant="secondary"><AlertCircle className="w-3 h-3 mr-1" />下架</Badge>
      case 'pending':
        return <Badge variant="outline"><Clock className="w-3 h-3 mr-1" />待处理</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const filteredProducts = products.filter(product => {
    const matchesSearch = product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         product.title.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === 'all' || product.status === statusFilter
    return matchesSearch && matchesStatus
  })

  return (
    <div className="space-y-6">
      {/* 页面标题和操作 */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">PHH 在线同步</h1>
          <p className="text-muted-foreground">同步和管理 PHH 平台的在线产品</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={handleSync} disabled={loading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            {loading ? '同步中...' : '同步产品'}
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            导出数据
          </Button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">总产品数</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{products.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">在售产品</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {products.filter(p => p.status === 'active').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">下架产品</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {products.filter(p => p.status === 'inactive').length}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">待处理</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {products.filter(p => p.status === 'pending').length}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 搜索和筛选 */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-4">
            <div className="flex-1">
              <Label htmlFor="search">搜索产品</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  id="search"
                  placeholder="输入 SKU 或产品标题..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div>
              <Label htmlFor="status">状态筛选</Label>
              <select
                id="status"
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
              >
                <option value="all">全部状态</option>
                <option value="active">在售</option>
                <option value="inactive">下架</option>
                <option value="pending">待处理</option>
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 产品列表 */}
      <Card>
        <CardHeader>
          <CardTitle>在线产品列表</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-3">SKU</th>
                  <th className="text-left p-3">产品标题</th>
                  <th className="text-left p-3">价格</th>
                  <th className="text-left p-3">库存</th>
                  <th className="text-left p-3">状态</th>
                  <th className="text-left p-3">最后同步</th>
                  <th className="text-left p-3">平台ID</th>
                  <th className="text-left p-3">操作</th>
                </tr>
              </thead>
              <tbody>
                {filteredProducts.map((product) => (
                  <tr key={product.id} className="border-b hover:bg-gray-50">
                    <td className="p-3 font-mono text-sm">{product.sku}</td>
                    <td className="p-3">
                      <div className="max-w-xs truncate" title={product.title}>
                        {product.title}
                      </div>
                    </td>
                    <td className="p-3">€{product.price}</td>
                    <td className="p-3">
                      <span className={product.stock === 0 ? 'text-red-600' : ''}>
                        {product.stock}
                      </span>
                    </td>
                    <td className="p-3">{getStatusBadge(product.status)}</td>
                    <td className="p-3 text-sm text-muted-foreground">{product.lastSync}</td>
                    <td className="p-3 font-mono text-sm">{product.platformProductId}</td>
                    <td className="p-3">
                      <div className="flex gap-1">
                        <Button size="sm" variant="ghost">
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button size="sm" variant="ghost">
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button size="sm" variant="ghost">
                          <RefreshCw className="w-4 h-4" />
                        </Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
