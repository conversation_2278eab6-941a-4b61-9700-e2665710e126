import { useState, useCallback } from 'react'
import { Platform } from '@/types'

interface UsePlatformsReturn {
  platforms: Platform[]
  loading: boolean
  error: string | null
  fetchPlatforms: () => Promise<void>
  createPlatform: (platformData: Partial<Platform>) => Promise<Platform>
  updatePlatform: (code: string, updateData: Partial<Platform>) => Promise<Platform>
  updatePlatformStatus: (code: string, status: 'active' | 'inactive') => Promise<Platform>
}

export function usePlatforms(): UsePlatformsReturn {
  const [platforms, setPlatforms] = useState<Platform[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchPlatforms = useCallback(async () => {
    setLoading(true)
    setError(null)
    
    try {
      const token = localStorage.getItem('auth_token')
      if (!token) {
        throw new Error('请先登录')
      }

      const response = await fetch('/api/v1/stores/platforms/list', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || '获取平台列表失败')
      }

      const result: { data: Platform[] } = await response.json()
      setPlatforms(result.data)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取平台列表失败'
      setError(errorMessage)
      console.error('获取平台列表失败:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  const createPlatform = useCallback(async (platformData: Partial<Platform>): Promise<Platform> => {
    const token = localStorage.getItem('auth_token')
    if (!token) {
      throw new Error('请先登录')
    }

    const response = await fetch('/api/v1/stores/platforms', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(platformData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || '创建平台失败')
    }

    const result: { data: Platform } = await response.json()
    
    // 重新获取平台列表
    await fetchPlatforms()
    
    return result.data
  }, [fetchPlatforms])

  const updatePlatform = useCallback(async (code: string, updateData: Partial<Platform>): Promise<Platform> => {
    const token = localStorage.getItem('auth_token')
    if (!token) {
      throw new Error('请先登录')
    }

    const response = await fetch(`/api/v1/stores/platforms/${code}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updateData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || '更新平台失败')
    }

    const result: { data: Platform } = await response.json()
    
    // 重新获取平台列表
    await fetchPlatforms()
    
    return result.data
  }, [fetchPlatforms])

  const updatePlatformStatus = useCallback(async (code: string, status: 'active' | 'inactive'): Promise<Platform> => {
    const token = localStorage.getItem('auth_token')
    if (!token) {
      throw new Error('请先登录')
    }

    const response = await fetch(`/api/v1/stores/platforms/${code}/status`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ status })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || '更新平台状态失败')
    }

    const result: { data: Platform } = await response.json()
    
    // 重新获取平台列表
    await fetchPlatforms()
    
    return result.data
  }, [fetchPlatforms])

  return {
    platforms,
    loading,
    error,
    fetchPlatforms,
    createPlatform,
    updatePlatform,
    updatePlatformStatus
  }
}
