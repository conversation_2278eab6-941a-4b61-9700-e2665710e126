/**
 * Logger Utility
 * Centralized logging for IKUN ERP Backend
 */

import fs from 'fs';
import path from 'path';
import { config } from '@/config/config';

enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3
}

interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  file?: string;  //  打印日志来源文件  非常重要
  data?: any;
}

class Logger {
  private logLevel: LogLevel;
  private logFile?: string;

  constructor() {
    this.logLevel = this.getLogLevel(config.log.level);
    this.logFile = config.log.file || undefined;
    
    // Ensure log directory exists
    if (this.logFile) {
      const logDir = path.dirname(this.logFile);
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
    }
  }

  private getLogLevel(level: string): LogLevel {
    switch (level.toLowerCase()) {
      case 'error': return LogLevel.ERROR;
      case 'warn': return LogLevel.WARN;
      case 'info': return LogLevel.INFO;
      case 'debug': return LogLevel.DEBUG;
      default: return LogLevel.INFO;
    }
  }
  //  打印日志来源文件  非常重要
  private getCallerFile(): string {
    const originalFunc = Error.prepareStackTrace;
    let callerFile = '';

    try {
      const err = new Error();
      Error.prepareStackTrace = (_, stack) => stack;
      const stack = err.stack as unknown as NodeJS.CallSite[];

      // 跳过当前文件和logger相关的调用栈，找到实际调用者
      for (let i = 0; i < stack.length; i++) {
        const fileName = stack[i].getFileName();
        if (fileName && !fileName.includes('logger.ts') && !fileName.includes('node_modules')) {
          callerFile = path.basename(fileName);
          break;
        }
      }
    } catch (err) {
      // 如果获取失败，返回空字符串
    } finally {
      Error.prepareStackTrace = originalFunc;
    }

    return callerFile;
  }

  private formatMessage(level: string, message: string, data?: any): string {
    const timestamp = new Date().toISOString();
    const file = this.getCallerFile();
    const logEntry: LogEntry = {
      timestamp,
      level: level.toUpperCase(),
      message,
      ...(file && { file }),
      ...(data && { data })
    };

    if (config.env === 'development') {
      // Pretty format for development
      let formatted = `[${timestamp}] ${level.toUpperCase()}`;
      if (file) {
        formatted += ` [${file}]`;
      }
      formatted += `: ${message}`;
      if (data) {
        formatted += `\n${JSON.stringify(data, null, 2)}`;
      }
      return formatted;
    } else {
      // JSON format for production
      return JSON.stringify(logEntry);
    }
  }

  private writeLog(level: string, message: string, data?: any): void {
    const formattedMessage = this.formatMessage(level, message, data);
    
    // Console output with colors
    if (config.env === 'development') {
      const colors = {
        ERROR: '\x1b[31m', // Red
        WARN: '\x1b[33m',  // Yellow
        INFO: '\x1b[36m',  // Cyan
        DEBUG: '\x1b[90m'  // Gray
      };
      const reset = '\x1b[0m';
      const color = colors[level.toUpperCase() as keyof typeof colors] || '';
      console.log(`${color}${formattedMessage}${reset}`);
    } else {
      console.log(formattedMessage);
    }

    // File output
    if (this.logFile) {
      fs.appendFileSync(this.logFile, formattedMessage + '\n');
    }
  }

  public error(message: string, data?: any): void {
    if (this.logLevel >= LogLevel.ERROR) {
      this.writeLog('ERROR', message, data);
    }
  }

  public warn(message: string, data?: any): void {
    if (this.logLevel >= LogLevel.WARN) {
      this.writeLog('WARN', message, data);
    }
  }

  public info(message: string, data?: any): void {
    if (this.logLevel >= LogLevel.INFO) {
      this.writeLog('INFO', message, data);
    }
  }

  public debug(message: string, data?: any): void {
    if (this.logLevel >= LogLevel.DEBUG) {
      this.writeLog('DEBUG', message, data);
    }
  }

  public log(level: string, message: string, data?: any): void {
    switch (level.toLowerCase()) {
      case 'error':
        this.error(message, data);
        break;
      case 'warn':
        this.warn(message, data);
        break;
      case 'info':
        this.info(message, data);
        break;
      case 'debug':
        this.debug(message, data);
        break;
      default:
        this.info(message, data);
    }
  }
}

// Create and export logger instance
export const logger = new Logger();

// Export for testing
export { Logger, LogLevel };
