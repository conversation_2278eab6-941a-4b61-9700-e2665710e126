"use client"

import { But<PERSON> } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"

export function TestToast() {
  const { toast } = useToast()

  const showSuccessToast = () => {
    toast({
      title: "操作成功",
    })
  }

  const showErrorToast = () => {
    toast({
      title: "操作失败",
      variant: "destructive"
    })
  }

  const showDescriptionToast = () => {
    toast({
      description: "这是一个描述性消息",
    })
  }

  return (
    <div className="p-4 space-y-4">
      <h2 className="text-lg font-semibold">Toast 测试</h2>
      <div className="space-x-2">
        <Button onClick={showSuccessToast}>
          显示成功提示
        </Button>
        <Button onClick={showErrorToast} variant="destructive">
          显示错误提示
        </Button>
        <Button onClick={showDescriptionToast} variant="outline">
          显示描述提示
        </Button>
      </div>
    </div>
  )
}
