/**
 * 翻译服务类型定义
 */

// 支持的语言代码
export type LanguageCode = 'en' | 'zh' | 'lt' | 'lv' | 'et' | 'fi' | 'pt' | 'es';

// 内容类型
export type ContentType = 'title' | 'description' | 'selling_point';

// 翻译请求接口
export interface TranslationRequest {
  text: string;
  sourceLang: LanguageCode;
  targetLang: LanguageCode;
  contentType: ContentType;
  platform?: string;
  source?: string; // 翻译来源场景
}

// 翻译响应接口
export interface TranslationResponse {
  success: boolean;
  text?: string;
  error?: string;
  sourceLang?: LanguageCode;
  targetLang?: LanguageCode;
}

// 批量翻译请求
export interface BatchTranslationRequest {
  text: string;
  sourceLang: LanguageCode;
  targetLangs: LanguageCode[];
  contentType: ContentType;
  platform?: string;
  source?: string; // 翻译来源场景
}

// 批量翻译响应
export interface BatchTranslationResponse {
  success: boolean;
  translations?: Record<string, string>; // 语言代码 -> 翻译结果
  errors?: Record<string, string>; // 语言代码 -> 错误信息
}

// 产品翻译请求
export interface ProductTranslationRequest {
  title: string;
  description: string;
  sellingPoints?: string[];
  sourceLang: LanguageCode;
  targetLangs: LanguageCode[];
}

// 产品翻译响应
export interface ProductTranslationResponse {
  success: boolean;
  title?: Record<string, string>;
  description?: Record<string, string>;
  sellingPoints?: Record<string, string[]>;
  errors?: string[];
}

// 翻译提供商配置
export interface TranslationProviderConfig {
  name: string;
  baseUrl: string;
  apiKey?: string;
  retryCount: number;
  retryInterval: number;
  timeout: number;
}

// 翻译提供商接口
export interface ITranslationProvider {
  name: string;
  config: TranslationProviderConfig;
  
  // 单文本翻译
  translateText(request: TranslationRequest): Promise<TranslationResponse>;
  
  // 批量翻译
  translateBatch(request: BatchTranslationRequest): Promise<BatchTranslationResponse>;
  
  // 健康检查
  healthCheck(): Promise<boolean>;
}

// 翻译服务配置
export interface TranslationServiceConfig {
  defaultProvider: string;
  providers: Record<string, TranslationProviderConfig>;
  defaultSourceLang: LanguageCode;
  defaultTargetLangs: LanguageCode[];
  enableCache: boolean;
  cacheExpiry: number; // 缓存过期时间（秒）
}

// 翻译统计信息
export interface TranslationStats {
  totalRequests: number;
  successCount: number;
  failureCount: number;
  averageResponseTime: number;
  lastRequestTime: Date;
}

// 翻译历史记录
export interface TranslationHistory {
  id: string;
  sourceText: string;
  translatedText: string;
  sourceLang: LanguageCode;
  targetLang: LanguageCode;
  contentType: ContentType;
  provider: string;
  createdAt: Date;
  responseTime: number;
}
