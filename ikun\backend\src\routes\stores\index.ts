/**
 * Stores Module Routes Index
 * Combines all store platform routes
 */

import { Router } from 'express';
import shopifyRoutes from './shopify';
import { storeController } from '@/controllers/stores/storeController';
import { platformController } from '@/controllers/stores/platformController';
import { validateRequest } from '@/middleware/validation/validateRequest';
import { storeValidation } from '@/validations/stores/storeValidation';

const router = Router();

// Mount platform-specific routes
router.use('/shopify', shopifyRoutes);

// GET /api/v1/stores - Get all stores overview
router.get('/',
  validateRequest({ query: storeValidation.getStores }),
  storeController.getStores
);

// POST /api/v1/stores - Create store
router.post('/',
  validateRequest({ body: storeValidation.createStore }),
  storeController.createStore
);

// GET /api/v1/stores/:id - Get store details
router.get('/:id',
  validateRequest({ params: storeValidation.storeId }),
  storeController.getStoreById
);

// PUT /api/v1/stores/:id - Update store
router.put('/:id',
  validateRequest({
    params: storeValidation.storeId,
    body: storeValidation.updateStore
  }),
  storeController.updateStore
);

// DELETE /api/v1/stores/:id - Delete store
router.delete('/:id',
  validateRequest({ params: storeValidation.storeId }),
  storeController.deleteStore
);

// Platform routes
router.get('/platforms/list', platformController.getPlatforms);
router.get('/platforms/:code', platformController.getPlatformByCode);
router.post('/platforms', platformController.createPlatform);
router.put('/platforms/:code', platformController.updatePlatform);
router.put('/platforms/:code/status', platformController.updatePlatformStatus);

// Store status update route
router.put('/:id/status',
  validateRequest({
    params: storeValidation.storeId,
    body: storeValidation.updateStatus
  }),
  storeController.updateStoreStatus
);

// Store sync route
router.post('/:id/sync',
  validateRequest({ params: storeValidation.storeId }),
  storeController.syncStore
);

export default router;
