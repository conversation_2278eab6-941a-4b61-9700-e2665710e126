/**
 * Application Configuration
 * Centralized configuration management for IKUN ERP Backend
 */

import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

interface DatabaseConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  database: string;
  connectionLimit: number;
  timeout: number;
  timezone: string;
}

interface RedisConfig {
  host: string;
  port: number;
  password?: string | undefined;
  db: number;
}

interface JWTConfig {
  secret: string;
  expiresIn: string;
  refreshExpiresIn: string;
}

interface ServerConfig {
  host: string;
  port: number;
}

interface UploadConfig {
  maxSize: number;
  allowedTypes: string[];
  path: string;
  staticUrl: string;
}

interface APIConfig {
  version: string;
  prefix: string;
}

interface CORSConfig {
  origin: string | string[];
}

interface RateLimitConfig {
  windowMs: number;
  max: number;
}

interface LogConfig {
  level: string;
  file?: string | undefined;
}

interface Config {
  env: string;
  server: ServerConfig;
  database: DatabaseConfig;
  redis: RedisConfig;
  jwt: JWTConfig;
  upload: UploadConfig;
  api: APIConfig;
  cors: CORSConfig;
  rateLimit: RateLimitConfig;
  log: LogConfig;
}

export const config: Config = {
  env: process.env.NODE_ENV || 'development',

  server: {
    host: process.env.HOST || 'localhost',
    port: parseInt(process.env.PORT || '3001', 10)
  },

  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306', 10),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '123456',
    database: process.env.DB_NAME || 'ikun',
    connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '10', 10),
    timeout: parseInt(process.env.DB_TIMEOUT || '60000', 10),
    timezone: process.env.DB_TIMEZONE || '+08:00'
  },

  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379', 10),
    password: process.env.REDIS_PASSWORD || undefined,
    db: parseInt(process.env.REDIS_DB || '0', 10)
  },

  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '7d',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d'
  },

  upload: {
    maxSize: parseInt(process.env.UPLOAD_MAX_SIZE || '10485760', 10), // 10MB
    allowedTypes: (process.env.UPLOAD_ALLOWED_TYPES || 'image/jpeg,image/png,image/webp,image/gif').split(','),
    path: process.env.UPLOAD_PATH || './uploads',
    staticUrl: process.env.STATIC_URL || '/static'
  },

  api: {
    version: process.env.API_VERSION || 'v1',
    prefix: process.env.API_PREFIX || '/api'
  },

  cors: {
    origin: process.env.CORS_ORIGIN || 'http://localhost:3000'
  },

  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW || '15', 10) * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX || '10000', 10) // limit each IP to 100 requests per windowMs
  },

  log: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || undefined
  }
};

// Validation
if (!config.jwt.secret || config.jwt.secret === 'your-super-secret-jwt-key') {
  if (config.env === 'production') {
    throw new Error('JWT_SECRET must be set in production environment');
  }
  console.warn('⚠️  Warning: Using default JWT secret. Please set JWT_SECRET in production.');
}

if (!config.database.password && config.env === 'production') {
  throw new Error('Database password must be set in production environment');
}

export default config;
