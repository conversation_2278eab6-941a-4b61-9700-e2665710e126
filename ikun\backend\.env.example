# IKUN ERP Backend Environment Variables

# Server Configuration
NODE_ENV=development
PORT=3001
HOST=localhost

# Database Configuration (MySQL)
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=123456
DB_NAME=ikun
DB_CONNECTION_LIMIT=10
DB_TIMEOUT=60000
DB_TIMEZONE=+08:00

# Redis Configuration (Cloud deployment only)
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_PASSWORD=
# REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/webp,image/gif
UPLOAD_PATH=./uploads
STATIC_URL=/static

# API Configuration
API_VERSION=v1
API_PREFIX=/api
CORS_ORIGIN=http://localhost:3000
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# External Services (Cloud deployment)
# AWS Configuration
# AWS_ACCESS_KEY_ID=
# AWS_SECRET_ACCESS_KEY=
# AWS_REGION=
# AWS_S3_BUCKET=

# Email Service (SendGrid)
# SENDGRID_API_KEY=
# EMAIL_FROM=<EMAIL>

# Monitoring (Sentry)
# SENTRY_DSN=

# Platform API Keys (for integrations)
# AMAZON_API_KEY=
# AMAZON_API_SECRET=
# EBAY_API_KEY=
# EBAY_API_SECRET=
# SHOPIFY_API_KEY=
# SHOPIFY_API_SECRET=

# Scraping Configuration
SCRAPING_TIMEOUT=30000
SCRAPING_MAX_RETRIES=3
SCRAPING_DELAY=1000

# Cache Configuration
CACHE_TTL=3600
CACHE_MAX_KEYS=1000

# Translation Service Configuration
TRANSLATION_DEFAULT_PROVIDER=mtran
TRANSLATION_MTRAN_URL=http://localhost:5000
TRANSLATION_DEEPSEEK_URL=https://api.deepseek.com
TRANSLATION_DEEPSEEK_API_KEY=
TRANSLATION_DEFAULT_SOURCE_LANG=en
TRANSLATION_DEFAULT_TARGET_LANGS=lt,lv,et,fi
TRANSLATION_RETRY_COUNT=3
TRANSLATION_RETRY_INTERVAL=5
TRANSLATION_TIMEOUT=30000
TRANSLATION_ENABLE_CACHE=true
TRANSLATION_CACHE_EXPIRY=3600
