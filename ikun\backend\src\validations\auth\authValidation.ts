/**
 * Authentication Validation Schemas
 * Joi validation rules for authentication-related requests
 */

import <PERSON><PERSON> from 'joi';

export const authValidation = {
  // POST /api/v1/auth/login
  login: Joi.object({
    username: Joi.string().trim().min(3).max(50).required(),
    password: Joi.string().min(6).max(100).required()
  }),

  // POST /api/v1/auth/register
  register: Joi.object({
    username: Joi.string().trim().min(3).max(50).required()
      .pattern(/^[a-zA-Z0-9_]+$/)
      .messages({
        'string.pattern.base': 'Username must contain only letters, numbers, and underscores'
      }),
    email: Joi.string().email().required(),
    password: Joi.string().min(8).max(100).required()
      .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      .messages({
        'string.pattern.base': 'Password must contain at least one lowercase letter, one uppercase letter, and one number'
      }),
    role: Joi.string().valid('admin', 'user', 'viewer').default('user')
  }),

  // POST /api/v1/auth/refresh
  refresh: Joi.object({
    refreshToken: Joi.string().required()
  })
};
