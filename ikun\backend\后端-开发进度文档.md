# IKUN ERP 后端开发进度文档

## 📋 项目概览

**项目名称**: IKUN ERP Backend API Server
**技术栈**: Node.js + Express + TypeScript + MySQL
**开发模式**: 分层架构 (Routes → Controllers → Services → Models → Database)
**API版本**: v1
**时区设置**: 北京时间 (+08:00)
**数据库管理**: 自动迁移和表结构管理

## 🏗️ 项目架构

### 技术栈选择
- **运行环境**: Node.js 18+
- **Web框架**: Express.js
- **编程语言**: TypeScript
- **数据库**: MySQL 8.0
- **ORM**: 原生SQL (mysql2)
- **认证**: JWT
- **验证**: Joi
- **日志**: 自定义Logger
- **测试**: Jest + Supertest

### 项目结构 (模块化设计)
```
backend/
├── src/
│   ├── config/                    # 配置文件
│   ├── controllers/               # 控制器层 (按功能模块分组)
│   │   ├── auth/                 # 认证控制器
│   │   ├── products/             # 产品相关控制器
│   │   └── stores/               # 店铺管理控制器
│   │       ├── amazon/           # Amazon平台控制器
│   │       ├── ebay/             # eBay平台控制器
│   │       └── shopify/          # Shopify平台控制器
│   ├── services/                  # 业务逻辑层 (按功能模块分组)
│   │   ├── products/             # 产品相关服务
│   │   └── stores/               # 店铺管理服务
│   ├── models/                    # 数据模型层 (TODO)
│   ├── routes/                    # 路由定义 (按功能模块分组)
│   │   ├── auth/                 # 认证路由
│   │   ├── products/             # 产品相关路由
│   │   ├── stores/               # 店铺管理路由
│   │   ├── scraping/             # 采集任务路由
│   │   ├── orders/               # 订单管理路由
│   │   ├── upload/               # 文件上传路由
│   │   ├── analytics/            # 统计分析路由
│   │   └── system/               # 系统配置路由
│   ├── middleware/                # 中间件 (按类型分组)
│   │   ├── auth/                 # 认证中间件
│   │   ├── validation/           # 验证中间件
│   │   ├── error/                # 错误处理中间件
│   │   ├── security/             # 安全中间件
│   │   └── common/               # 通用中间件
│   ├── validations/               # 请求验证规则 (按功能模块分组)
│   │   ├── auth/                 # 认证验证规则
│   │   ├── products/             # 产品验证规则
│   │   └── stores/               # 店铺验证规则
│   ├── utils/                     # 工具函数
│   ├── types/                     # TypeScript类型定义
│   ├── database/                  # 数据库连接和迁移
│   └── server.ts                  # 应用入口
├── tests/                         # 测试文件 (TODO)
├── uploads/                       # 文件上传目录 (TODO)
├── logs/                          # 日志文件目录
└── dist/                          # 编译输出目录
```

## ✅ 已完成功能 (95%)

### 1. 基础架构 (100%)
- ✅ **项目初始化**
  - ✅ package.json 配置
  - ✅ TypeScript 配置
  - ✅ ESLint 配置
  - ✅ 环境变量配置
  - ✅ Git 忽略文件

- ✅ **核心服务**
  - ✅ Express 服务器搭建
  - ✅ 中间件配置 (CORS, Helmet, Morgan, Compression)
  - ✅ 错误处理中间件
  - ✅ 请求验证中间件
  - ✅ 认证中间件框架
  - ✅ 限流中间件

- ✅ **数据库连接**
  - ✅ MySQL 连接池配置
  - ✅ 数据库连接管理
  - ✅ 事务支持
  - ✅ 查询工具函数
  - ✅ 自动数据库迁移系统
  - ✅ 表结构自动创建和更新
  - ✅ 北京时间配置

- ✅ **工具函数**
  - ✅ 日志系统 (支持文件和控制台输出)
  - ✅ 配置管理
  - ✅ 类型定义

### 2. 模块化路由架构 (100%)
- ✅ **认证模块** (`/routes/auth/`)
  - ✅ 用户登录/登出路由
  - ✅ Token管理路由
  - ✅ 用户注册路由

- ✅ **产品管理模块** (`/routes/products/`)
  - ✅ 产品目录路由 (/categories)
  - ✅ 产品管理路由 (/products)
  - ✅ 模块索引文件整合

- ✅ **店铺管理模块** (`/routes/stores/`)
  - ✅ 统一店铺管理路由 (/stores)
  - ✅ 平台管理路由 (/platforms)
  - ✅ 多平台架构设计
  - ✅ 平台特定配置支持

- ✅ **其他功能模块** (框架完成)
  - ✅ 采集任务路由 (/scraping)
  - ✅ 订单管理路由 (/orders)
  - ✅ 文件上传路由 (/upload)
  - ✅ 统计分析路由 (/analytics)
  - ✅ 系统配置路由 (/system)

### 3. 产品目录管理 (80%)
- ✅ **控制器层**
  - ✅ 获取目录列表 (支持分页、筛选)
  - ✅ 获取目录树结构
  - ✅ 按层级获取目录
  - ✅ 创建目录
  - ✅ 批量创建目录
  - ✅ 获取目录详情
  - ✅ 更新目录
  - ✅ 更新目录状态
  - ✅ 删除目录
  - ✅ 获取子目录

- ✅ **服务层**
  - ✅ 完整的业务逻辑实现
  - ✅ 数据验证和约束检查
  - ✅ 目录路径自动生成
  - ✅ 树状结构构建
  - ✅ 错误处理

- 🔄 **待完成**
  - [ ] 请求验证规则 (Joi schemas)
  - [ ] 单元测试
  - [ ] 集成测试

### 4. 产品管理 (95%)
- ✅ **控制器层**
  - ✅ 获取产品列表 (支持分页、搜索、筛选)
  - ✅ 创建产品
  - ✅ 批量导入产品
  - ✅ 按目录获取产品
  - ✅ 获取产品详情
  - ✅ 更新产品
  - ✅ 删除产品
  - ✅ **Excel 导入/导出功能**
    - ✅ 模板下载 (GET /template)
    - ✅ 文件上传导入 (POST /import)
    - ✅ 数据导出 (GET /export)

- ✅ **服务层**
  - ✅ 完整业务逻辑实现
  - ✅ 与目录表关联查询
  - ✅ SKU/EAN唯一性验证
  - ✅ 高级搜索功能
  - ✅ **Excel 处理服务**
    - ✅ 动态模板生成 (包含分类数据和下拉选择)
    - ✅ 多文件解析处理
    - ✅ 数据验证和错误收集
    - ✅ 批量操作支持 (update/delete)
    - ✅ 格式化导出功能

### 5. 数据模型层 (100%)
- ✅ **基础模型类** (已完成)
  - ✅ BaseModel抽象类 - 提供CRUD、分页、事务等基础功能
  - ✅ ModelInterface接口定义 - 关联关系、软删除、树形结构等接口
  - ✅ 关联关系处理 - 支持一对一、一对多、多对多关系

- ✅ **产品相关模型** (已完成)
  - ✅ CategoryModel (产品目录模型) - 支持三级分类、树形结构、路径生成
  - ✅ ProductModel (产品模型) - 支持SKU/EAN唯一性、分类关联、搜索功能

- ✅ **用户相关模型** (已完成)
  - ✅ UserModel (用户模型) - 支持密码加密、权限验证、批量操作

- ✅ **店铺相关模型** (已完成)
  - ✅ StoreModel (店铺基础模型) - 抽象基类，定义通用店铺功能
  - ✅ AmazonStoreModel (Amazon店铺模型) - Amazon SP-API集成、配额管理
  - ✅ EbayStoreModel (eBay店铺模型) - eBay Trading API集成、刊登管理

- ✅ **订单相关模型** (已完成)
  - ✅ OrderModel (订单模型) - 完整订单字段定义、多平台订单管理、状态跟踪、统计分析

- ✅ **平台和店铺模型** (已完成)
  - ✅ PlatformModel (平台模型) - 独立平台配置管理
  - ✅ StoreModel (统一店铺模型) - 多平台店铺统一管理、平台特定配置

- ✅ **模型工厂和索引** (已完成)
  - ✅ ModelFactory - 单例模式管理模型实例
  - ✅ 统一导出接口 - 便于其他层调用

### 6. 数据库设计 (95%)
- ✅ **核心数据表设计**
  - ✅ 产品分类表 (product_categories) - 三级分类、树形结构
  - ✅ 铺货产品表 (product_dropship) - 完整产品信息、SKU/EAN管理
  - ✅ 订单表 (orders_updata) - 完整订单字段、客户信息、商品信息
  - ✅ 平台配置表 (stores_platforms) - 独立平台管理
  - ✅ 统一店铺表 (stores_updata) - 多平台店铺统一管理
  - ✅ 用户表 (users) - 用户权限管理

- ✅ **数据库特性**
  - ✅ 自动迁移系统 - 启动时自动创建/更新表结构
  - ✅ 外键关系管理 - 数据完整性保证
  - ✅ 索引优化 - 查询性能优化
  - ✅ 时区配置 - 统一使用北京时间
  - ✅ 自增ID配置 - 订单从10001开始，店铺从100开始

- ✅ **初始化数据**
  - ✅ 平台基础数据 - Amazon、eBay、PHH、Shopify、AliExpress平台自动初始化
  - ✅ 配置字段定义 - 各平台特定字段要求

### 7. 店铺管理系统 (95%)
- ✅ **控制器层**
  - ✅ 获取店铺列表 (支持分页、筛选、搜索)
  - ✅ 创建店铺
  - ✅ 获取店铺详情
  - ✅ 更新店铺
  - ✅ 删除店铺
  - ✅ 更新店铺状态
  - ✅ 店铺同步功能

- ✅ **平台管理控制器**
  - ✅ 获取平台列表
  - ✅ 获取平台详情
  - ✅ 创建平台
  - ✅ 更新平台
  - ✅ 更新平台状态

- ✅ **服务层**
  - ✅ 店铺业务逻辑实现
  - ✅ 平台管理业务逻辑
  - ✅ 多平台配置支持
  - ✅ 数据验证和约束检查
  - ✅ 错误处理

- ✅ **验证规则**
  - ✅ 店铺创建验证
  - ✅ 店铺更新验证
  - ✅ 状态更新验证
  - ✅ 参数验证规则

## 🔄 进行中功能

### 1. 产品服务层开发
- 实现产品CRUD业务逻辑
- 与产品目录的关联查询
- 产品搜索和筛选功能
- 数据验证和约束检查

### 2. 请求验证规则
- 创建Joi验证规则
- 集成到路由中
- 错误消息本地化

## 📋 待开发功能

### 1. 认证系统 (100%) ✅ 已完成
- ✅ 用户登录/登出
- ✅ JWT Token 生成和验证
- ✅ 刷新Token机制
- ✅ 用户注册 (管理员)
- ✅ 密码加密和验证 (bcrypt)
- ✅ 角色权限控制
- ✅ 认证中间件 (强制和可选)
- ✅ Token安全验证增强
- ✅ 种子用户数据 (admin/admin123456)

### 2. 文件上传 (0%)
- [ ] 图片上传功能
- [ ] 文件类型验证
- [ ] 文件大小限制
- [ ] 批量上传
- [ ] 文件存储管理
- [ ] 云存储集成 (可选)

### 3. 采集任务管理 (0%)
- [ ] 采集任务创建
- [ ] 任务状态管理
- [ ] 采集结果处理
- [ ] Python脚本集成
- [ ] 多平台适配

### 4. 订单管理 (0%)
- [ ] 订单CRUD操作
- [ ] 平台订单同步
- [ ] 订单状态更新
- [ ] 发货管理
- [ ] 订单统计

### 5. 平台集成 (0%)
- [ ] 平台配置管理
- [ ] API密钥管理
- [ ] 平台数据同步
- [ ] 错误重试机制

### 6. 统计分析 (0%)
- [ ] 仪表板数据
- [ ] 产品统计
- [ ] 订单统计
- [ ] 收入分析
- [ ] 数据可视化接口

### 7. 系统配置 (0%)
- [ ] 系统参数配置
- [ ] 用户偏好设置
- [ ] 系统监控
- [ ] 日志管理

### 8. 测试覆盖 (0%)
- [ ] 单元测试
- [ ] 集成测试
- [ ] API测试
- [ ] 性能测试

## 🛠️ IKUN ERP 后端开发规范

### 📋 概述

本规范定义了IKUN ERP后端项目的开发标准，确保代码质量、可维护性和团队协作效率。

### 🏗️ 架构规范

#### 分层架构
```
Controller Layer (控制器层)
    ↓
Service Layer (业务逻辑层)
    ↓
Database Layer (数据访问层)
```

#### 职责分离
- **Controller**: 处理HTTP请求/响应，参数验证，调用Service
- **Service**: 业务逻辑处理，数据转换，事务管理
- **Database**: 数据库操作，SQL查询，连接管理

### 📁 文件组织规范

#### 文件命名规范
- **文件名**: 使用camelCase，如 `userController.ts`
- **类名**: 使用PascalCase，如 `UserController`
- **函数名**: 使用camelCase，如 `getUserById`
- **常量**: 使用UPPER_SNAKE_CASE，如 `MAX_FILE_SIZE`
- **接口**: 使用PascalCase，如 `UserInterface`

### 💻 代码规范

#### TypeScript规范
```typescript
// ✅ 正确示例
interface CreateUserRequest {
  username: string;
  email: string;
  password: string;
}

class UserController {
  public async createUser(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const userData: CreateUserRequest = req.body;
      const user = await userService.createUser(userData);

      res.status(201).json({
        code: 201,
        message: 'User created successfully',
        data: user,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      next(error);
    }
  }
}
```

#### 错误处理规范
```typescript
// ✅ 使用自定义错误类
import { NotFoundError, ValidationError } from '@/middleware/errorHandler';

// 在Service层抛出业务错误
if (!user) {
  throw new NotFoundError('User not found');
}

// 在Controller层统一处理
try {
  // 业务逻辑
} catch (error) {
  next(error); // 传递给错误处理中间件
}
```

#### 异步处理规范
```typescript
// ✅ 使用async/await
public async getUsers(): Promise<User[]> {
  try {
    const users = await database.query<User>('SELECT * FROM users');
    return users;
  } catch (error) {
    logger.error('Failed to get users:', error);
    throw error;
  }
}

// ❌ 避免使用Promise.then
```

### 🛣️ API设计规范

#### RESTful API规范
```
GET    /api/v1/users          # 获取用户列表
POST   /api/v1/users          # 创建用户
GET    /api/v1/users/:id      # 获取用户详情
PUT    /api/v1/users/:id      # 更新用户
DELETE /api/v1/users/:id      # 删除用户
```

#### 响应格式规范
```typescript
// ✅ 成功响应
{
  "code": 200,
  "message": "Success",
  "data": {
    // 响应数据
  },
  "timestamp": "2024-01-01T00:00:00Z"
}

// ✅ 分页响应
{
  "code": 200,
  "message": "Success",
  "data": {
    "items": [],
    "total": 100,
    "page": 1,
    "limit": 20,
    "totalPages": 5
  },
  "timestamp": "2024-01-01T00:00:00Z"
}

// ✅ 错误响应
{
  "code": 400,
  "message": "Validation failed",
  "error": "VALIDATION_ERROR",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### 状态码规范
- `200`: 请求成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `409`: 资源冲突
- `500`: 服务器内部错误

### 🗄️ 数据库规范

#### 数据库表命名规范

**IKUN ERP 数据库表命名标准**

为了便于长期维护和清晰的项目管理，IKUN ERP 项目采用以下数据库表命名规范：

##### 表名分类前缀
- **`product_*`**: 产品相关表
  - `product_categories` - 产品分类表
  - `product_dropship` - 铺货产品表
  - `product_pod` - POD产品表（未来扩展）

- **`stores_*`**: 店铺相关表
  - `stores_updata` - 统一店铺表
  - `stores_platforms` - 平台配置表
  - `stores_amazon` - Amazon店铺表（未来扩展）
  - `stores_ebay` - eBay店铺表（未来扩展）

- **`orders_*`**: 订单相关表
  - `orders_updata` - 统一订单表
  - `orders_amazon` - Amazon订单表（未来扩展）
  - `orders_ebay` - eBay订单表（未来扩展）

- **`tasks_*`**: 任务相关表
  - `tasks_scraping` - 采集任务表（未来扩展）
  - `tasks_upload` - 上传任务表（未来扩展）

- **`users`**: 用户管理表（单表，无前缀）

##### 命名规范优势
1. **模块化管理**: 通过前缀快速识别表的功能归属
2. **扩展性强**: 便于添加新的相关表而不产生命名冲突
3. **维护友好**: 数据库管理工具中表会按前缀自动分组
4. **避免冲突**: 减少与系统保留字或第三方库的命名冲突

##### 表名修改记录
**2025年6月23日更新**：
- `stores` → `stores_updata`
- `platforms` → `stores_platforms`
- `dropship_products` → `product_dropship`
- `orders` → `orders_updata`

#### 数据库文件管理
**重要文件位置：**
- **数据表定义**: `src/database/migration.ts` - 所有数据表结构定义和自动迁移逻辑
- **测试数据**: `src/database/seedData.ts` - 开发和测试环境的种子数据
- **数据库连接**: `src/database/connection.ts` - 数据库连接配置和管理

**修改数据表结构流程：**
1. 在 `migration.ts` 中修改对应表的 `TableSchema` 定义
2. 重启后端服务，系统会自动检测并添加新字段
3. 如需修改测试数据，在 `seedData.ts` 中更新对应的种子数据
4. 不要直接修改 `database/init.sql` 文件，该文件仅作为参考

#### 查询规范
```typescript
// ✅ 使用参数化查询
const users = await database.query<User>(
  'SELECT * FROM users WHERE status = ? AND created_at > ?',
  ['active', startDate]
);

// ❌ 避免字符串拼接
const sql = `SELECT * FROM users WHERE name = '${name}'`; // SQL注入风险
```

#### ⚠️ MySQL参数化查询注意事项
**重要：避免 ER_WRONG_ARGUMENTS 错误**

```typescript
// ❌ 错误做法：LIMIT/OFFSET使用参数化查询可能导致参数类型错误
const sql = `
  SELECT * FROM products
  WHERE status = ?
  ORDER BY created_at DESC
  LIMIT ? OFFSET ?
`;
const params = ['active', limit, offset]; // 可能导致 ER_WRONG_ARGUMENTS

// ✅ 正确做法：LIMIT/OFFSET使用字符串拼接，确保数值类型正确
const sql = `
  SELECT * FROM products
  WHERE status = ?
  ORDER BY created_at DESC
  LIMIT ${Number(limit)} OFFSET ${Number(offset)}
`;
const params = ['active']; // 只传递WHERE条件的参数

// ✅ 或者确保参数类型正确
const finalParams = [...params, Number(limit), Number(offset)];
```

**错误原因分析：**
- MySQL2驱动对参数类型要求严格
- LIMIT/OFFSET参数必须是数值类型
- 参数数量必须与SQL中的占位符完全匹配
- 复杂JOIN查询时更容易出现参数不匹配问题

**最佳实践：**
1. 对于LIMIT/OFFSET，优先使用字符串拼接并确保类型转换
2. 复杂查询时，将JOIN查询拆分为多个简单查询
3. 添加调试日志记录SQL和参数，便于排查问题
4. 在开发阶段充分测试各种查询条件组合

#### 事务处理
```typescript
// ✅ 使用事务确保数据一致性
public async transferMoney(fromId: number, toId: number, amount: number): Promise<void> {
  await database.transaction(async (connection) => {
    await connection.execute(
      'UPDATE accounts SET balance = balance - ? WHERE id = ?',
      [amount, fromId]
    );

    await connection.execute(
      'UPDATE accounts SET balance = balance + ? WHERE id = ?',
      [amount, toId]
    );
  });
}
```

#### 批量数据处理优化

**问题**：Excel导入大量数据时，逐条插入性能极差，万条数据可能需要几分钟。

**解决方案**：使用批量插入和事务优化

```typescript
// ❌ 错误：逐条插入
for (const product of products) {
  await productService.createProduct(product);
}

// ✅ 正确：批量插入
public async batchImport(products: Partial<DropshipProduct>[]): Promise<DropshipProduct[]> {
  const BATCH_SIZE = 50; // 每批50条，避免SQL过长

  await this.transaction(async (connection) => {
    for (let i = 0; i < products.length; i += BATCH_SIZE) {
      const batch = products.slice(i, i + BATCH_SIZE);

      // 构建批量插入SQL
      const fields = Object.keys(batch[0]);
      const placeholders = fields.map(() => '?').join(', ');
      const valuesSql = batch.map(() => `(${placeholders})`).join(', ');

      const sql = `INSERT INTO ${this.tableName} (${fields.join(', ')}) VALUES ${valuesSql}`;

      // 准备参数
      const params: any[] = [];
      batch.forEach(item => {
        fields.forEach(field => params.push(item[field]));
      });

      await connection.execute(sql, params);
    }
  });
}
```

**性能提升**：
- 逐条插入：10,000条数据约需要5-10分钟
- 批量插入：10,000条数据约需要10-30秒
- 性能提升：10-30倍

**最佳实践**：
1. 批量大小控制在50-100条，避免SQL语句过长
2. 使用事务确保数据一致性
3. 添加错误处理和回退机制
4. 记录详细的性能日志

#### API验证规则设计

**问题**：不同API端点需要不同的查询参数，但共用验证规则会导致参数冲突。

**解决方案**：为特殊功能创建专门的验证规则

```typescript
// ❌ 错误：导出API使用通用验证规则
router.get('/export',
  validateRequest({ query: productValidation.getProducts }), // 不支持export_all参数
  productController.exportToExcel
);

// ✅ 正确：为导出功能创建专门验证规则
export const productValidation = {
  // 通用产品查询
  getProducts: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    // ... 其他通用参数
  }),

  // 导出专用验证
  exportProducts: Joi.object({
    // 继承通用参数
    ...getProducts.describe().keys,
    // 添加导出专用参数
    export_all: Joi.string().valid('true', 'false').optional(),
    selected_ids: Joi.alternatives().try(
      Joi.string().pattern(/^\d+(,\d+)*$/),
      Joi.array().items(Joi.string().pattern(/^\d+$/))
    ).optional()
  })
};
```

**最佳实践**：
1. 为不同功能创建专门的验证规则
2. 使用Joi.alternatives()支持多种参数格式
3. 合理使用正则表达式验证参数格式
4. 记录验证规则的设计原因

#### 连接管理
```typescript
// ✅ 使用连接池
const result = await database.query(sql, params);

// ❌ 避免手动管理连接
const connection = await mysql.createConnection(config);
// ... 容易忘记释放连接
```

### 🔐 安全规范

#### 输入验证
```typescript
// ✅ 使用Joi验证
import Joi from 'joi';

const createUserSchema = Joi.object({
  username: Joi.string().alphanum().min(3).max(30).required(),
  email: Joi.string().email().required(),
  password: Joi.string().min(8).required()
});

// 在路由中使用
router.post('/users',
  validateRequest({ body: createUserSchema }),
  userController.createUser
);
```

#### 认证授权
```typescript
// ✅ JWT认证
import jwt from 'jsonwebtoken';

const token = jwt.sign(
  { id: user.id, role: user.role },
  config.jwt.secret,
  { expiresIn: config.jwt.expiresIn }
);

// ✅ 角色权限检查
router.delete('/users/:id',
  authMiddleware,
  requireRole(['admin']),
  userController.deleteUser
);
```

#### 敏感信息处理
```typescript
// ✅ 密码加密
import bcrypt from 'bcryptjs';

const hashedPassword = await bcrypt.hash(password, 12);

// ✅ 响应中排除敏感字段
const userResponse = {
  id: user.id,
  username: user.username,
  email: user.email,
  // 不包含password_hash
};
```

### 📝 日志规范

#### 日志级别
- `ERROR`: 错误信息，需要立即关注
- `WARN`: 警告信息，可能的问题
- `INFO`: 一般信息，重要的业务操作
- `DEBUG`: 调试信息，开发阶段使用

#### 日志格式
```typescript
// ✅ 结构化日志
logger.info('User created', {
  userId: user.id,
  username: user.username,
  ip: req.ip,
  userAgent: req.get('User-Agent')
});

// ✅ 错误日志
logger.error('Database connection failed', {
  error: error.message,
  stack: error.stack,
  query: sql,
  params: params
});
```

### 🧪 测试规范

#### 测试文件组织
```
tests/
├── unit/           # 单元测试
│   ├── services/
│   └── utils/
├── integration/    # 集成测试
│   └── api/
└── fixtures/       # 测试数据
```

#### 测试命名
```typescript
// ✅ 描述性测试名称
describe('UserService', () => {
  describe('createUser', () => {
    it('should create user with valid data', async () => {
      // 测试逻辑
    });

    it('should throw error when email already exists', async () => {
      // 测试逻辑
    });
  });
});
```

#### 测试覆盖率
- 单元测试覆盖率 > 80%
- 集成测试覆盖核心API
- 关键业务逻辑 100% 覆盖

### 📚 文档规范

#### 代码注释
```typescript
/**
 * Create a new user account
 * @param userData - User registration data
 * @returns Created user object (without password)
 * @throws {ValidationError} When user data is invalid
 * @throws {ConflictError} When email already exists
 */
public async createUser(userData: CreateUserRequest): Promise<User> {
  // 实现逻辑
}
```

#### API文档
- 使用JSDoc注释
- 包含请求/响应示例
- 说明错误情况
- 定期更新

### 🔄 版本控制规范

#### Git提交规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建工具、依赖更新

示例:
feat: add user authentication
fix: resolve database connection issue
docs: update API documentation
```

#### 分支管理
- `main`: 生产环境代码
- `develop`: 开发环境代码
- `feature/*`: 功能开发分支
- `hotfix/*`: 紧急修复分支

### 🚀 部署规范

#### 环境配置
- 开发环境: `NODE_ENV=development`
- 测试环境: `NODE_ENV=test`
- 生产环境: `NODE_ENV=production`

#### 环境变量
- 敏感信息通过环境变量配置
- 不同环境使用不同的配置文件
- 生产环境必须设置所有必需的环境变量

### 📊 性能规范

#### 数据库优化
- 合理使用索引
- 避免N+1查询
- 使用连接池
- 定期分析慢查询

#### 缓存策略
- 静态数据使用缓存
- 设置合理的过期时间
- 缓存失效策略

#### 监控指标
- 响应时间 < 200ms
- 错误率 < 1%
- 数据库连接池使用率 < 80%
- 内存使用率 < 80%

### ✅ 代码审查清单

#### 功能性
- [ ] 功能是否按需求实现
- [ ] 边界条件是否处理
- [ ] 错误情况是否处理

#### 安全性
- [ ] 输入是否验证
- [ ] SQL注入是否防护
- [ ] 敏感信息是否保护

#### 性能
- [ ] 数据库查询是否优化
- [ ] 是否有内存泄漏
- [ ] 是否使用了缓存

#### 可维护性
- [ ] 代码是否清晰易懂
- [ ] 是否有足够的注释
- [ ] 是否遵循项目规范

## 📊 开发进度统计

| 模块 | 进度 | 状态 |
|------|------|------|
| 基础架构 | 100% | ✅ 完成 |
| 路由架构 | 100% | ✅ 完成 |
| 数据库设计 | 95% | ✅ 完成 |
| 数据模型层 | 100% | ✅ 完成 |
| 产品目录管理 | 90% | ✅ 完成 |
| 产品管理 | 95% | ✅ 完成 |
| 店铺管理 | 95% | ✅ 完成 |
| 平台管理 | 95% | ✅ 完成 |
| 订单管理 | 80% | ✅ 完成 |
| 认证系统 | 0% | ⏳ 待开发 |
| 文件上传 | 0% | ⏳ 待开发 |
| 采集任务 | 0% | ⏳ 待开发 |
| 统计分析 | 0% | ⏳ 待开发 |
| 系统配置 | 0% | ⏳ 待开发 |
| 测试覆盖 | 0% | ⏳ 待开发 |

**总体进度**: 80%

## 🎯 下阶段开发计划

### 第1周目标
- [ ] 完成产品管理服务层
- [ ] 实现请求验证规则
- [ ] 完成产品目录和产品管理的完整功能
- [ ] 编写基础测试用例

### 第2周目标
- [ ] 实现认证系统
- [ ] 完成文件上传功能
- [ ] 集成JWT认证到所有路由
- [ ] 完善错误处理

### 第3周目标
- [ ] 实现采集任务管理
- [ ] 开发订单管理功能
- [ ] 平台集成基础框架
- [ ] 统计分析接口

### 第4周目标
- [ ] 完善所有功能模块
- [ ] 完整的测试覆盖
- [ ] 性能优化
- [ ] 部署准备

## 🐛 已知问题

### 当前问题
- [ ] 产品服务层未完成
- [ ] 缺少请求验证规则
- [ ] 认证中间件未实现具体逻辑
- [ ] 缺少测试用例

### 技术债务
- [ ] 添加API文档生成
- [ ] 实现数据库迁移脚本
- [ ] 添加性能监控
- [ ] 完善日志记录

## 🔄 更新记录

**最后更新时间**: 2025年6月23日
**更新人**: 开发团队
**更新内容**:
- ✅ **数据库表名规范化** - 完成数据库表命名标准化改造
  - ✅ 表名修改：`stores` → `stores_updata`、`platforms` → `stores_platforms`、`dropship_products` → `product_dropship`、`orders` → `orders_updata`
  - ✅ 后端代码全面更新：模型层、服务层、控制器层、数据库迁移文件
  - ✅ 前端常量定义同步更新
  - ✅ 文档全面更新：开发文档、API文档、数据库设计文档
  - ✅ 建立清晰的表名分类前缀规范：`product_*`、`stores_*`、`orders_*`、`tasks_*`
- ✅ **后端开发规范整合** - 将完整的开发规范整合到进度文档中
  - ✅ 架构规范：分层架构、职责分离、文件组织规范
  - ✅ 代码规范：TypeScript规范、错误处理、异步处理
  - ✅ API设计规范：RESTful设计、响应格式、状态码规范
  - ✅ 数据库规范：表命名规范、查询规范、事务处理、批量操作优化
  - ✅ 安全规范：输入验证、认证授权、敏感信息处理
  - ✅ 测试规范：文件组织、命名规范、覆盖率要求
  - ✅ 文档规范：代码注释、API文档、版本控制规范
  - ✅ 部署和性能规范：环境配置、监控指标、代码审查清单
- ✅ 完成店铺管理系统 - 实现完整的多平台店铺管理功能
- ✅ 完成 Excel 导入/导出功能 - 实现完整的批量操作功能
- ✅ 完善产品服务层 - 添加根据SKU查找产品功能
- ✅ 集成认证中间件 - 所有API都需要认证访问

---

## 📞 开发团队

**项目负责人**: IKUN ERP Team
**技术栈**: Node.js + TypeScript + MySQL
**开发环境**: localhost:3001
**API文档**: http://localhost:3001/api/v1/docs

---

**文档版本**: 2.0
**最后更新**: 2025年6月23日
**维护团队**: IKUN ERP Development Team

**重要提醒**：
- 本文档包含完整的后端开发进度和开发规范
- 数据库表名已标准化，请严格按照新的命名规范开发
- 所有开发人员必须遵循本文档中的开发规范
- 如有疑问请及时与团队沟通
