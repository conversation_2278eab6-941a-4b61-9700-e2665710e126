# ConfirmDialog 确认对话框组件

一个符合项目UI风格的全局确认对话框组件，支持多种样式和异步操作。

## 功能特性

- 🎨 符合项目UI风格，基于shadcn/ui组件
- 🔄 支持异步操作，自动loading状态
- 🎯 多种预设样式（默认、危险、警告、信息、成功）
- 🌍 全局Hook调用，无需手动管理状态
- ⚡ 支持Promise返回值
- 🎭 可自定义图标、文本和按钮样式

## 安装使用

### 1. 在根组件中添加Provider

```tsx
// app/layout.tsx 或 _app.tsx
import { GlobalConfirmProvider } from '@/components/ui/confirm-dialog'

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html>
      <body>
        <GlobalConfirmProvider>
          {children}
        </GlobalConfirmProvider>
      </body>
    </html>
  )
}
```

### 2. 在组件中使用

```tsx
import { useConfirm } from '@/components/ui/confirm-dialog'

function MyComponent() {
  const { confirm } = useConfirm()

  const handleDelete = async () => {
    const confirmed = await confirm({
      title: '删除确认',
      description: '确定要删除这个产品吗？此操作不可撤销。',
      confirmText: '删除',
      cancelText: '取消',
      variant: 'destructive'
    })

    if (confirmed) {
      // 执行删除操作
      await deleteProduct()
    }
  }

  return (
    <button onClick={handleDelete}>
      删除产品
    </button>
  )
}
```

## API参考

### ConfirmDialogOptions

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| title | string | '确认操作' | 对话框标题 |
| description | string | '您确定要执行此操作吗？' | 对话框描述文本 |
| confirmText | string | '确认' | 确认按钮文本 |
| cancelText | string | '取消' | 取消按钮文本 |
| variant | 'default' \| 'destructive' \| 'warning' \| 'info' \| 'success' | 'default' | 对话框样式变体 |
| icon | boolean | true | 是否显示图标 |

### 样式变体

- **default**: 蓝色图标，默认按钮样式
- **destructive**: 红色警告图标，危险按钮样式
- **warning**: 黄色警告图标，默认按钮样式
- **info**: 蓝色信息图标，默认按钮样式
- **success**: 绿色成功图标，默认按钮样式

## 使用示例

### 基础用法

```tsx
const { confirm } = useConfirm()

// 简单确认
const result = await confirm()
if (result) {
  // 用户点击了确认
}
```

### 删除确认

```tsx
const handleDelete = async () => {
  const confirmed = await confirm({
    title: '删除产品',
    description: '确定要删除这个产品吗？此操作不可撤销。',
    confirmText: '删除',
    variant: 'destructive'
  })

  if (confirmed) {
    await deleteProduct()
  }
}
```

### 批量操作确认

```tsx
const handleBatchDelete = async () => {
  const confirmed = await confirm({
    title: '批量删除',
    description: `确定要删除选中的 ${selectedCount} 个产品吗？`,
    confirmText: `删除 ${selectedCount} 个产品`,
    variant: 'destructive'
  })

  if (confirmed) {
    await batchDeleteProducts()
  }
}
```

### 保存确认

```tsx
const handleSave = async () => {
  const confirmed = await confirm({
    title: '保存更改',
    description: '确定要保存当前的更改吗？',
    confirmText: '保存',
    variant: 'success'
  })

  if (confirmed) {
    await saveChanges()
  }
}
```

### 警告提示

```tsx
const handleRiskyAction = async () => {
  const confirmed = await confirm({
    title: '风险操作',
    description: '此操作可能会影响系统性能，确定要继续吗？',
    confirmText: '继续',
    variant: 'warning'
  })

  if (confirmed) {
    await performRiskyAction()
  }
}
```

## 注意事项

1. 确保在应用根组件中添加了 `GlobalConfirmProvider`
2. `useConfirm` 返回的是Promise，需要使用 `await` 或 `.then()`
3. 组件会自动处理loading状态，无需手动管理
4. 如果没有Provider，会回退到原生 `window.confirm`
