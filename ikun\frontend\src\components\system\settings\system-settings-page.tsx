'use client'

import { useState } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Settings, Globe, Shield, Database, BarChart3 } from 'lucide-react'
import { SystemBasicSettings } from '@/components/system/settings/system-basic-settings'
import { SystemTranslationSettings } from '@/components/system/settings/system-translation-settings'
import { SystemTokenStatistics } from '@/components/system/settings/system-token-statistics'
import { cn } from '@/lib/utils'

// 菜单配置
const menuItems = [
  {
    id: 'system',
    label: '系统配置',
    icon: Settings,
    component: SystemBasicSettings,
    description: '基础系统设置'
  },
  {
    id: 'translation',
    label: '翻译服务',
    icon: Globe,
    component: SystemTranslationSettings,
    description: '翻译配置管理'
  },
  {
    id: 'token-statistics',
    label: 'Token统计',
    icon: BarChart3,
    component: SystemTokenStatistics,
    description: 'Token使用量统计'
  },
  {
    id: 'security',
    label: '安全设置',
    icon: Shield,
    component: null,
    description: '安全和权限配置'
  },
  {
    id: 'database',
    label: '数据配置',
    icon: Database,
    component: null,
    description: '数据库和存储设置'
  }
]

export function SystemSettingsPage() {
  const [activeMenu, setActiveMenu] = useState('system')

  const selectMenu = (menuId: string) => {
    setActiveMenu(menuId)
  }

  // 查找当前激活的菜单项
  const findActiveMenuItem = () => {
    return menuItems.find(item => item.id === activeMenu) || null
  }

  const activeMenuItem = findActiveMenuItem()
  const ActiveComponent = activeMenuItem?.component

  return (
    <Card className="h-full">
      <CardContent className="p-0 h-full">
        <div className="flex h-full">
          {/* 左侧导航 */}
          <div className="w-64 border-r bg-muted/30">
            <div className="p-3 space-y-1">
              {menuItems.map((item) => {
                const Icon = item.icon
                const isActive = activeMenu === item.id

                return (
                  <div key={item.id} className="space-y-1">
                    {/* 主菜单项 */}
                    <Button
                      variant={isActive ? "secondary" : "ghost"}
                      className={cn(
                        "w-full justify-between h-auto p-2.5 text-sm",
                        isActive && "bg-secondary"
                      )}
                      onClick={() => selectMenu(item.id)}
                    >
                      <div className="flex items-center space-x-2.5">
                        <Icon className="h-4 w-4" />
                        <div className="text-left">
                          <div className="font-medium">{item.label}</div>
                          <div className="text-xs text-muted-foreground">
                            {item.description}
                          </div>
                        </div>
                      </div>
                    </Button>
                  </div>
                )
              })}
            </div>
          </div>

          {/* 右侧内容区域 */}
          <div className="flex-1 bg-background overflow-auto">
            {ActiveComponent ? (
              <div className="p-6 max-w-5xl mx-auto">
                <ActiveComponent />
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <div className="text-center">
                  <div className="mx-auto w-12 h-12 bg-muted rounded-full flex items-center justify-center mb-3">
                    {activeMenuItem && activeMenuItem.icon && (
                      <activeMenuItem.icon className="h-6 w-6 text-muted-foreground" />
                    )}
                  </div>
                  <h3 className="text-base font-medium mb-1">{activeMenuItem?.label}</h3>
                  <p className="text-sm text-muted-foreground">
                    {activeMenuItem?.description}功能正在开发中...
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
