/**
 * 测试首次请求问题的修复效果
 * 独立测试文件，直接测试MTran服务
 */

import axios, { AxiosInstance } from 'axios';

// MTran服务配置
const MTRAN_BASE_URL = 'http://192.168.2.109:8989';
const TIMEOUT = 30000;

// 创建HTTP客户端
const createHttpClient = (): AxiosInstance => {
  return axios.create({
    baseURL: MTRAN_BASE_URL,
    timeout: TIMEOUT,
    headers: {
      'Content-Type': 'application/json'
    }
  });
};

/**
 * 模拟首次请求失败的翻译函数
 */
async function translateWithRetry(
  text: string,
  from: string,
  to: string,
  maxRetries: number = 2
): Promise<{ success: boolean; result?: string; error?: string; attempts: number }> {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`   尝试 ${attempt}/${maxRetries}...`);

      // 每次创建新的客户端来模拟首次请求
      const client = createHttpClient();

      const startTime = Date.now();
      const response = await client.post('/translate', {
        from,
        to,
        text
      });
      const endTime = Date.now();

      if (response.status === 200 && response.data?.result) {
        console.log(`   ✅ 成功 (${endTime - startTime}ms, 第${attempt}次尝试)`);
        return {
          success: true,
          result: response.data.result,
          attempts: attempt
        };
      } else {
        throw new Error('Invalid response format');
      }

    } catch (error: any) {
      lastError = error;
      console.log(`   ❌ 第${attempt}次尝试失败: ${error.message}`);

      if (attempt < maxRetries) {
        console.log(`   ⏳ 等待1秒后重试...`);
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  }

  return {
    success: false,
    error: lastError?.message || 'Unknown error',
    attempts: maxRetries
  };
}

/**
 * 测试首次请求问题
 */
async function testFirstRequestIssue(): Promise<void> {
  console.log('🧪 测试首次请求问题修复效果');
  console.log('='.repeat(50));

  const testCases = [
    {
      name: '测试1: 英文到葡萄牙语',
      text: 'Hello World',
      from: 'en',
      to: 'pt'
    },
    {
      name: '测试2: 英文到西班牙语',
      text: 'Good Morning',
      from: 'en',
      to: 'es'
    },
    {
      name: '测试3: 长文本翻译',
      text: '2 PCS Hanging Picture Painting Mirror Hooks Stainless Steel Interlocking Hangers Photo Frame Hooks with Screws (Silver)',
      from: 'en',
      to: 'pt'
    }
  ];

  let successCount = 0;
  let totalAttempts = 0;

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`\n${i + 1}. ${testCase.name}`);
    console.log(`   文本: "${testCase.text}"`);
    console.log(`   翻译: ${testCase.from} -> ${testCase.to}`);

    const result = await translateWithRetry(testCase.text, testCase.from, testCase.to);
    totalAttempts += result.attempts;

    if (result.success) {
      console.log(`   结果: "${result.result}"`);
      successCount++;
    } else {
      console.log(`   最终失败: ${result.error}`);
    }

    // 添加延迟
    if (i < testCases.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }

  // 测试总结
  console.log('\n' + '='.repeat(50));
  console.log('📊 首次请求测试总结:');
  console.log(`   总测试数: ${testCases.length}`);
  console.log(`   成功: ${successCount}`);
  console.log(`   失败: ${testCases.length - successCount}`);
  console.log(`   总尝试次数: ${totalAttempts}`);
  console.log(`   平均尝试次数: ${(totalAttempts / testCases.length).toFixed(1)}`);
  console.log(`   成功率: ${((successCount / testCases.length) * 100).toFixed(1)}%`);

  if (successCount === testCases.length) {
    if (totalAttempts === testCases.length) {
      console.log('\n🎉 完美！所有请求都在第一次尝试成功！');
    } else {
      console.log('\n✅ 所有测试通过，重试机制工作正常！');
    }
  } else {
    console.log('\n⚠️  仍存在首次请求问题，需要进一步调试');
  }
}

/**
 * 测试连续请求的稳定性（使用同一个客户端）
 */
async function testContinuousRequests(): Promise<void> {
  console.log('\n\n🔄 测试连续请求稳定性');
  console.log('='.repeat(50));

  // 使用同一个客户端进行连续请求
  const client = createHttpClient();
  const testTexts = [
    'Hello',
    'Good Morning',
    'How are you?',
    'Thank you',
    'Goodbye'
  ];

  let successCount = 0;
  let failureCount = 0;

  for (let i = 0; i < testTexts.length; i++) {
    const text = testTexts[i];
    console.log(`\n${i + 1}. 连续请求测试`);
    console.log(`   文本: "${text}"`);

    try {
      const startTime = Date.now();

      const response = await client.post('/translate', {
        from: 'en',
        to: 'pt',
        text
      });

      const endTime = Date.now();
      const duration = endTime - startTime;

      if (response.status === 200 && response.data?.result) {
        console.log(`   ✅ 成功 (${duration}ms): "${response.data.result}"`);
        successCount++;
      } else {
        console.log(`   ❌ 失败: 无效响应格式`);
        failureCount++;
      }

    } catch (error: any) {
      console.log(`   ❌ 异常: ${error.message}`);
      failureCount++;
    }

    // 短暂延迟
    await new Promise(resolve => setTimeout(resolve, 500));
  }

  console.log('\n📊 连续请求测试总结:');
  console.log(`   总测试数: ${testTexts.length}`);
  console.log(`   成功: ${successCount}`);
  console.log(`   失败: ${failureCount}`);
  console.log(`   成功率: ${((successCount / testTexts.length) * 100).toFixed(1)}%`);
}

/**
 * 主测试函数
 */
async function runAllTests(): Promise<void> {
  try {
    await testFirstRequestIssue();
    await testContinuousRequests();
    
    console.log('\n🏁 所有测试完成');
  } catch (error) {
    console.error('测试运行失败:', error);
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}
