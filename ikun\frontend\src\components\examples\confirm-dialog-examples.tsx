'use client'

import { useConfirm } from '@/components/ui/confirm-dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Trash2, Save, AlertTriangle, Info, CheckCircle } from 'lucide-react'

export function ConfirmDialogExamples() {
  const { confirm } = useConfirm()

  const handleBasicConfirm = async () => {
    const result = await confirm()
    console.log('基础确认结果:', result)
  }

  const handleDeleteConfirm = async () => {
    const result = await confirm({
      title: '删除产品',
      description: '确定要删除这个产品吗？此操作不可撤销。',
      confirmText: '删除',
      cancelText: '取消',
      variant: 'destructive'
    })
    console.log('删除确认结果:', result)
  }

  const handleBatchDeleteConfirm = async () => {
    const selectedCount = 5
    const result = await confirm({
      title: '批量删除',
      description: `确定要删除选中的 ${selectedCount} 个产品吗？此操作不可撤销。`,
      confirmText: `删除 ${selectedCount} 个产品`,
      cancelText: '取消',
      variant: 'destructive'
    })
    console.log('批量删除确认结果:', result)
  }

  const handleSaveConfirm = async () => {
    const result = await confirm({
      title: '保存更改',
      description: '确定要保存当前的更改吗？',
      confirmText: '保存',
      cancelText: '取消',
      variant: 'success'
    })
    console.log('保存确认结果:', result)
  }

  const handleWarningConfirm = async () => {
    const result = await confirm({
      title: '风险操作',
      description: '此操作可能会影响系统性能，确定要继续吗？',
      confirmText: '继续',
      cancelText: '取消',
      variant: 'warning'
    })
    console.log('警告确认结果:', result)
  }

  const handleInfoConfirm = async () => {
    const result = await confirm({
      title: '信息提示',
      description: '这是一个信息提示，您可以选择继续或取消。',
      confirmText: '继续',
      cancelText: '取消',
      variant: 'info'
    })
    console.log('信息确认结果:', result)
  }

  const handleNoIconConfirm = async () => {
    const result = await confirm({
      title: '无图标确认',
      description: '这是一个没有图标的确认对话框。',
      confirmText: '确认',
      cancelText: '取消',
      variant: 'default',
      icon: false
    })
    console.log('无图标确认结果:', result)
  }

  const handleCustomTextConfirm = async () => {
    const result = await confirm({
      title: '自定义文本',
      description: '您可以自定义所有的文本内容。',
      confirmText: '好的',
      cancelText: '不了',
      variant: 'default'
    })
    console.log('自定义文本确认结果:', result)
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">确认对话框示例</h1>
        <p className="text-muted-foreground">
          展示不同样式和用途的确认对话框组件
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* 基础确认 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="w-5 h-5" />
              基础确认
            </CardTitle>
            <CardDescription>
              最简单的确认对话框
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleBasicConfirm} className="w-full">
              基础确认
            </Button>
          </CardContent>
        </Card>

        {/* 删除确认 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trash2 className="w-5 h-5 text-red-500" />
              删除确认
            </CardTitle>
            <CardDescription>
              危险操作的确认对话框
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleDeleteConfirm} variant="destructive" className="w-full">
              删除产品
            </Button>
          </CardContent>
        </Card>

        {/* 批量删除 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Trash2 className="w-5 h-5 text-red-500" />
              批量删除
            </CardTitle>
            <CardDescription>
              批量操作的确认对话框
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleBatchDeleteConfirm} variant="destructive" className="w-full">
              批量删除 (5个)
            </Button>
          </CardContent>
        </Card>

        {/* 保存确认 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Save className="w-5 h-5 text-green-500" />
              保存确认
            </CardTitle>
            <CardDescription>
              成功样式的确认对话框
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleSaveConfirm} className="w-full">
              保存更改
            </Button>
          </CardContent>
        </Card>

        {/* 警告确认 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="w-5 h-5 text-yellow-500" />
              警告确认
            </CardTitle>
            <CardDescription>
              警告样式的确认对话框
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleWarningConfirm} variant="outline" className="w-full">
              风险操作
            </Button>
          </CardContent>
        </Card>

        {/* 信息确认 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="w-5 h-5 text-blue-500" />
              信息确认
            </CardTitle>
            <CardDescription>
              信息样式的确认对话框
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleInfoConfirm} variant="outline" className="w-full">
              信息提示
            </Button>
          </CardContent>
        </Card>

        {/* 无图标确认 */}
        <Card>
          <CardHeader>
            <CardTitle>无图标确认</CardTitle>
            <CardDescription>
              不显示图标的确认对话框
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleNoIconConfirm} variant="outline" className="w-full">
              无图标确认
            </Button>
          </CardContent>
        </Card>

        {/* 自定义文本 */}
        <Card>
          <CardHeader>
            <CardTitle>自定义文本</CardTitle>
            <CardDescription>
              自定义按钮文本的确认对话框
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button onClick={handleCustomTextConfirm} variant="outline" className="w-full">
              自定义文本
            </Button>
          </CardContent>
        </Card>
      </div>

      <div className="mt-8 p-4 bg-muted rounded-lg">
        <h3 className="font-semibold mb-2">使用说明：</h3>
        <ul className="text-sm text-muted-foreground space-y-1">
          <li>• 点击按钮查看不同样式的确认对话框</li>
          <li>• 查看浏览器控制台可以看到返回的结果</li>
          <li>• 所有对话框都支持异步操作和loading状态</li>
          <li>• 可以通过ESC键或点击遮罩层关闭对话框</li>
        </ul>
      </div>
    </div>
  )
}
