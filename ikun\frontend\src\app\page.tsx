import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { DashboardStats } from '@/components/dashboard/dashboard-stats'
import { ProductOverview } from '@/components/dashboard/product-overview'
import { RecentTasks } from '@/components/dashboard/recent-tasks'
import { QuickActions } from '@/components/dashboard/quick-actions'
import { AuthGuard } from '@/components/auth/auth-guard'

export default function HomePage() {
  return (
    <AuthGuard>
      <DashboardLayout>
        <div className="space-y-6">
          {/* 统计卡片 */}
          <DashboardStats />

          {/* 主要内容区域 */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {/* 产品概览 */}
            <div className="lg:col-span-2">
              <ProductOverview />
            </div>

            {/* 快速操作 */}
            <div>
              <QuickActions />
            </div>
          </div>

          {/* 最近任务 */}
          <RecentTasks />
        </div>
      </DashboardLayout>
    </AuthGuard>
  )
}
