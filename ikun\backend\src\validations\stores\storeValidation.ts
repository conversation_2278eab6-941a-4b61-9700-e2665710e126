/**
 * Store Validation Rules
 * 店铺管理验证规则
 */

import <PERSON><PERSON> from 'joi';

export const storeValidation = {
  // GET /api/v1/stores - 获取店铺列表
  getStores: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    platform_code: Joi.string().optional(),
    status: Joi.string().valid('active', 'failed').optional(),
    search: Joi.string().optional()
  }),

  // POST /api/v1/stores - 创建店铺
  createStore: Joi.object({
    platform_code: Joi.string().required().messages({
      'string.empty': '平台代码不能为空',
      'any.required': '平台代码是必填项'
    }),
    store_name: Joi.string().required().min(1).max(200).messages({
      'string.empty': '店铺名称不能为空',
      'string.min': '店铺名称至少需要1个字符',
      'string.max': '店铺名称不能超过200个字符',
      'any.required': '店铺名称是必填项'
    }),
    status: Joi.string().valid('active', 'failed').default('active'),
    api_key: Joi.string().optional().allow(''),
    token: Joi.string().optional().allow(''),
    token_expires_at: Joi.date().optional().allow(null),
    site: Joi.string().optional().allow(''),
    platform_config: Joi.object().optional().allow(null)
  }),

  // PUT /api/v1/stores/:id - 更新店铺
  updateStore: Joi.object({
    store_name: Joi.string().min(1).max(200).optional().messages({
      'string.min': '店铺名称至少需要1个字符',
      'string.max': '店铺名称不能超过200个字符'
    }),
    status: Joi.string().valid('active', 'failed').optional(),
    api_key: Joi.string().optional().allow(''),
    token: Joi.string().optional().allow(''),
    token_expires_at: Joi.date().optional().allow(null),
    site: Joi.string().optional().allow(''),
    platform_config: Joi.object().optional().allow(null)
  }),

  // 店铺ID参数验证
  storeId: Joi.object({
    id: Joi.number().integer().min(1).required().messages({
      'number.base': '店铺ID必须是数字',
      'number.integer': '店铺ID必须是整数',
      'number.min': '店铺ID必须大于0',
      'any.required': '店铺ID是必填项'
    })
  }),

  // 更新店铺状态
  updateStatus: Joi.object({
    status: Joi.string().valid('active', 'failed').required().messages({
      'any.only': '状态只能是 active 或 failed',
      'any.required': '状态是必填项'
    })
  })
};
