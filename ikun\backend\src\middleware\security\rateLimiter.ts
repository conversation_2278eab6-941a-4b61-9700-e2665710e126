/**
 * Rate Limiting Middleware
 */

import { Request, Response, NextFunction } from 'express';
import { config } from '@/config/config';
import { getBeijingTimeISO } from '@/utils/time';

interface RateLimitStore {
  [key: string]: {
    count: number;
    resetTime: number;
  };
}

class RateLimiter {
  private store: RateLimitStore = {};
  private windowMs: number;
  private max: number;

  constructor(windowMs: number, max: number) {
    this.windowMs = windowMs;
    this.max = max;
    
    // Clean up expired entries every minute
    setInterval(() => {
      this.cleanup();
    }, 60000);
  }

  private cleanup(): void {
    const now = Date.now();
    Object.keys(this.store).forEach(key => {
      if (this.store[key]!.resetTime < now) {
        delete this.store[key];
      }
    });
  }

  private getKey(req: Request): string {
    return req.ip || 'unknown';
  }

  public middleware = (req: Request, res: Response, next: NextFunction): void => {
    const key = this.getKey(req);
    const now = Date.now();
    
    if (!this.store[key] || this.store[key]!.resetTime < now) {
      this.store[key] = {
        count: 1,
        resetTime: now + this.windowMs
      };
    } else {
      this.store[key]!.count++;
    }

    const current = this.store[key]!;
    
    // Set rate limit headers
    res.set({
      'X-RateLimit-Limit': this.max.toString(),
      'X-RateLimit-Remaining': Math.max(0, this.max - current.count).toString(),
      'X-RateLimit-Reset': new Date(current.resetTime).toISOString()
    });

    if (current.count > this.max) {
      res.status(429).json({
        code: 429,
        message: 'Too many requests',
        error: 'RATE_LIMIT_EXCEEDED',
        retryAfter: Math.ceil((current.resetTime - now) / 1000),
        timestamp: getBeijingTimeISO()
      });
      return;
    }

    next();
  };
}

const rateLimiterInstance = new RateLimiter(config.rateLimit.windowMs, config.rateLimit.max);

export { rateLimiterInstance };
export const rateLimiter = rateLimiterInstance.middleware;
