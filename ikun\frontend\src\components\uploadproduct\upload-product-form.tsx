'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import {
  Dialog,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogPortal,
} from '@/components/ui/dialog'
import * as DialogPrimitive from "@radix-ui/react-dialog"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import {
  Loader2,
  Package,
  Globe,
  Search,
  Info,
  Type,
  Image as ImageIcon,
  Box,
  DollarSign,
  X,
  Languages
} from 'lucide-react'
import { toast } from 'sonner'
import { TranslationButton, LanguageCode } from '@/components/translation'

// 基础表单验证模式
const baseUploadProductSchema = z.object({
  // 基本信息
  dropship_product_id: z.coerce.number().min(1, '请选择铺货产品'),
  store_id: z.coerce.number().min(1, '请选择店铺'),
  sku: z.string().min(1, 'SKU不能为空'),
  ean: z.string().optional(), // 编辑模式下EAN可以为空
  english_title: z.string().optional(), // 编辑模式下标题可以为空
  english_description: z.string().optional(), // 编辑模式下描述可以为空
  selling_point: z.array(z.string()).optional(),
  multi_titles: z.record(z.string()).optional(),

  // 多语言描述 - 动态字段，根据平台确定
  multi_descriptions: z.record(z.string()).optional(),

  // 多语言卖点 - 动态字段，根据平台确定
  multi_selling_points: z.record(z.array(z.string())).optional(),

  // 图片信息
  image1: z.string().optional(),
  image2: z.string().optional(),
  image3: z.string().optional(),
  image4: z.string().optional(),
  image5: z.string().optional(),

  // 平台信息
  platform_category_id: z.string().optional(),

  // 平台报价信息
  platform_sku: z.string().optional(),
  platform_ean: z.string().optional(),
  discounted_price: z.coerce.number().min(0).optional(),
  original_price: z.coerce.number().min(0).optional(),
  stock_quantity: z.coerce.number().min(0).optional(),
  discount_percentage: z.coerce.number().min(0).max(100).optional(),
  discount_start_date: z.string().optional(),
  discount_end_date: z.string().optional(),
})

// 新增模式的严格验证
const createUploadProductSchema = baseUploadProductSchema.extend({
  ean: z.string().min(1, 'EAN不能为空'),
  english_title: z.string().min(1, '英文标题不能为空'),
  english_description: z.string().min(1, '英文描述不能为空'),
})

// 根据模式选择验证模式
const getValidationSchema = (mode: 'add' | 'edit') => {
  return mode === 'add' ? createUploadProductSchema : baseUploadProductSchema
}

type UploadProductFormData = z.infer<typeof baseUploadProductSchema>

interface UploadProductFormProps {
  open: boolean
  onClose: () => void
  onSubmit: (data: UploadProductFormData) => Promise<void>
  editingProduct?: any
  mode?: 'add' | 'edit'
  stores?: any[]
  platform: string // 必需的平台参数
}

// 隐藏数字输入框spinner的CSS类
const numberInputClass = "appearance-none [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none [-moz-appearance:textfield]"

export function UploadProductForm({
  open,
  onClose,
  onSubmit,
  editingProduct,
  mode = 'add',
  stores = [],
  platform
}: UploadProductFormProps) {
  const [loading, setLoading] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')

  // 根据模式判断字段是否必填
  const isRequired = (fieldName: string) => {
    if (mode === 'add') {
      return ['ean', 'english_title', 'english_description'].includes(fieldName)
    }
    return false // 编辑模式下所有字段都不是必填
  }

  // 获取平台所需的语言列表
  const getPlatformRequiredLanguages = (platformCode: string): Array<{code: string, name: string}> => {
    const platformLanguages: Record<string, Array<{code: string, name: string}>> = {
      worten: [
        { code: 'PT', name: '葡萄牙语' },
        { code: 'ES', name: '西班牙语' }
      ],
      phh: [
        { code: 'LT', name: '立陶宛语' },
        { code: 'LV', name: '拉脱维亚语' },
        { code: 'EE', name: '爱沙尼亚语' },
        { code: 'FI', name: '芬兰语' }
      ],
      amazon: [
        { code: 'EN', name: '英语' },
        { code: 'DE', name: '德语' },
        { code: 'FR', name: '法语' },
        { code: 'IT', name: '意大利语' },
        { code: 'ES', name: '西班牙语' }
      ],
      ebay: [{ code: 'EN', name: '英语' }],
      shopify: [{ code: 'EN', name: '英语' }]
    }
    return platformLanguages[platformCode] || [{ code: 'EN', name: '英语' }]
  }

  const platformLanguages = getPlatformRequiredLanguages(platform)
  const [activeLanguage, setActiveLanguage] = useState(platformLanguages[0]?.code || 'EN')

  // 根据平台过滤店铺（参考平台listing文件的实现）
  const filteredStores = stores.filter(store => store.platform_code === platform)

  // 搜索状态
  const [searchLoading, setSearchLoading] = useState(false)

  // 常量定义
  const NAV_BUTTON_CLASS = "w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-white hover:shadow-sm rounded-md transition-colors"
  const LANG_BUTTON_BASE_CLASS = "w-full text-left px-3 py-2 text-sm rounded-md transition-colors"



  // 获取语言按钮样式
  const getLangButtonClass = (langCode: string) =>
    `${LANG_BUTTON_BASE_CLASS} ${
      activeLanguage === langCode
        ? 'bg-blue-100 text-blue-700 border border-blue-200'
        : 'text-gray-700 hover:bg-white hover:shadow-sm'
    }`

  // 搜索状态
  const [searchResults, setSearchResults] = useState<any[]>([])
  const [hasSearched, setHasSearched] = useState(false)

  // 翻译功能已简化，移除批量翻译模态框，使用字段旁的翻译按钮

  // 搜索功能 - 服务端搜索
  const handleSearch = async () => {
    if (!searchTerm.trim()) {
      toast.info('请输入搜索关键词')
      return
    }

    setSearchLoading(true)
    try {
      const token = localStorage.getItem('auth_token')
      if (!token) {
        throw new Error('请先登录')
      }

      // 使用现有的铺货产品API进行搜索
      const response = await fetch(`/api/v1/products/dropship?search=${encodeURIComponent(searchTerm)}&limit=20&status=active`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error('搜索请求失败')
      }

      const data = await response.json()

      // 检查API返回格式：使用code字段判断成功
      if (data.code === 200) {
        const products = data.data?.items || []
        setSearchResults(products)
        setHasSearched(true)
        toast.success(`找到 ${products.length} 个匹配产品`)
      } else {
        throw new Error(data.message || '搜索失败')
      }
    } catch (error) {
      console.error('搜索失败:', error)
      toast.error('搜索失败，请重试')
      setSearchResults([])
      setHasSearched(true)
    } finally {
      setSearchLoading(false)
    }
  }

  // 清空搜索
  const clearSearch = () => {
    setSearchTerm('')
    setSearchResults([])
    setHasSearched(false)
    toast.success('已清空搜索')
  }


  const form = useForm<UploadProductFormData>({
    resolver: zodResolver(getValidationSchema(mode)),
    defaultValues: {
      dropship_product_id: 0,
      store_id: 0,
      sku: '',
      ean: '',
      english_title: '',
      english_description: '',
      ...(platform !== 'worten' && { selling_point: ['', '', '', '', ''] }),
      image1: '',
      image2: '',
      image3: '',
      image4: '',
      image5: '',
      platform_category_id: '',
      platform_sku: '',
      platform_ean: '',
      discounted_price: 0,
      original_price: 0,
      stock_quantity: 50,
      discount_percentage: 0,
      discount_start_date: '',
      discount_end_date: '',
    }
  })

  // 当模式变化时更新验证器
  useEffect(() => {
    const newResolver = zodResolver(getValidationSchema(mode))
    form.clearErrors() // 清除现有错误
    // 注意：react-hook-form 不支持动态更改 resolver，所以我们在提交时进行验证
  }, [mode, form])

  // 组件打开时重置表单和获取数据
  useEffect(() => {
    if (open) {
      if (mode === 'add') {
        // 新增模式：重置表单为默认值
        form.reset({
          dropship_product_id: 0,
          store_id: 0,
          sku: '',
          ean: '',
          english_title: '',
          english_description: '',
          // 五点卖点
          selling_point: ['', '', '', '', ''],
          image1: '',
          image2: '',
          image3: '',
          image4: '',
          image5: '',
          platform_category_id: '',
          platform_sku: '',
          platform_ean: '',
          discounted_price: 0,
          original_price: 0,
          stock_quantity: 50,
          discount_percentage: 0,
          discount_start_date: '',
          discount_end_date: '',
        })

        // 重置搜索状态
        setSearchTerm('')
        setSearchResults([])
        setHasSearched(false)
      }
    }
  }, [open, mode, form])

  // 显示搜索结果的条件：已执行搜索
  const shouldShowResults = hasSearched

  // 选择铺货产品并自动填充表单
  const selectDropshipProduct = (product: any) => {
    // 自动填充基本信息
    form.setValue('dropship_product_id', product.id)
    form.setValue('sku', product.sku)
    form.setValue('ean', product.ean || '')
    form.setValue('english_title', product.english_title)
    form.setValue('english_description', product.english_description || '')

    // 自动填充图片信息
    form.setValue('image1', product.image1 || '')
    form.setValue('image2', product.image2 || '')
    form.setValue('image3', product.image3 || '')
    form.setValue('image4', product.image4 || '')
    form.setValue('image5', product.image5 || '')

    // 自动填充卖点信息（如果有）
    if (product.selling_point && Array.isArray(product.selling_point)) {
      form.setValue('selling_point', product.selling_point)
    }

    // 自动填充多语言字段（如果有）
    if (product.multi_titles) {
      form.setValue('multi_titles', product.multi_titles)
    }
    if (product.multi_descriptions) {
      form.setValue('multi_descriptions', product.multi_descriptions)
    }

    // 自动同步SKU和EAN到平台字段
    form.setValue('platform_sku', product.sku)
    form.setValue('platform_ean', product.ean || '')

    // 自动关闭搜索
    setSearchTerm('')
    setSearchResults([])
    setHasSearched(false)

    // 显示成功提示
    toast.success(`已选择产品: ${product.sku}`)
  }

  // 编辑模式下填充数据
  useEffect(() => {
    if (editingProduct && mode === 'edit') {
      console.log('编辑模式填充数据:', editingProduct)

      // 计算折扣百分比
      const calculateDiscountPercentage = (original: number, discounted: number) => {
        if (original > 0 && discounted > 0 && original > discounted) {
          return Math.round(((original - discounted) / original) * 100 * 100) / 100
        }
        return 0
      }

      const discountPercentage = calculateDiscountPercentage(
        editingProduct.original_price || 0,
        editingProduct.discounted_price || 0
      )

      form.reset({
        dropship_product_id: editingProduct.dropship_product_id,
        store_id: editingProduct.store_id,
        sku: editingProduct.sku,
        ean: editingProduct.ean || '',
        english_title: editingProduct.english_title || '',
        // 现在从关联的 product_dropship 表获取英文描述
        english_description: editingProduct.english_description || '',
        image1: editingProduct.image1 || '',
        // 现在从关联的 product_dropship 表获取所有图片
        image2: editingProduct.image2 || '',
        image3: editingProduct.image3 || '',
        image4: editingProduct.image4 || '',
        image5: editingProduct.image5 || '',
        platform_category_id: editingProduct.platform_category_id || '',
        // 使用 upstores_sku 和 upstores_ean 作为平台字段
        platform_sku: editingProduct.upstores_sku || editingProduct.sku || '',
        platform_ean: editingProduct.upstores_ean || editingProduct.ean || '',
        discounted_price: editingProduct.discounted_price || 0,
        original_price: editingProduct.original_price || 0,
        stock_quantity: editingProduct.stock_quantity || 50,
        discount_percentage: discountPercentage,
        discount_start_date: editingProduct.discount_start_date || '',
        discount_end_date: editingProduct.discount_end_date || '',
        // 多语言字段
        multi_titles: editingProduct.multi_titles || {},
        multi_descriptions: editingProduct.multi_descriptions || {},
        // 卖点相关字段仅在非 Worten 平台设置
        ...(platform !== 'worten' && {
          // 五点卖点：优先使用 product_dropship 表的 selling_point，fallback 到 multi_selling_points 的英文版本
          selling_point: editingProduct.selling_point || editingProduct.multi_selling_points?.EN || ['', '', '', '', ''],
          multi_selling_points: editingProduct.multi_selling_points || {},
        }),
      })

      console.log('表单数据填充完成')
    }
  }, [editingProduct, mode, form])

  const handleSubmit = async (data: UploadProductFormData) => {
    console.log('表单提交开始:', data)
    try {
      setLoading(true)

      // 根据当前模式进行验证
      const validationSchema = getValidationSchema(mode)
      const validationResult = validationSchema.safeParse(data)

      if (!validationResult.success) {
        // 显示验证错误
        const errors = validationResult.error.errors
        errors.forEach(error => {
          form.setError(error.path[0] as any, { message: error.message })
        })
        toast.error('请检查表单填写是否完整')
        return
      }

      // 数据转换：根据平台处理卖点数据
      const processedData = {
        ...data,
        // Worten 平台不需要卖点数据
        ...(platform !== 'worten' && {
          // 保持英文卖点在 selling_point 字段中（将存储到 product_dropship 表）
          selling_point: data.selling_point || [],
          // multi_selling_points 只包含非英文的目标语言卖点
          multi_selling_points: {
            ...data.multi_selling_points
            // 注意：不包含 EN 字段，英文卖点存储在 selling_point 字段中
          }
        })
      }

      console.log('处理后的数据:', processedData)
      await onSubmit(processedData)
      toast.success(editingProduct ? '产品更新成功' : '产品认领成功')
      onClose()
    } catch (error) {
      console.error('提交失败:', error)
      toast.error('操作失败，请重试')
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    form.reset()
    setSearchTerm('')
    onClose()
  }

  // 滚动到指定区域
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }

  return (
    <Dialog open={open} onOpenChange={() => {}}>
      <DialogPortal>
        {/* 自定义透明背景 */}
        <div className="fixed inset-0 z-50 bg-transparent" />
        <DialogPrimitive.Content
          className="fixed left-[50%] top-[50%] z-50 grid w-full max-w-4xl translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-0 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg max-h-[90vh]"
          onEscapeKeyDown={handleClose}
          onPointerDownOutside={handleClose}
          onInteractOutside={handleClose}
        >
        {/* 自定义关闭按钮 */}
        <button
          type="button"
          onClick={handleClose}
          className="absolute right-4 top-4 z-10 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
        >
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </button>
        <DialogHeader className="px-6 py-4 border-b">
          <DialogTitle className="flex items-center gap-2">
            <Package className="w-5 h-5" />
            {editingProduct ? '编辑产品上架' : '认领产品上架'}
          </DialogTitle>
        </DialogHeader>

        <div className="flex h-[calc(90vh-180px)]">
          {/* 左侧 - 表单内容区域 */}
          <div className="flex-1 overflow-y-auto px-6 py-4" style={{ scrollbarGutter: 'stable' }}>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
                {/* 基本信息 */}
                <div id="basic-info" className="form-section pt-0">
                  <div className="flex items-center text-base font-medium text-gray-900 mb-4">
                    <Info className="w-5 h-5 mr-2" />
                    <span>基本信息</span>
                  </div>
                  
                  {/* 铺货产品搜索选择 */}
                  {mode === 'add' && (
                    <div className="mb-4 w-full">
                      <Label>铺货产品搜索 *</Label>
                      <div className="flex gap-2 mt-1 w-full">
                        <div className="flex-1 min-w-0">
                          <Input
                            placeholder={searchLoading ? "正在搜索..." : "输入SKU、标题或EAN码，然后点击搜索"}
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            onKeyDown={(e) => {
                              if (e.key === 'Enter') {
                                e.preventDefault()
                                handleSearch()
                              }
                            }}
                            disabled={searchLoading}
                            className="w-full"
                          />
                        </div>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={handleSearch}
                          disabled={!searchTerm.trim() || searchLoading}
                          title="搜索产品"
                        >
                          {searchLoading ? (
                            <Loader2 className="w-4 h-4 animate-spin" />
                          ) : (
                            <Search className="w-4 h-4" />
                          )}
                        </Button>
                        {(searchTerm || hasSearched) && (
                          <Button
                            type="button"
                            variant="outline"
                            onClick={clearSearch}
                            title="清空搜索"
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        )}
                      </div>

                      {/* 搜索结果 - 严格限制宽度不超过左侧表单 */}
                      {shouldShowResults && (
                        <div className="mt-2 relative">
                          {/* 搜索结果统计 */}
                          <div className="text-xs text-gray-500 mb-1 px-1">
                            {searchLoading ? "正在搜索..." : `找到 ${searchResults.length} 个匹配产品`}
                          </div>
                          <div className="max-h-40 overflow-y-auto border rounded-md bg-white shadow-sm max-w-[480px] overflow-x-hidden">
                          {searchLoading ? (
                            <div className="p-4 text-center text-gray-500">
                              <Loader2 className="w-6 h-6 mx-auto mb-2 animate-spin" />
                              <div className="text-sm">正在搜索产品...</div>
                            </div>
                          ) : searchResults.length > 0 ? (
                            searchResults.map((product: any) => (
                              <div
                                key={product.id}
                                className="p-3 hover:bg-gray-50 cursor-pointer border-b last:border-b-0 max-w-full"
                                onClick={() => selectDropshipProduct(product)}
                              >
                                <div className="flex items-center gap-3 max-w-full overflow-hidden">
                                  {product.image1 && (
                                    <img
                                      src={product.image1}
                                      alt={product.english_title}
                                      className="w-12 h-12 object-cover rounded flex-shrink-0"
                                    />
                                  )}
                                  <div className="flex-1 min-w-0 overflow-hidden">
                                    <div className="font-medium text-sm truncate max-w-full">{product.sku}</div>
                                    <div className="text-sm text-gray-600 truncate max-w-full">
                                      {product.english_title}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            ))
                          ) : (
                            <div className="p-4 text-center text-gray-500">
                              <Search className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                              <div className="text-sm">未找到匹配的产品</div>
                              <div className="text-xs text-gray-400 mt-1">
                                请尝试其他关键词搜索
                              </div>
                            </div>
                          )}
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  <div className="grid grid-cols-2 gap-4">
                    {/* 店铺选择 */}
                    <FormField
                      control={form.control}
                      name="store_id"
                      render={({ field }) => (
                        <FormItem className="relative">
                          <FormLabel>选择店铺 *</FormLabel>
                          <Select onValueChange={(value) => field.onChange(parseInt(value))} value={field.value?.toString()}>
                            <FormControl>
                              <SelectTrigger className="relative">
                                <SelectValue placeholder="请选择店铺" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent className="absolute z-50 min-w-full">
                              {filteredStores.map((store) => (
                                <SelectItem key={store.id} value={store.id.toString()}>
                                  {store.store_name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* SKU */}
                    <FormField
                      control={form.control}
                      name="sku"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>SKU *</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="产品SKU"
                              readOnly={mode === 'add'}
                              className={mode === 'add' ? 'bg-gray-50 cursor-not-allowed' : ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* EAN */}
                    <FormField
                      control={form.control}
                      name="ean"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>EAN{isRequired('ean') ? ' *' : ''}</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="EAN码"
                              readOnly
                              className="bg-gray-50 cursor-not-allowed"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 分类ID */}
                    <FormField
                      control={form.control}
                      name="platform_category_id"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>平台销售类目*</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="平台销售类目" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* 产品信息 */}
                <div id="product-info" className="form-section">
                  <div className="flex items-center text-base font-medium text-gray-900 mb-4">
                    <Type className="w-5 h-5 mr-2" />
                    <span>产品信息</span>
                  </div>

                  <div className="space-y-4">
                    {/* 英文标题 */}
                    <FormField
                      control={form.control}
                      name="english_title"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>英文标题{isRequired('english_title') ? ' *' : ''}</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="英文产品标题" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 英文描述 */}
                    <FormField
                      control={form.control}
                      name="english_description"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>英文描述{isRequired('english_description') ? ' *' : ''}</FormLabel>
                          <FormControl>
                            <Textarea {...field} placeholder="英文产品描述" rows={4} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 英文卖点 - 仅在非 Worten 平台显示 */}
                    {platform !== 'worten' && (
                      <div>
                        <Label className="text-sm font-medium text-gray-700 mb-2 block">英文卖点</Label>
                        <div className="space-y-2">
                          {[0, 1, 2, 3, 4].map((index) => (
                            <FormField
                              key={index}
                              control={form.control}
                              name={`selling_point.${index}`}
                              render={({ field }) => (
                                <FormItem>
                                  <FormControl>
                                    <Input {...field} placeholder={`第${index + 1}个卖点`} />
                                  </FormControl>
                                  <FormMessage />
                                </FormItem>
                              )}
                            />
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* 图片信息 */}
                <div id="image-info" className="form-section">
                  <div className="flex items-center text-base font-medium text-gray-900 mb-4">
                    <ImageIcon className="w-5 h-5 mr-2" />
                    <span>图片信息</span>
                  </div>

                  <div className="space-y-4">
                    {[1, 2, 3, 4, 5].map((index) => (
                      <div key={index} className="space-y-2">
                        <FormField
                          control={form.control}
                          name={`image${index}` as any}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel>图片 {index}</FormLabel>
                              <FormControl>
                                <Input {...field} placeholder={`图片${index}链接`} />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        {/* 图片预览 */}
                        {form.watch(`image${index}` as any) && (
                          <div className="mt-2">
                            <img
                              src={form.watch(`image${index}` as any)}
                              alt={`图片${index}预览`}
                              className="w-32 h-32 object-cover border rounded-md"
                              onError={(e) => {
                                e.currentTarget.style.display = 'none'
                              }}
                            />
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* 多语言信息 */}
                <div id="multilang-info" className="form-section">
                  <div className="flex items-center text-base font-medium text-gray-900 mb-4">
                    <Globe className="w-5 h-5 mr-2" />
                    <span>多语言信息</span>
                  </div>

                  <div className="space-y-6">
                    {/* 多语言标题 */}
                    <div className="border-t border-gray-200 pt-4">
                      <div className="flex justify-between items-center mb-4">
                        <div className="flex space-x-4">
                          {platformLanguages.map((lang) => (
                            <button
                              key={lang.code}
                              type="button"
                              className={`px-4 py-2 text-sm font-medium border-b-2 ${
                                activeLanguage === lang.code
                                  ? 'text-blue-600 border-blue-600'
                                  : 'text-gray-700 border-transparent hover:text-blue-600 hover:border-blue-600'
                              }`}
                              onClick={() => setActiveLanguage(lang.code)}
                            >
                              {lang.name}标题
                            </button>
                          ))}
                        </div>

                        {/* 翻译按钮 */}
                        <TranslationButton
                          text={form.watch('english_title') || ''}
                          sourceLang="en"
                          targetLangs={platformLanguages.map(lang => lang.code.toLowerCase() as LanguageCode)}
                          contentType="title"
                          platform={platform} // 传入平台参数
                          source="form_editing" // 表单编辑翻译场景
                          onTranslationComplete={(translations) => {
                            // 将翻译结果填充到对应的多语言字段
                            const currentTitles = form.getValues('multi_titles') || {}
                            const updatedTitles = { ...currentTitles }

                            Object.entries(translations).forEach(([langCode, translation]) => {
                              updatedTitles[langCode] = translation
                            })

                            form.setValue('multi_titles', updatedTitles)
                            toast.success('标题翻译完成')
                          }}
                          onTranslationError={(error) => {
                            toast.error(`标题翻译失败: ${error}`)
                          }}
                          size="sm"
                          disabled={!form.watch('english_title')}
                        />
                      </div>

                      {/* 动态多语言标题 */}
                      {platformLanguages.map((lang) => (
                        activeLanguage === lang.code && (
                          <FormField
                            key={lang.code}
                            control={form.control}
                            name={`multi_titles.${lang.code}` as any}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input {...field} placeholder={`${lang.name}标题`} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        )
                      ))}
                    </div>

                    {/* 多语言描述 */}
                    <div className="border-t border-gray-200 pt-4">
                      <div className="flex justify-between items-center mb-4">
                        <div className="flex space-x-4">
                          {platformLanguages.map((lang) => (
                            <button
                              key={lang.code}
                              type="button"
                              className={`px-4 py-2 text-sm font-medium border-b-2 ${
                                activeLanguage === lang.code
                                  ? 'text-blue-600 border-blue-600'
                                  : 'text-gray-700 border-transparent hover:text-blue-600 hover:border-blue-600'
                              }`}
                              onClick={() => setActiveLanguage(lang.code)}
                            >
                              {lang.name}描述
                            </button>
                          ))}
                        </div>

                        {/* 翻译按钮 */}
                        <TranslationButton
                          text={form.watch('english_description') || ''}
                          sourceLang="en"
                          targetLangs={platformLanguages.map(lang => lang.code.toLowerCase() as LanguageCode)}
                          contentType="description"
                          platform={platform} // 传入平台参数
                          source="form_editing" // 表单编辑翻译场景
                          onTranslationComplete={(translations) => {
                            // 将翻译结果填充到对应的多语言字段
                            const currentDescriptions = form.getValues('multi_descriptions') || {}
                            const updatedDescriptions = { ...currentDescriptions }

                            Object.entries(translations).forEach(([langCode, translation]) => {
                              updatedDescriptions[langCode] = translation
                            })

                            form.setValue('multi_descriptions', updatedDescriptions)
                            toast.success('描述翻译完成')
                          }}
                          onTranslationError={(error) => {
                            toast.error(`描述翻译失败: ${error}`)
                          }}
                          size="sm"
                          disabled={!form.watch('english_description')}
                        />
                      </div>

                      {/* 动态多语言描述 */}
                      {platformLanguages.map((lang) => (
                        activeLanguage === lang.code && (
                          <FormField
                            key={lang.code}
                            control={form.control}
                            name={`multi_descriptions.${lang.code}` as any}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Textarea {...field} placeholder={`${lang.name}描述`} rows={4} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />
                        )
                      ))}
                    </div>

                    {/* 多语言卖点 - 仅在非 Worten 平台显示 */}
                    {platform !== 'worten' && (
                      <div className="border-t border-gray-200 pt-4">
                        <div className="flex justify-between items-center mb-4">
                          <div className="flex space-x-4">
                            {platformLanguages.map((lang) => (
                              <button
                                key={lang.code}
                                type="button"
                                className={`px-4 py-2 text-sm font-medium border-b-2 ${
                                  activeLanguage === lang.code
                                    ? 'text-blue-600 border-blue-600'
                                    : 'text-gray-700 border-transparent hover:text-blue-600 hover:border-blue-600'
                                }`}
                                onClick={() => setActiveLanguage(lang.code)}
                              >
                                {lang.name}卖点
                              </button>
                            ))}
                          </div>

                          {/* 翻译按钮 */}
                          <TranslationButton
                            text={(form.watch('selling_point') || []).filter(point => point.trim()).join('\n')}
                            sourceLang="en"
                            targetLangs={platformLanguages.map(lang => lang.code.toLowerCase() as LanguageCode)}
                            contentType="selling_point"
                            platform={platform} // 传入平台参数
                            source="form_editing" // 表单编辑翻译场景
                            onTranslationComplete={(translations) => {
                              // 处理卖点翻译结果
                              const currentMultiSellingPoints = form.getValues('multi_selling_points') || {}
                              const updatedMultiSellingPoints = { ...currentMultiSellingPoints }

                              Object.entries(translations).forEach(([langCode, translatedText]) => {
                                // 将翻译结果按行分割为数组
                                const points = (translatedText as string).split('\n').filter(point => point.trim())
                                // 确保有5个卖点，不足的用空字符串补充
                                while (points.length < 5) {
                                  points.push('')
                                }
                                updatedMultiSellingPoints[langCode.toUpperCase()] = points.slice(0, 5)
                              })

                              form.setValue('multi_selling_points', updatedMultiSellingPoints)
                              toast.success('卖点翻译完成')
                            }}
                          />
                        </div>

                        {/* 动态多语言卖点 */}
                        {platformLanguages.map((lang) => (
                          activeLanguage === lang.code && (
                            <div key={lang.code} className="space-y-2">
                              {[0, 1, 2, 3, 4].map((index) => (
                                <FormField
                                  key={`${lang.code}-${index}`}
                                  control={form.control}
                                  name={`multi_selling_points.${lang.code}.${index}` as any}
                                  render={({ field }) => (
                                    <FormItem>
                                      <FormControl>
                                        <Input {...field} placeholder={`${lang.name}第${index + 1}个卖点`} />
                                      </FormControl>
                                      <FormMessage />
                                    </FormItem>
                                  )}
                                />
                              ))}
                            </div>
                          )
                        ))}
                      </div>
                    )}
                  </div>
                </div>

                {/* 产品参数 */}
                <div id="params-info" className="form-section">
                  <div className="flex items-center text-base font-medium text-gray-900 mb-4">
                    <Box className="w-5 h-5 mr-2" />
                    <span>产品参数</span>
                  </div>
                  <div className="text-gray-500 text-sm">
                    产品参数功能正在开发中，将根据不同类目动态显示参数字段...
                  </div>
                </div>

                {/* 平台报价 */}
                <div id="price-info" className="form-section">
                  <div className="flex items-center text-base font-medium text-gray-900 mb-4">
                    <DollarSign className="w-5 h-5 mr-2" />
                    <span>平台报价</span>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    {/* 平台SKU */}
                    <FormField
                      control={form.control}
                      name="platform_sku"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>平台SKU</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="平台SKU" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 平台EAN */}
                    <FormField
                      control={form.control}
                      name="platform_ean"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>平台EAN</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="平台EAN" />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 折扣售价 */}
                    <FormField
                      control={form.control}
                      name="discounted_price"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>折扣售价</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="number"
                              step="0.01"
                              placeholder="折扣售价"
                              className={numberInputClass}
                              onChange={(e) => {
                                const discountedPrice = parseFloat(e.target.value) || 0
                                field.onChange(discountedPrice)

                                // 自动计算折扣比例
                                const originalPrice = form.getValues('original_price') || 0
                                if (originalPrice > 0 && discountedPrice > 0) {
                                  const discountPercentage = ((originalPrice - discountedPrice) / originalPrice) * 100
                                  form.setValue('discount_percentage', Math.round(discountPercentage * 100) / 100)
                                }
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 折后原价 */}
                    <FormField
                      control={form.control}
                      name="original_price"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>折后原价</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="number"
                              step="0.01"
                              placeholder="折后原价"
                              className={numberInputClass}
                              onChange={(e) => {
                                const originalPrice = parseFloat(e.target.value) || 0
                                field.onChange(originalPrice)

                                // 自动计算折扣比例
                                const discountedPrice = form.getValues('discounted_price') || 0
                                if (originalPrice > 0 && discountedPrice > 0) {
                                  const discountPercentage = ((originalPrice - discountedPrice) / originalPrice) * 100
                                  form.setValue('discount_percentage', Math.round(discountPercentage * 100) / 100)
                                }
                              }}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 库存 */}
                    <FormField
                      control={form.control}
                      name="stock_quantity"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>库存</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="number"
                              placeholder="库存数量"
                              className={numberInputClass}
                              onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 折扣比例 */}
                    <FormField
                      control={form.control}
                      name="discount_percentage"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>折扣比例 (%) - 自动计算</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="number"
                              step="0.01"
                              max="100"
                              min="0"
                              placeholder="自动计算"
                              readOnly
                              className={`bg-gray-50 cursor-not-allowed ${numberInputClass}`}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 折扣开始日期 */}
                    <FormField
                      control={form.control}
                      name="discount_start_date"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>折扣开始日期</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="datetime-local"
                              placeholder="选择开始日期"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* 折扣结束日期 */}
                    <FormField
                      control={form.control}
                      name="discount_end_date"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>折扣结束日期</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              type="datetime-local"
                              placeholder="选择结束日期"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </form>
            </Form>
          </div>

          {/* 右侧 - 快捷操作区域 */}
          <div className="w-40 border-l bg-gray-50 p-4">
            <div className="space-y-6">
              {/* 快速导航 */}
              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-3">快速导航</h3>
                <div className="space-y-2">
                  <button
                    type="button"
                    onClick={() => scrollToSection('basic-info')}
                    className={NAV_BUTTON_CLASS}
                  >
                    <Info className="w-4 h-4 inline mr-2" />
                    基本信息
                  </button>
                  <button
                    type="button"
                    onClick={() => scrollToSection('product-info')}
                    className={NAV_BUTTON_CLASS}
                  >
                    <Type className="w-4 h-4 inline mr-2" />
                    产品信息
                  </button>
                  <button
                    type="button"
                    onClick={() => scrollToSection('image-info')}
                    className={NAV_BUTTON_CLASS}
                  >
                    <ImageIcon className="w-4 h-4 inline mr-2" />
                    图片信息
                  </button>
                  <button
                    type="button"
                    onClick={() => scrollToSection('multilang-info')}
                    className={NAV_BUTTON_CLASS}
                  >
                    <Globe className="w-4 h-4 inline mr-2" />
                    多语言信息
                  </button>
                  <button
                    type="button"
                    onClick={() => scrollToSection('params-info')}
                    className={NAV_BUTTON_CLASS}
                  >
                    <Box className="w-4 h-4 inline mr-2" />
                    产品参数
                  </button>
                  <button
                    type="button"
                    onClick={() => scrollToSection('price-info')}
                    className={NAV_BUTTON_CLASS}
                  >
                    <DollarSign className="w-4 h-4 inline mr-2" />
                    平台报价
                  </button>
                </div>
              </div>

              {/* 语言切换 */}
              <div>
                <h3 className="text-sm font-medium text-gray-900 mb-3">语言切换</h3>
                <div className="space-y-2">
                  {platformLanguages.map((lang) => (
                    <button
                      key={lang.code}
                      type="button"
                      onClick={() => setActiveLanguage(lang.code)}
                      className={getLangButtonClass(lang.code)}
                    >
                      <Globe className="w-4 h-4 inline mr-2" />
                      {lang.name}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="px-6 py-4 border-t">
          <Button type="button" variant="outline" onClick={handleClose}>
            取消
          </Button>
          <Button
            type="button"
            onClick={form.handleSubmit(handleSubmit)}
            disabled={loading}
          >
            {loading && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
            {editingProduct ? '更新产品' : '认领产品'}
          </Button>
        </DialogFooter>

        {/* 关闭按钮 */}
        <DialogPrimitive.Close className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </DialogPrimitive.Close>
        </DialogPrimitive.Content>
      </DialogPortal>

      {/* 翻译功能已简化，使用字段旁的翻译按钮 */}
    </Dialog>
  )
}
