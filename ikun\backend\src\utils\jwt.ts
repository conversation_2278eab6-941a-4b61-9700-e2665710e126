/**
 * JWT工具函数
 * 解决JWT类型问题
 */

import jwt from 'jsonwebtoken';
import type { StringValue } from 'ms';
import { config } from '@/config/config';

export interface JWTPayload {
  id: number;
  username: string;
  email: string;
  role: string;
  type?: string;
}

export class JWTUtils {
  /**
   * 生成访问token
   */
  static generateAccessToken(payload: Omit<JWTPayload, 'type'>): string {
    return jwt.sign(
      payload,
      config.jwt.secret,
      { expiresIn: config.jwt.expiresIn as StringValue }
    );
  }

  /**
   * 生成刷新token
   */
  static generateRefreshToken(userId: number): string {
    return jwt.sign(
      { id: userId, type: 'refresh' },
      config.jwt.secret,
      { expiresIn: config.jwt.refreshExpiresIn as StringValue }
    );
  }

  /**
   * 验证token
   */
  static verifyToken(token: string): JWTPayload {
    return jwt.verify(token, config.jwt.secret) as JWTPayload;
  }
}
