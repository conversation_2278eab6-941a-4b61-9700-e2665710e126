/**
 * User Model
 * 用户数据模型 - 支持认证和权限管理
 */

import { BaseModel, QueryOptions } from '@/models/base/BaseModel';
import { User } from '@/types';
import { database } from '@/database/connection';
import { logger } from '@/utils/logger';
import { ConflictError, BadRequestError } from '@/middleware/error/errorHandler';
import bcrypt from 'bcryptjs';

export class UserModel extends BaseModel<User> {
  protected tableName = 'users';
  protected primaryKey = 'id';
  protected timestamps = true;
  
  protected fillable = [
    'username',
    'email',
    'password_hash',
    'role',
    'last_login',
    'is_active'
  ];

  protected hidden = ['password_hash'];

  /**
   * 根据用户名查找用户
   */
  public async findByUsername(username: string): Promise<User | null> {
    return await this.findOne({ username });
  }

  /**
   * 根据邮箱查找用户
   */
  public async findByEmail(email: string): Promise<User | null> {
    return await this.findOne({ email });
  }

  /**
   * 检查用户名是否存在
   */
  public async usernameExists(username: string, excludeId?: number): Promise<boolean> {
    if (excludeId) {
      const sql = `SELECT COUNT(*) as count FROM ${this.tableName} WHERE username = ? AND id != ?`;
      const result = await database.queryOne<{ count: number }>(sql, [username, excludeId]);
      return (result?.count || 0) > 0;
    }
    
    const result = await this.findOne({ username });
    return result !== null;
  }

  /**
   * 检查邮箱是否存在
   */
  public async emailExists(email: string, excludeId?: number): Promise<boolean> {
    if (excludeId) {
      const sql = `SELECT COUNT(*) as count FROM ${this.tableName} WHERE email = ? AND id != ?`;
      const result = await database.queryOne<{ count: number }>(sql, [email, excludeId]);
      return (result?.count || 0) > 0;
    }
    
    const result = await this.findOne({ email });
    return result !== null;
  }

  /**
   * 创建用户（包含密码加密）
   */
  public async createUser(userData: {
    username: string;
    email: string;
    password: string;
    role?: 'admin' | 'user' | 'viewer';
  }): Promise<User> {
    // 验证用户名和邮箱唯一性
    if (await this.usernameExists(userData.username)) {
      throw new ConflictError(`Username '${userData.username}' already exists`);
    }

    if (await this.emailExists(userData.email)) {
      throw new ConflictError(`Email '${userData.email}' already exists`);
    }

    // 加密密码
    const saltRounds = 12;
    const password_hash = await bcrypt.hash(userData.password, saltRounds);

    const user = await this.create({
      username: userData.username,
      email: userData.email,
      password_hash,
      role: userData.role || 'user',
      is_active: true
    } as Partial<User>);

    logger.info('User created:', { userId: user.id, username: user.username });
    return user;
  }

  /**
   * 验证用户密码
   */
  public async verifyPassword(username: string, password: string): Promise<User | null> {
    // 查找用户（包含密码哈希）
    const sql = `SELECT * FROM ${this.tableName} WHERE username = ? AND is_active = 1`;
    const user = await database.queryOne<User>(sql, [username]);

    if (!user) {
      return null;
    }

    // 验证密码
    const isValid = await bcrypt.compare(password, user.password_hash);
    if (!isValid) {
      return null;
    }

    // 更新最后登录时间
    await this.updateLastLogin(user.id);

    // 返回用户信息（不包含密码哈希）
    return this.formatOutput(user);
  }

  /**
   * 更新用户密码
   */
  public async updatePassword(id: number | string, newPassword: string): Promise<User> {
    const saltRounds = 12;
    const password_hash = await bcrypt.hash(newPassword, saltRounds);

    const user = await this.update(id, { password_hash } as Partial<User>);
    
    logger.info('User password updated:', { userId: id });
    return user;
  }

  /**
   * 更新最后登录时间
   */
  public async updateLastLogin(id: number | string): Promise<void> {
    const sql = `
      UPDATE ${this.tableName} 
      SET last_login = CURRENT_TIMESTAMP, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `;
    
    await database.execute(sql, [id]);
  }

  /**
   * 激活/禁用用户
   */
  public async updateActiveStatus(id: number | string, isActive: boolean): Promise<User> {
    const user = await this.update(id, { is_active: isActive } as Partial<User>);
    
    logger.info('User active status updated:', { userId: id, isActive });
    return user;
  }

  /**
   * 更新用户角色
   */
  public async updateRole(id: number | string, role: 'admin' | 'user' | 'viewer'): Promise<User> {
    const user = await this.update(id, { role } as Partial<User>);
    
    logger.info('User role updated:', { userId: id, role });
    return user;
  }

  /**
   * 根据角色获取用户
   */
  public async findByRole(role: 'admin' | 'user' | 'viewer', options: QueryOptions = {}): Promise<User[]> {
    const whereCondition = { role, ...options.where };
    return await this.findMany({ ...options, where: whereCondition });
  }

  /**
   * 获取活跃用户
   */
  public async findActiveUsers(options: QueryOptions = {}): Promise<User[]> {
    const whereCondition = { is_active: true, ...options.where };
    return await this.findMany({ ...options, where: whereCondition });
  }

  /**
   * 搜索用户
   */
  public async searchUsers(query: string, options: QueryOptions = {}): Promise<User[]> {
    const { page = 1, limit = 20 } = options;
    const offset = (page - 1) * limit;

    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE (username LIKE ? OR email LIKE ?)
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const searchParam = `%${query}%`;
    const users = await database.query<User>(sql, [searchParam, searchParam, limit, offset]);

    return users.map(user => this.formatOutput(user));
  }

  /**
   * 获取用户统计信息
   */
  public async getUserStatistics(): Promise<{
    total: number;
    active: number;
    inactive: number;
    by_role: Record<string, number>;
    recent_logins: number;
  }> {
    // 总用户数
    const totalResult = await database.queryOne<{ total: number }>(
      `SELECT COUNT(*) as total FROM ${this.tableName}`
    );
    const total = totalResult?.total || 0;

    // 活跃用户数
    const activeResult = await database.queryOne<{ active: number }>(
      `SELECT COUNT(*) as active FROM ${this.tableName} WHERE is_active = 1`
    );
    const active = activeResult?.active || 0;

    // 非活跃用户数
    const inactive = total - active;

    // 按角色统计
    const roleStats = await database.query<{ role: string; count: number }>(
      `SELECT role, COUNT(*) as count FROM ${this.tableName} GROUP BY role`
    );
    const by_role: Record<string, number> = {};
    roleStats.forEach(stat => {
      by_role[stat.role] = stat.count;
    });

    // 最近7天登录用户数
    const recentLoginsResult = await database.queryOne<{ recent: number }>(
      `SELECT COUNT(*) as recent FROM ${this.tableName} WHERE last_login >= DATE_SUB(NOW(), INTERVAL 7 DAY)`
    );
    const recent_logins = recentLoginsResult?.recent || 0;

    return {
      total,
      active,
      inactive,
      by_role,
      recent_logins
    };
  }

  /**
   * 验证用户权限
   */
  public async hasPermission(userId: number | string, requiredRole: 'admin' | 'user' | 'viewer'): Promise<boolean> {
    const user = await this.findById(userId);
    if (!user || !user.is_active) {
      return false;
    }

    // 权限级别：admin > user > viewer
    const roleHierarchy = {
      'admin': 3,
      'user': 2,
      'viewer': 1
    };

    const userLevel = roleHierarchy[user.role] || 0;
    const requiredLevel = roleHierarchy[requiredRole] || 0;

    return userLevel >= requiredLevel;
  }

  /**
   * 批量创建用户
   */
  public async batchCreateUsers(users: Array<{
    username: string;
    email: string;
    password: string;
    role?: 'admin' | 'user' | 'viewer';
  }>): Promise<User[]> {
    const results: User[] = [];
    const errors: Array<{ index: number; error: string; data: any }> = [];

    await this.transaction(async (connection) => {
      for (let i = 0; i < users.length; i++) {
        const userData = users[i];
        
        try {
          const user = await this.createUser(userData);
          results.push(user);
        } catch (error) {
          errors.push({
            index: i,
            error: (error as Error).message,
            data: userData
          });
          
          logger.warn('Failed to create user in batch:', {
            index: i,
            username: userData.username,
            error: (error as Error).message
          });
        }
      }
    });

    if (errors.length > 0) {
      logger.error('Batch user creation completed with errors:', {
        total: users.length,
        success: results.length,
        errors: errors.length,
        errorDetails: errors
      });
    }

    return results;
  }

  /**
   * 重写更新方法，添加验证
   */
  public async update(id: number | string, data: Partial<User>): Promise<User> {
    const current = await this.findByIdOrFail(id);
    
    // 用户名唯一性验证（排除自身）
    if (data.username && data.username !== current.username && await this.usernameExists(data.username, Number(id))) {
      throw new ConflictError(`Username '${data.username}' already exists`);
    }

    // 邮箱唯一性验证（排除自身）
    if (data.email && data.email !== current.email && await this.emailExists(data.email, Number(id))) {
      throw new ConflictError(`Email '${data.email}' already exists`);
    }

    return await super.update(id, data);
  }

  /**
   * 重写删除方法，添加保护
   */
  public async delete(id: number | string): Promise<void> {
    const user = await this.findByIdOrFail(id);
    
    // 防止删除最后一个管理员
    if (user.role === 'admin') {
      const adminCount = await this.count({ role: 'admin' });
      if (adminCount <= 1) {
        throw new BadRequestError('Cannot delete the last admin user');
      }
    }

    await super.delete(id);
    logger.info('User deleted:', { userId: id, username: user.username });
  }
}

export default UserModel;
