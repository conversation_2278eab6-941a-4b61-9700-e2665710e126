/**
 * Store Service
 * 店铺管理服务层
 */

import { Store, PaginationQuery, PaginationResult } from '@/types';
import { database } from '@/database/connection';
import { logger } from '@/utils/logger';
import { NotFoundError, ConflictError } from '@/middleware/error/errorHandler';

interface StoreQuery extends PaginationQuery {
  platform_code?: string;
  status?: 'active' | 'failed';
  search?: string;
}

class StoreService {
  /**
   * 获取店铺列表
   */
  public async getStores(query: StoreQuery): Promise<PaginationResult<Store>> {
    try {
      const {
        page = 1,
        limit = 20,
        platform_code,
        status,
        search
      } = query;

      const offset = (page - 1) * limit;
      let whereConditions: string[] = [];
      let params: any[] = [];

      // 构建查询条件
      if (platform_code) {
        whereConditions.push('s.platform_code = ?');
        params.push(platform_code);
      }

      if (status) {
        whereConditions.push('s.status = ?');
        params.push(status);
      }

      if (search) {
        whereConditions.push('(s.store_name LIKE ? OR s.platform_code LIKE ?)');
        params.push(`%${search}%`, `%${search}%`);
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      // 获取总数
      const countRows = await database.executeQuery<{ total: number }>(`
        SELECT COUNT(*) as total
        FROM stores_updata s
        ${whereClause}
      `, params);

      const total = countRows[0].total;

      // 获取数据
      const dataParams = [...params];
      let limitClause = '';
      if (limit && offset !== undefined) {
        limitClause = `LIMIT ${limit} OFFSET ${offset}`;
      }

      const rows = await database.executeQuery(`
        SELECT
          s.id, s.platform_code, s.store_name, s.status,
          s.api_key, s.token, s.token_expires_at, s.site,
          s.platform_config, s.created_at, s.updated_at,
          p.platform_name, p.platform_name_en, p.logo_url
        FROM stores_updata s
        LEFT JOIN stores_platforms p ON s.platform_code = p.platform_code
        ${whereClause}
        ORDER BY s.created_at DESC
        ${limitClause}
      `, dataParams);

      const stores = (rows as any[]).map(row => {
        // 调试信息 - 改为debug级别，避免生产环境输出过多日志
        logger.debug('Processing row platform_config:', {
          type: typeof row.platform_config,
          value: row.platform_config
        });

        let parsedConfig = null;
        if (row.platform_config) {
          try {
            if (typeof row.platform_config === 'string') {
              parsedConfig = JSON.parse(row.platform_config);
            } else {
              parsedConfig = row.platform_config;
            }
          } catch (error) {
            logger.error('Failed to parse platform_config:', {
              error,
              value: row.platform_config,
              type: typeof row.platform_config
            });
            parsedConfig = null;
          }
        }

        return {
          ...row,
          platform_config: parsedConfig
        };
      });

      return {
        data: stores,
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit)
        }
      };
    } catch (error) {
      logger.error('Failed to get stores:', error);
      throw error;
    }
  }

  /**
   * 根据ID获取店铺详情
   */
  public async getStoreById(id: number): Promise<Store> {
    try {
      const stores = await database.executeQuery(`
        SELECT
          s.id, s.platform_code, s.store_name, s.status,
          s.api_key, s.token, s.token_expires_at, s.site,
          s.platform_config, s.created_at, s.updated_at,
          p.platform_name, p.platform_name_en, p.logo_url, p.config_fields
        FROM stores_updata s
        LEFT JOIN stores_platforms p ON s.platform_code = p.platform_code
        WHERE s.id = ?
      `, [id]);

      if (stores.length === 0) {
        throw new NotFoundError(`Store with id ${id} not found`);
      }

      const store = stores[0] as any;
      return {
        ...store,
        platform_config: store.platform_config ?
          (typeof store.platform_config === 'string' ? JSON.parse(store.platform_config) : store.platform_config)
          : null,
        config_fields: store.config_fields ?
          (typeof store.config_fields === 'string' ? JSON.parse(store.config_fields) : store.config_fields)
          : null
      };
    } catch (error) {
      logger.error('Failed to get store by id:', error);
      throw error;
    }
  }

  /**
   * 创建店铺
   */
  public async createStore(storeData: Partial<Store>): Promise<Store> {
    try {
      const result = await database.executeUpdate(`
        INSERT INTO stores_updata (
          platform_code, store_name, status, api_key, token,
          token_expires_at, site, platform_config
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        storeData.platform_code,
        storeData.store_name,
        storeData.status || 'active',
        storeData.api_key || null,
        storeData.token || null,
        storeData.token_expires_at || null,
        storeData.site || null,
        storeData.platform_config ? JSON.stringify(storeData.platform_config) : null
      ]);

      return await this.getStoreById(result.insertId as number);
    } catch (error) {
      logger.error('Failed to create store:', error);
      throw error;
    }
  }

  /**
   * 更新店铺
   */
  public async updateStore(id: number, updateData: Partial<Store>): Promise<Store> {
    try {
      const store = await this.getStoreById(id);

      await database.executeUpdate(`
        UPDATE stores_updata SET
          store_name = ?,
          status = ?,
          api_key = ?,
          token = ?,
          token_expires_at = ?,
          site = ?,
          platform_config = ?
        WHERE id = ?
      `, [
        updateData.store_name || store.store_name,
        updateData.status || store.status,
        updateData.api_key !== undefined ? updateData.api_key : store.api_key,
        updateData.token !== undefined ? updateData.token : store.token,
        updateData.token_expires_at !== undefined ? updateData.token_expires_at : store.token_expires_at,
        updateData.site !== undefined ? updateData.site : store.site,
        updateData.platform_config ? JSON.stringify(updateData.platform_config) : store.platform_config,
        id
      ]);

      return await this.getStoreById(id);
    } catch (error) {
      logger.error('Failed to update store:', error);
      throw error;
    }
  }

  /**
   * 删除店铺
   */
  public async deleteStore(id: number): Promise<void> {
    try {
      await this.getStoreById(id); // 检查店铺是否存在

      await database.executeUpdate('DELETE FROM stores_updata WHERE id = ?', [id]);
    } catch (error) {
      logger.error('Failed to delete store:', error);
      throw error;
    }
  }

  /**
   * 更新店铺状态
   */
  public async updateStoreStatus(id: number, status: 'active' | 'failed'): Promise<Store> {
    try {
      await this.getStoreById(id); // 检查店铺是否存在

      await database.executeUpdate(`
        UPDATE stores_updata SET status = ? WHERE id = ?
      `, [status, id]);

      return await this.getStoreById(id);
    } catch (error) {
      logger.error('Failed to update store status:', error);
      throw error;
    }
  }

  /**
   * 同步店铺数据
   */
  public async syncStore(id: number): Promise<{ message: string; status: string }> {
    try {
      const store = await this.getStoreById(id);
      
      // TODO: 实现具体的同步逻辑，根据不同平台调用不同的同步方法
      logger.info('Store sync initiated:', { storeId: id, platform: store.platform_code });

      return {
        message: 'Store sync initiated successfully',
        status: 'pending'
      };
    } catch (error) {
      logger.error('Failed to sync store:', error);
      throw error;
    }
  }
}

export const storeService = new StoreService();
