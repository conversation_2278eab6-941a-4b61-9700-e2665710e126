# 组件开发规范

## 📁 文件结构规范

### 组件文件命名
```
src/components/
├── ui/                     # Shadcn/ui 基础组件
│   ├── button.tsx
│   ├── card.tsx
│   └── input.tsx
├── layout/                 # 布局组件
│   ├── dashboard-layout.tsx
│   ├── sidebar.tsx
│   └── header.tsx
├── products/               # 业务组件 (按功能模块分组)
│   ├── products-table.tsx
│   ├── products-header.tsx
│   ├── product-form.tsx
│   └── index.ts           # 导出文件
└── common/                 # 通用业务组件
    ├── data-table.tsx
    ├── search-filter.tsx
    └── status-badge.tsx
```

### 组件命名规范
- **文件名**: 使用 kebab-case (小写+连字符)
- **组件名**: 使用 PascalCase (大驼峰)
- **Props接口**: 组件名 + Props

```typescript
// 文件名: product-form.tsx
export interface ProductFormProps {
  // ...
}

export function ProductForm(props: ProductFormProps) {
  // ...
}
```

## 🏗️ 组件结构模板

### 基础组件模板
```typescript
'use client'

import { useState } from 'react'
import { cn } from '@/lib/utils'

// 类型定义
interface ComponentNameProps {
  className?: string
  children?: React.ReactNode
  // 其他props
}

// 主组件
export function ComponentName({ 
  className,
  children,
  ...props 
}: ComponentNameProps) {
  // 状态管理
  const [state, setState] = useState()

  // 事件处理
  const handleEvent = () => {
    // 处理逻辑
  }

  // 渲染
  return (
    <div className={cn("default-classes", className)} {...props}>
      {children}
    </div>
  )
}

// 默认导出
ComponentName.displayName = "ComponentName"
```

### 复杂组件模板
```typescript
'use client'

import { useState, useEffect, useCallback } from 'react'
import { useQuery } from '@tanstack/react-query'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import type { Product } from '@/types'

// Props接口
interface ProductTableProps {
  className?: string
  onProductSelect?: (product: Product) => void
  filters?: ProductFilters
}

// 内部类型
interface ProductFilters {
  status?: string
  category?: string
  search?: string
}

// 主组件
export function ProductTable({ 
  className,
  onProductSelect,
  filters 
}: ProductTableProps) {
  // 状态管理
  const [selectedProducts, setSelectedProducts] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1)

  // 数据获取
  const { data, isLoading, error } = useQuery({
    queryKey: ['products', filters, currentPage],
    queryFn: () => fetchProducts({ ...filters, page: currentPage })
  })

  // 事件处理
  const handleSelectProduct = useCallback((productId: string) => {
    setSelectedProducts(prev => 
      prev.includes(productId)
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    )
  }, [])

  // 副作用
  useEffect(() => {
    if (selectedProducts.length > 0 && onProductSelect) {
      const selectedProduct = data?.find(p => p.id === selectedProducts[0])
      if (selectedProduct) {
        onProductSelect(selectedProduct)
      }
    }
  }, [selectedProducts, data, onProductSelect])

  // 加载状态
  if (isLoading) {
    return <div>Loading...</div>
  }

  // 错误状态
  if (error) {
    return <div>Error: {error.message}</div>
  }

  // 主渲染
  return (
    <Card className={cn("product-table", className)}>
      {/* 组件内容 */}
    </Card>
  )
}

// 显示名称
ProductTable.displayName = "ProductTable"
```

## 🎨 样式规范

### Tailwind CSS 使用
```typescript
// ✅ 推荐: 使用 cn 工具函数合并类名
<div className={cn(
  "flex items-center justify-between p-4",
  "border rounded-lg bg-card",
  isActive && "bg-primary text-primary-foreground",
  className
)}>

// ❌ 避免: 直接字符串拼接
<div className={`flex items-center ${isActive ? 'bg-primary' : 'bg-card'} ${className}`}>
```

### 响应式设计
```typescript
// 移动端优先的响应式设计
<div className={cn(
  "grid grid-cols-1 gap-4",      // 移动端: 1列
  "md:grid-cols-2",              // 平板: 2列
  "lg:grid-cols-3",              // 桌面: 3列
  "xl:grid-cols-4"               // 大屏: 4列
)}>
```

### 主题支持
```typescript
// 支持深色模式的样式
<div className={cn(
  "bg-white text-gray-900",           // 浅色模式
  "dark:bg-gray-900 dark:text-white"  // 深色模式
)}>
```

## 🔧 Props 设计规范

### Props 接口设计
```typescript
interface ComponentProps {
  // 必需属性
  id: string
  title: string
  
  // 可选属性
  description?: string
  className?: string
  
  // 事件处理
  onClick?: (event: MouseEvent) => void
  onSubmit?: (data: FormData) => Promise<void>
  
  // 子组件
  children?: React.ReactNode
  
  // 变体
  variant?: 'default' | 'primary' | 'secondary'
  size?: 'sm' | 'md' | 'lg'
  
  // 状态
  loading?: boolean
  disabled?: boolean
  error?: string
}
```

### 默认值处理
```typescript
// ✅ 推荐: 使用解构赋值设置默认值
export function Component({ 
  variant = 'default',
  size = 'md',
  disabled = false,
  ...props 
}: ComponentProps) {
  // ...
}

// ✅ 推荐: 复杂默认值使用 useMemo
const defaultConfig = useMemo(() => ({
  pageSize: 10,
  sortBy: 'createdAt',
  sortOrder: 'desc'
}), [])
```

## 🎯 状态管理规范

### 本地状态
```typescript
// ✅ 简单状态使用 useState
const [isOpen, setIsOpen] = useState(false)
const [formData, setFormData] = useState<FormData>({})

// ✅ 复杂状态使用 useReducer
const [state, dispatch] = useReducer(reducer, initialState)
```

### 全局状态
```typescript
// ✅ 使用 Zustand 管理全局状态
import { useStore } from '@/store/useStore'

export function Component() {
  const { user, setUser } = useStore()
  // ...
}
```

### 服务器状态
```typescript
// ✅ 使用 React Query 管理服务器状态
const { data, isLoading, error, mutate } = useQuery({
  queryKey: ['products'],
  queryFn: fetchProducts
})
```

## 🔄 事件处理规范

### 事件命名
```typescript
interface ComponentProps {
  // 标准事件: on + 动词
  onClick?: () => void
  onSubmit?: (data: FormData) => void
  onChange?: (value: string) => void
  
  // 自定义事件: on + 名词 + 动词
  onProductSelect?: (product: Product) => void
  onTaskComplete?: (taskId: string) => void
}
```

### 事件处理优化
```typescript
// ✅ 使用 useCallback 优化事件处理
const handleClick = useCallback((id: string) => {
  onItemClick?.(id)
}, [onItemClick])

// ✅ 防抖处理
const debouncedSearch = useMemo(
  () => debounce((query: string) => {
    onSearch?.(query)
  }, 300),
  [onSearch]
)
```

## 📝 文档和注释

### 组件文档
```typescript
/**
 * 产品表格组件
 * 
 * @description 显示产品列表，支持搜索、筛选、分页和批量操作
 * @example
 * ```tsx
 * <ProductTable 
 *   filters={{ status: 'active' }}
 *   onProductSelect={(product) => console.log(product)}
 * />
 * ```
 */
export function ProductTable(props: ProductTableProps) {
  // ...
}
```

### 复杂逻辑注释
```typescript
// 计算分页信息
// 确保当前页不超过总页数，避免空白页面
const currentPage = useMemo(() => {
  const maxPage = Math.ceil(total / pageSize)
  return Math.min(page, maxPage)
}, [page, total, pageSize])
```

## ✅ 最佳实践

### 性能优化
```typescript
// ✅ 使用 React.memo 优化重渲染
export const ProductCard = React.memo(function ProductCard({ product }: Props) {
  return <div>{product.title}</div>
})

// ✅ 使用 useMemo 缓存计算结果
const expensiveValue = useMemo(() => {
  return heavyCalculation(data)
}, [data])

// ✅ 使用 useCallback 缓存函数
const handleSubmit = useCallback(async (data: FormData) => {
  await submitForm(data)
}, [])
```

### 错误处理
```typescript
// ✅ 组件级错误边界
export function ComponentWithErrorBoundary() {
  return (
    <ErrorBoundary fallback={<ErrorFallback />}>
      <Component />
    </ErrorBoundary>
  )
}

// ✅ 优雅的错误状态
if (error) {
  return (
    <div className="p-4 text-center">
      <p className="text-destructive">加载失败</p>
      <Button onClick={retry} variant="outline">
        重试
      </Button>
    </div>
  )
}
```

### 可访问性
```typescript
// ✅ 添加适当的 ARIA 属性
<button
  aria-label="删除产品"
  aria-describedby="delete-help"
  onClick={handleDelete}
>
  <Trash2 className="h-4 w-4" />
</button>

// ✅ 键盘导航支持
<div
  role="button"
  tabIndex={0}
  onKeyDown={(e) => {
    if (e.key === 'Enter' || e.key === ' ') {
      handleClick()
    }
  }}
>
```

## 🧪 测试规范

### 组件测试
```typescript
// ProductCard.test.tsx
import { render, screen } from '@testing-library/react'
import { ProductCard } from './ProductCard'

describe('ProductCard', () => {
  const mockProduct = {
    id: '1',
    title: 'Test Product',
    price: 99.99
  }

  it('renders product information', () => {
    render(<ProductCard product={mockProduct} />)
    
    expect(screen.getByText('Test Product')).toBeInTheDocument()
    expect(screen.getByText('¥99.99')).toBeInTheDocument()
  })

  it('calls onSelect when clicked', () => {
    const onSelect = jest.fn()
    render(<ProductCard product={mockProduct} onSelect={onSelect} />)
    
    fireEvent.click(screen.getByRole('button'))
    expect(onSelect).toHaveBeenCalledWith(mockProduct)
  })
})
```

---

**记住**: 一致的组件开发规范能提高代码质量和团队协作效率！
