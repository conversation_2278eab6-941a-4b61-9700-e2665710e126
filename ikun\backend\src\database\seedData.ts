/**
 * 数据库种子数据
 * 插入测试用的分类和店铺数据
 */

import Models from '@/models';
import { database } from './connection';
import { logger } from '@/utils/logger';
import bcrypt from 'bcryptjs';

export class SeedDataManager {
  /**
   * 插入所有种子数据
   */
  public async seedAll(): Promise<void> {
    try {
      logger.info('Starting to seed test data...');
      
      await this.seedUsers();
      await this.seedSystemSettings();

      logger.info('Test data seeded successfully');
    } catch (error) {
      logger.error('Failed to seed test data:', error);
      throw error;
    }
  }

  /**
   * 插入用户数据
   */
  private async seedUsers(): Promise<void> {
    try {
      // 检查是否已有用户数据
      const existingUsers = await database.query('SELECT COUNT(*) as count FROM users');
      if ((existingUsers[0] as any).count > 0) {
        logger.info('User data already exists, skipping user seeding');
        return;
      }

      logger.info('Seeding users...');

      // 创建管理员用户
      const saltRounds = 12;
      const adminPasswordHash = await bcrypt.hash('admin123456', saltRounds);

      const adminUser = {
        username: 'admin',
        email: '<EMAIL>',
        password_hash: adminPasswordHash,
        role: 'admin',
        is_active: true
      };

      await database.execute(
        `INSERT INTO users (username, email, password_hash, role, is_active)
         VALUES (?, ?, ?, ?, ?)`,
        [
          adminUser.username,
          adminUser.email,
          adminUser.password_hash,
          adminUser.role,
          adminUser.is_active
        ]
      );

      logger.info('Admin user created successfully:', {
        username: adminUser.username,
        email: adminUser.email
      });

    } catch (error) {
      logger.error('Failed to seed users:', error);
      throw error;
    }
  }

  /**
   * 插入系统设置数据
   */
  private async seedSystemSettings(): Promise<void> {
    try {
      // 检查是否已有系统设置数据
      const existingSettings = await database.query('SELECT COUNT(*) as count FROM system_settings');
      if ((existingSettings[0] as any).count > 0) {
        logger.info('System settings data already exists, skipping settings seeding');
        return;
      }

      logger.info('Seeding system settings...');

      // 翻译服务提供商配置
      const translationProviders = {
        globally_enabled: true, //全局翻译开关，打开可用翻译，关闭不可用翻译
        mtran: {
          name: 'MTran翻译服务',
          baseUrl: 'http://localhost:5000',
          timeout: 30000,
          enabled: true,
          description: 'MTran本地翻译服务'
        },
        deepseek_huoshan: {
          name: 'DeepSeek火山引擎',
          baseUrl: 'https://ark.cn-beijing.volces.com/api/v3/chat/completions',
          mult_models: [
            { models_name: 'hu-v3', 
              models_id: 'ep-20250221162908-465p7', 
              apikey: '825522c0-78c6-4474-a5f5-6f699ceea1d6',
              max_token: 5000000, //每日使用最大token
              today_used_token: 0, //今日已经使用token，这个不需要在前端输入，只需要显示，会有专门的方式去统计，每日凌晨会重置为0
              input_price: 2,
              output_price: 8,
             }
          ],
          one_model: [
            { models_name: 'chen-v3', 
            models_id: 'ep-20241227140526-33609', 
            apikey: 'sk-jipjycienusxsfdptoweqvagdillzrumjjtcblfjfsrdhqxk',
            max_token: 100000000, //每日使用最大token
            today_used_token: 0, //今日已经使用token，这个不需要在前端输入，只需要显示，会有专门的方式去统计，每日凌晨会重置为0
            input_price: 2,
            output_price: 8,
            }
          ],
          timeout: 60000,
          enabled: false,
          description: 'DeepSeek AI翻译服务（火山引擎版）',
        },
        with_openai : {
          name: '兼容OpenAI',
          description: '兼容OpenAI接口的翻译服务，可以自定义多服务商多模型',
          timeout: 60000,
          enabled: false,
          providers: [
            { models_name: 'lx-喵哥', 
              baseUrl: 'https://miaogeapi.deno.dev/v1', 
              models_id: 'DeepSeek-V3', 
              apikey: 'sk-7IIRvRFheoPUzEzSbbG1yAfCsqdQpm4sIaZK5nesbwcx8ZOM',
              max_token: 100000000, //每日使用最大token
              today_used_token: 0, //今日已经使用token，这个不需要在前端输入，只需要显示，会有专门的方式去统计，每日凌晨会重置为0
            },
            { models_name: 'LX-LobeAPI', 
              baseUrl: 'https://api.ds.lobeapi.com/v1', 
              models_id: 'deepseek-v3', 
              apikey: 'sk-FRAurTTGHnbVP2iNzoRXpxbbYs5qYZWIb6k7VApP5erfCMDo',
              max_token: 100000000, //每日使用最大token
              today_used_token: 0, //今日已经使用token，这个不需要在前端输入，只需要显示，会有专门的方式去统计，每日凌晨会重置为0
            },
          ],
        }
      };

      // 翻译场景配置 - 按平台维度配置
      const translationScenarios = {
        worten: {
          platform_name: 'Worten',
          enabled: true,
          scenarios: {
            form_editing: {
              name: '表单翻译接口',
              description: '在Worten产品编辑表单中使用的翻译服务',
              enabled: true,
              content_types: {
                title: {
                  provider: 'mtran',
                  sub_service: null
                },
                description: {
                  provider: 'mtran',
                  sub_service: null
                }
              }
            },
            form_batch: {
              name: '批量翻译接口',
              description: 'Worten批量产品翻译服务',
              enabled: true,
              content_types: {
                title: {
                  provider: 'mtran',
                  sub_service: null
                },
                description: {
                  provider: 'mtran',
                  sub_service: null
                }
              }
            },
            form_task: {
              name: '任务翻译接口',
              description: 'Worten后台翻译任务服务',
              enabled: true,
              content_types: {
                title: {
                  provider: 'mtran',
                  sub_service: null
                },
                description: {
                  provider: 'mtran',
                  sub_service: null
                }
              }
            }
          }
        },
        phh: {
          platform_name: 'PHH平台',
          enabled: true,
          scenarios: {
            form_editing: {
              name: '表单翻译接口',
              description: '在PHH产品编辑表单中使用的翻译服务',
              enabled: true,
              content_types: {
                title: {
                  provider: 'mtran',
                  sub_service: null
                },
                description: {
                  provider: 'mtran',
                  sub_service: null
                },
                selling_point: {
                  provider: 'mtran',
                  sub_service: null
                }
              }
            },
            form_batch: {
              name: '批量翻译接口',
              description: 'PHH批量产品翻译服务',
              enabled: true,
              content_types: {
                title: {
                  provider: 'mtran',
                  sub_service: null
                },
                description: {
                  provider: 'mtran',
                  sub_service: null
                },
                selling_point: {
                  provider: 'mtran',
                  sub_service: null
                }
              }
            },
            form_task: {
              name: '翻译任务接口',
              description: 'PHH后台翻译任务服务',
              enabled: true,
              content_types: {
                title: {
                  provider: 'mtran',
                  sub_service: null
                },
                description: {
                  provider: 'mtran',
                  sub_service: null
                },
                selling_point: {
                  provider: 'mtran',
                  sub_service: null
                }
              }
            }
          }
        }
      };

      // 系统基础配置
      const systemBasic = {
        company_name: 'IKUN ERP',
        system_title: '跨境电商ERP管理系统',
        proxy_host: '',
        proxy_port: '',
        proxy_username: '',
        proxy_password: ''
      };

      // Token统计配置
      const tokenStatistics = {
        deepseek_huoshan: {
          models: {
            'hu-v3': {
              yesterday_input_tokens: 0,
              yesterday_output_tokens: 0,
              today_input_tokens: 0,
              today_output_tokens: 0,
              total_input_tokens: 0,
              total_output_tokens: 0
            },
            'chen-v3': {
              yesterday_input_tokens: 0,
              yesterday_output_tokens: 0,
              today_input_tokens: 0,
              today_output_tokens: 0,
              total_input_tokens: 0,
              total_output_tokens: 0
            }
          }
        },
        openai_compatible: {
          models: {
            'lx-喵哥': {
              yesterday_input_tokens: 0,
              yesterday_output_tokens: 0,
              today_input_tokens: 0,
              today_output_tokens: 0,
              total_input_tokens: 0,
              total_output_tokens: 0
            },
            'LX-LobeAPI': {
              yesterday_input_tokens: 0,
              yesterday_output_tokens: 0,
              today_input_tokens: 0,
              today_output_tokens: 0,
              total_input_tokens: 0,
              total_output_tokens: 0
            }
          }
        },
        global_statistics: {
          total_input_tokens: 0,
          total_output_tokens: 0,
          total_api_calls: 0
        }
      };

      const settingsData = [
        {
          setting_key: 'translation_providers',
          setting_value: JSON.stringify(translationProviders),
          category: 'translation',
          display_name: '翻译服务提供商',
          description: '配置可用的翻译服务提供商及其参数',
          is_public: false,
          is_editable: true,
          sort_order: 1
        },
        {
          setting_key: 'translation_scenarios',
          setting_value: JSON.stringify(translationScenarios),
          category: 'translation',
          display_name: '翻译场景配置',
          description: '配置不同平台和使用场景下的翻译服务选择',
          is_public: false,
          is_editable: true,
          sort_order: 2
        },
        {
          setting_key: 'token_statistics',
          setting_value: JSON.stringify(tokenStatistics),
          category: 'translation',
          display_name: 'Token使用统计',
          description: '翻译服务Token使用量统计数据',
          is_public: false,
          is_editable: true,
          sort_order: 3
        },
        {
          setting_key: 'system_basic',
          setting_value: JSON.stringify(systemBasic),
          category: 'system',
          display_name: '系统基础配置',
          description: '系统基本信息和全局设置',
          is_public: true,
          is_editable: true,
          sort_order: 1
        }
      ];

      // 插入设置数据
      for (const setting of settingsData) {
        await database.executeUpdate(`
          INSERT INTO system_settings (
            setting_key, setting_value, category, display_name, description,
            is_public, is_editable, sort_order
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          setting.setting_key,
          setting.setting_value,
          setting.category,
          setting.display_name,
          setting.description,
          setting.is_public,
          setting.is_editable,
          setting.sort_order
        ]);
      }

      logger.info('System settings seeded successfully');
    } catch (error) {
      logger.error('Failed to seed system settings:', error);
      throw error;
    }
  }
}

export const seedDataManager = new SeedDataManager();
