/**
 * 系统设置路由
 * 系统配置管理相关的API路由
 */

import { Router } from 'express';
import { systemSettingController } from '@/controllers/system/systemSettingController';
import { validateRequest } from '@/middleware/validation/validateRequest';
import { systemSettingValidation } from '@/validations/system/systemSettingValidation';

const router = Router();

// 获取所有设置
router.get('/', systemSettingController.getAllSettings.bind(systemSettingController));

// 获取公开设置（无需认证）
router.get('/public', systemSettingController.getPublicSettings.bind(systemSettingController));

// 获取所有分类
router.get('/categories', systemSettingController.getCategories.bind(systemSettingController));

// 获取翻译配置
router.get('/translation/config', systemSettingController.getTranslationConfig.bind(systemSettingController));

// 更新翻译服务提供商配置
router.put('/translation/providers',
  validateRequest({ body: systemSettingValidation.translationProviders }),
  systemSettingController.updateTranslationConfig.bind(systemSettingController)
);

// 更新翻译场景配置
router.put('/translation/scenarios',
  validateRequest({ body: systemSettingValidation.translationScenarios }),
  systemSettingController.updateTranslationConfig.bind(systemSettingController)
);

// 获取系统基础配置
router.get('/basic', systemSettingController.getSystemBasicConfig.bind(systemSettingController));

// 更新系统基础配置
router.put('/basic',
  validateRequest({ body: systemSettingValidation.systemBasicConfig }),
  systemSettingController.updateSystemBasicConfig.bind(systemSettingController)
);

// 获取Token统计数据
router.get('/token-statistics', systemSettingController.getTokenStatistics.bind(systemSettingController));

// 更新Token统计数据
router.put('/token-statistics',
  validateRequest({ body: systemSettingValidation.tokenStatistics }),
  systemSettingController.updateTokenStatistics.bind(systemSettingController)
);

// 重置今日Token统计
router.post('/token-statistics/reset-daily', systemSettingController.resetDailyTokenStats.bind(systemSettingController));

// 增加Token使用量（用于连接测试等场景）
router.post('/token-usage/increment', systemSettingController.incrementTokenUsage.bind(systemSettingController));

// 根据分类获取设置
router.get('/category/:category',
  validateRequest({ params: systemSettingValidation.getSettingsByCategory }),
  systemSettingController.getSettingsByCategory.bind(systemSettingController)
);

// 批量更新设置
router.put('/batch',
  validateRequest({ body: systemSettingValidation.batchUpdateSettings }),
  systemSettingController.batchUpdateSettings.bind(systemSettingController)
);

// 获取单个设置
router.get('/:key',
  validateRequest({ params: systemSettingValidation.getSetting }),
  systemSettingController.getSetting.bind(systemSettingController)
);

// 更新单个设置
router.put('/:key',
  validateRequest({
    params: systemSettingValidation.getSetting,
    body: systemSettingValidation.updateSetting
  }),
  systemSettingController.updateSetting.bind(systemSettingController)
);

// 重置设置为默认值
router.post('/:key/reset', systemSettingController.resetToDefault.bind(systemSettingController));

export default router;
