/**
 * Product Controller
 * Handles product management operations
 */

import { Request, Response, NextFunction } from 'express';
import { productService } from '@/services/products/productService';
import { excelService } from '@/services/products/excelService';
import { logger } from '@/utils/logger';
import { getBeijingTimeISO } from '@/utils/time';
import {
  CreateProductRequest,
  UpdateProductRequest,
  SearchQuery
} from '@/types';
import multer from 'multer';
import path from 'path';

class ProductController {
  // GET /api/v1/products
  public async getProducts(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query = req.query as SearchQuery & {
        status?: 'draft' | 'active' | 'inactive';
        category_id?: number;
        source?: string;
        start_date?: string;
        end_date?: string;
      };

      const result = await productService.getProducts(query);

      res.status(200).json({
        code: 200,
        message: 'Products retrieved successfully',
        data: result,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // POST /api/v1/products
  public async createProduct(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const productData: CreateProductRequest = req.body;
      const product = await productService.createProduct(productData);

      logger.info('Product created:', { productId: product.id, sku: product.sku });

      res.status(201).json({
        code: 201,
        message: 'Product created successfully',
        data: product,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // POST /api/v1/products/import
  public async importProducts(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { products }: { products: CreateProductRequest[] } = req.body;
      const result = await productService.importProducts(products);

      logger.info('Products imported:', { count: result.length });

      res.status(201).json({
        code: 201,
        message: 'Products imported successfully',
        data: result,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // GET /api/v1/products/category/:categoryId
  public async getProductsByCategory(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const categoryId = parseInt(req.params.categoryId!);
      const query = req.query as SearchQuery;
      
      const result = await productService.getProductsByCategory(categoryId, query);

      res.status(200).json({
        code: 200,
        message: 'Products by category retrieved successfully',
        data: result,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // GET /api/v1/products/:id
  public async getProductById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const id = parseInt(req.params.id!);
      const product = await productService.getProductById(id);

      res.status(200).json({
        code: 200,
        message: 'Product retrieved successfully',
        data: product,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // PUT /api/v1/products/:id
  public async updateProduct(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const id = parseInt(req.params.id!);
      const updateData: UpdateProductRequest = req.body;
      const product = await productService.updateProduct(id, updateData);

      logger.info('Product updated:', { productId: id });

      res.status(200).json({
        code: 200,
        message: 'Product updated successfully',
        data: product,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // DELETE /api/v1/products/:id
  public async deleteProduct(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const id = parseInt(req.params.id!);
      const result = await productService.deleteProduct(id);

      logger.info('Product deletion result:', { productId: id, success: result.success });

      res.status(200).json({
        code: 200,
        message: result.message,
        success: result.success,
        error: result.error,
        claimedInfo: result.claimedInfo,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // GET /api/v1/products/template - Download import template
  public async downloadTemplate(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      logger.info('Template download requested');
      const templateBuffer = await excelService.generateTemplate();

      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', 'attachment; filename="dropship-products-template.xlsx"');
      res.send(templateBuffer);

      logger.info('Template downloaded successfully');
    } catch (error) {
      logger.error('Template download failed:', error);
      next(error);
    }
  }

  // POST /api/v1/products/import - Import products from Excel
  public async importFromExcel(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      // 配置 multer 用于文件上传
      const upload = multer({
        storage: multer.memoryStorage(),
        fileFilter: (req, file, cb) => {
          const allowedTypes = ['.xlsx', '.xls'];
          const fileExt = path.extname(file.originalname).toLowerCase();
          if (allowedTypes.includes(fileExt)) {
            cb(null, true);
          } else {
            cb(new Error('只支持 .xlsx 和 .xls 格式的文件'));
          }
        },
        limits: {
          fileSize: 10 * 1024 * 1024 // 10MB
        }
      }).array('files', 10); // 最多10个文件

      upload(req, res, async (err) => {
        if (err) {
          return next(err);
        }

        const files = req.files as Express.Multer.File[];
        if (!files || files.length === 0) {
          return res.status(400).json({
            code: 400,
            message: '请选择要导入的文件',
            timestamp: getBeijingTimeISO()
          });
        }

        // 检查是否只是验证模式
        const validateOnly = req.body.validate_only === 'true';

        const results = await excelService.importFromExcel(files, validateOnly);

        logger.info(`Excel ${validateOnly ? 'validation' : 'import'} completed:`, {
          fileCount: files.length,
          validateOnly,
          results: results.map(r => ({
            filename: r.filename,
            success: r.success,
            total: r.total,
            imported: r.imported,
            errors: r.errors?.length || 0
          }))
        });

        res.status(200).json({
          code: 200,
          message: validateOnly ? 'Excel validation completed' : 'Excel import completed',
          data: results,
          timestamp: getBeijingTimeISO()
        });
      });
    } catch (error) {
      next(error);
    }
  }

  // GET /api/v1/products/export - Export products to Excel
  public async exportToExcel(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query = req.query as SearchQuery & {
        status?: 'draft' | 'active' | 'inactive';
        category_id?: number;
        source?: string;
        start_date?: string;
        end_date?: string;
        export_all?: string;
        selected_ids?: string | string[];
      };

      // 处理选中的产品ID
      let selectedIds: number[] = [];
      if (query.selected_ids) {
        if (Array.isArray(query.selected_ids)) {
          selectedIds = query.selected_ids.map(id => parseInt(id, 10)).filter(id => !isNaN(id));
        } else {
          selectedIds = query.selected_ids.split(',').map(id => parseInt(id.trim(), 10)).filter(id => !isNaN(id));
        }
      }

      const exportAll = query.export_all === 'true';

      logger.info('Export request:', {
        exportAll,
        selectedIdsCount: selectedIds.length,
        filters: { ...query, selected_ids: undefined, export_all: undefined }
      });

      const excelBuffer = await excelService.exportToExcel({
        ...query,
        exportAll,
        selectedIds
      });

      const exportType = exportAll ? '全部' : `选中${selectedIds.length}个`;
      const filename = `铺货产品数据_${exportType}_${new Date().toISOString().split('T')[0]}.xlsx`;

      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
      res.send(excelBuffer);

      logger.info('Products exported to Excel:', {
        exportType,
        filename,
        bufferSize: excelBuffer.length
      });
    } catch (error) {
      next(error);
    }
  }

  // POST /api/v1/products/batch-claim
  public async batchClaimProducts(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { product_ids, store_ids }: { product_ids: number[], store_ids: number[] } = req.body;

      logger.info('Batch claim products:', {
        productCount: product_ids.length,
        storeCount: store_ids.length
      });

      const result = await productService.batchClaimProducts(product_ids, store_ids);

      res.status(200).json({
        code: 200,
        message: 'Products claimed successfully',
        data: {
          success_count: result.success_count,
          failed_count: result.failed_count,
          details: result.details
        },
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }
}

export const productController = new ProductController();
