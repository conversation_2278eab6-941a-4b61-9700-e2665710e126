'use client'

import { useEffect, useState } from 'react'

interface RequestLog {
  id: string
  url: string
  method: string
  timestamp: string
  status?: number
}

export function RequestLogger() {
  const [requests, setRequests] = useState<RequestLog[]>([])

  useEffect(() => {
    // 拦截fetch请求
    const originalFetch = window.fetch
    
    window.fetch = async (...args) => {
      const [url, options] = args
      const method = options?.method || 'GET'
      const requestId = Math.random().toString(36).substr(2, 9)
      
      // 记录请求开始
      const startLog: RequestLog = {
        id: requestId,
        url: url.toString(),
        method,
        timestamp: new Date().toLocaleTimeString('zh-CN')
      }
      
      setRequests(prev => [startLog, ...prev.slice(0, 19)]) // 只保留最近20条
      
      try {
        const response = await originalFetch(...args)
        
        // 更新请求状态
        setRequests(prev => prev.map(req => 
          req.id === requestId 
            ? { ...req, status: response.status }
            : req
        ))
        
        return response
      } catch (error) {
        // 更新错误状态
        setRequests(prev => prev.map(req => 
          req.id === requestId 
            ? { ...req, status: 0 }
            : req
        ))
        throw error
      }
    }

    return () => {
      window.fetch = originalFetch
    }
  }, [])

  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 w-96 max-h-96 bg-white border border-gray-300 rounded-lg shadow-lg overflow-hidden z-50">
      <div className="bg-gray-100 px-3 py-2 border-b">
        <h3 className="text-sm font-medium">API请求日志</h3>
        <button 
          onClick={() => setRequests([])}
          className="text-xs text-blue-600 hover:text-blue-800"
        >
          清空
        </button>
      </div>
      <div className="overflow-y-auto max-h-80">
        {requests.length === 0 ? (
          <div className="p-3 text-sm text-gray-500">暂无请求</div>
        ) : (
          requests.map((req) => (
            <div key={req.id} className="px-3 py-2 border-b border-gray-100 text-xs">
              <div className="flex items-center justify-between">
                <span className={`font-medium ${
                  req.status === undefined ? 'text-yellow-600' :
                  req.status >= 200 && req.status < 300 ? 'text-green-600' :
                  req.status >= 400 ? 'text-red-600' : 'text-gray-600'
                }`}>
                  {req.method}
                </span>
                <span className="text-gray-500">{req.timestamp}</span>
              </div>
              <div className="text-gray-700 truncate" title={req.url}>
                {req.url}
              </div>
              {req.status !== undefined && (
                <div className={`text-xs ${
                  req.status >= 200 && req.status < 300 ? 'text-green-600' :
                  req.status >= 400 ? 'text-red-600' : 'text-gray-600'
                }`}>
                  状态: {req.status}
                </div>
              )}
            </div>
          ))
        )}
      </div>
    </div>
  )
}
