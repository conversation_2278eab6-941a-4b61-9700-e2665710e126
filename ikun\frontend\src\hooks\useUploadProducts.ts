import { useState, useCallback } from 'react'
import { UploadProductListing, CreateUploadProductData, UpdateUploadProductData } from '@/types'

interface UseUploadProductsReturn {
  uploadProducts: UploadProductListing[]
  loading: boolean
  error: string | null
  pagination: {
    page: number
    limit: number
    total: number
  }
  fetchUploadProducts: (params?: any) => Promise<void>
  createUploadProduct: (data: CreateUploadProductData) => Promise<UploadProductListing>
  updateUploadProduct: (id: number, data: UpdateUploadProductData) => Promise<UploadProductListing>
  deleteUploadProduct: (id: number) => Promise<void>
  uploadToPlattform: (id: number) => Promise<void>
  batchUpload: (ids: number[]) => Promise<void>
  translateProduct: (id: number) => Promise<void>
  batchTranslate: (ids: number[]) => Promise<void>
}

export function useUploadProducts(platform: string): UseUploadProductsReturn {
  const [uploadProducts, setUploadProducts] = useState<UploadProductListing[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0
  })

  const fetchUploadProducts = useCallback(async (params: any = {}) => {
    setLoading(true)
    setError(null)

    try {
      const token = localStorage.getItem('auth_token')
      if (!token) {
        throw new Error('请先登录')
      }

      // 获取当前分页状态
      const currentPagination = pagination;

      const queryParams = new URLSearchParams({
        platform: platform,
        page: params.page?.toString() || currentPagination.page.toString(),
        limit: params.limit?.toString() || currentPagination.limit.toString(),
        ...params
      })

      // 添加时间戳防止缓存
      if (params._t) {
        queryParams.set('_t', params._t.toString())
      }

      const response = await fetch(`/api/v1/uploadproduct/listings?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache'
        }
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      if (data.success) {
        console.log('fetchUploadProducts 收到数据:', {
          itemsCount: data.data.items?.length || 0,
          firstItem: data.data.items?.[0] ? {
            id: data.data.items[0].id,
            sku: data.data.items[0].sku,
            multi_titles: data.data.items[0].multi_titles,
            multi_descriptions: data.data.items[0].multi_descriptions
          } : null,
          timestamp: new Date().toISOString()
        });

        setUploadProducts(data.data.items || [])
        setPagination({
          page: data.data.pagination?.page || 1,
          limit: data.data.pagination?.limit || 20,
          total: data.data.pagination?.total || 0
        })
      } else {
        throw new Error(data.message || '获取产品列表失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取产品列表失败'
      setError(errorMessage)
      console.error('获取产品列表失败:', err)
    } finally {
      setLoading(false)
    }
  }, [platform])

  const createUploadProduct = useCallback(async (data: CreateUploadProductData): Promise<UploadProductListing> => {
    setLoading(true)
    setError(null)

    try {
      const token = localStorage.getItem('auth_token')
      if (!token) {
        throw new Error('请先登录')
      }

      const response = await fetch('/api/v1/uploadproduct/listings', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...data,
          platform_code: platform
        }),
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const result = await response.json()
      
      if (result.success) {
        // 刷新列表
        await fetchUploadProducts()
        return result.data
      } else {
        throw new Error(result.message || '创建产品失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '创建产品失败'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [platform, fetchUploadProducts])

  const updateUploadProduct = useCallback(async (id: number, data: UpdateUploadProductData): Promise<UploadProductListing> => {
    setLoading(true)
    setError(null)

    try {
      const token = localStorage.getItem('auth_token')
      if (!token) {
        throw new Error('请先登录')
      }

      const response = await fetch(`/api/v1/uploadproduct/listings/${id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const result = await response.json()
      
      if (result.success) {
        // 更新本地状态
        setUploadProducts(prev => 
          prev.map(product => 
            product.id === id ? { ...product, ...result.data } : product
          )
        )
        return result.data
      } else {
        throw new Error(result.message || '更新产品失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '更新产品失败'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const deleteUploadProduct = useCallback(async (id: number): Promise<void> => {
    setLoading(true)
    setError(null)

    try {
      const token = localStorage.getItem('auth_token')
      if (!token) {
        throw new Error('请先登录')
      }

      const response = await fetch(`/api/v1/uploadproduct/listings/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const result = await response.json()
      
      if (result.success) {
        // 从本地状态中移除
        setUploadProducts(prev => prev.filter(product => product.id !== id))
        // 更新分页信息
        setPagination(prev => ({
          ...prev,
          total: Math.max(0, prev.total - 1)
        }))
      } else {
        throw new Error(result.message || '删除产品失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '删除产品失败'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const uploadToPlattform = useCallback(async (id: number): Promise<void> => {
    setLoading(true)
    setError(null)

    try {
      const token = localStorage.getItem('auth_token')
      if (!token) {
        throw new Error('请先登录')
      }

      const response = await fetch(`/api/v1/uploadproduct/listings/${id}/upload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const result = await response.json()
      
      if (result.success) {
        // 更新产品状态
        setUploadProducts(prev => 
          prev.map(product => 
            product.id === id ? { ...product, status: 'pending' } : product
          )
        )
      } else {
        throw new Error(result.message || '上架失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '上架失败'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const batchUpload = useCallback(async (ids: number[]): Promise<void> => {
    setLoading(true)
    setError(null)

    try {
      const token = localStorage.getItem('auth_token')
      if (!token) {
        throw new Error('请先登录')
      }

      const response = await fetch('/api/v1/uploadproduct/listings/batch-upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ids }),
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const result = await response.json()
      
      if (result.success) {
        // 刷新列表
        await fetchUploadProducts()
      } else {
        throw new Error(result.message || '批量上架失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '批量上架失败'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [fetchUploadProducts])

  const translateProduct = useCallback(async (id: number): Promise<void> => {
    setLoading(true)
    setError(null)

    try {
      const token = localStorage.getItem('auth_token')
      if (!token) {
        throw new Error('请先登录')
      }

      const response = await fetch(`/api/v1/uploadproduct/listings/${id}/translate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const result = await response.json()
      
      if (result.success) {
        // 更新产品翻译状态
        setUploadProducts(prev => 
          prev.map(product => 
            product.id === id ? { ...product, listings_translation_status: 'completed' } : product
          )
        )
      } else {
        throw new Error(result.message || '翻译失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '翻译失败'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const batchTranslate = useCallback(async (ids: number[]): Promise<void> => {
    setLoading(true)
    setError(null)

    try {
      const token = localStorage.getItem('auth_token')
      if (!token) {
        throw new Error('请先登录')
      }

      const response = await fetch('/api/v1/uploadproduct/listings/batch-translate', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ ids }),
      })
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }
      
      const result = await response.json()
      
      if (result.success) {
        // 刷新列表
        await fetchUploadProducts()
      } else {
        throw new Error(result.message || '批量翻译失败')
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '批量翻译失败'
      setError(errorMessage)
      throw err
    } finally {
      setLoading(false)
    }
  }, [fetchUploadProducts])

  return {
    uploadProducts,
    loading,
    error,
    pagination,
    fetchUploadProducts,
    createUploadProduct,
    updateUploadProduct,
    deleteUploadProduct,
    uploadToPlattform,
    batchUpload,
    translateProduct,
    batchTranslate
  }
}
