/**
 * 数据库配置文件
 * IKUN ERP - MySQL数据库连接配置
 */

// 数据库连接配置
export const dbConfig = {
  // MySQL配置 (本地开发)
  mysql: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '123456',
    database: process.env.DB_NAME || 'ikun',
    charset: 'utf8mb4',
    timezone: '+08:00', // 中国时区
    connectionLimit: 10,
    acquireTimeout: 60000,
    timeout: 60000,
  },

  // Redis配置 (云端部署时启用)
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT || '6379'),
    password: process.env.REDIS_PASSWORD || '',
    db: 0,
    retryDelayOnFailover: 100,
    enableReadyCheck: false,
    maxRetriesPerRequest: null,
  }
}

// 数据库连接URL
export const DATABASE_URL = process.env.DATABASE_URL || 
  `mysql://${dbConfig.mysql.user}:${dbConfig.mysql.password}@${dbConfig.mysql.host}:${dbConfig.mysql.port}/${dbConfig.mysql.database}`

// 数据库表名常量
export const TABLE_NAMES = {
  PRODUCT_CATEGORIES: 'product_categories',
  DROPSHIP_PRODUCTS: 'product_dropship',
  ORDERS: 'orders_updata',
  SCRAPING_TASKS: 'scraping_tasks',
  USERS: 'users',
  PLATFORMS: 'stores_platforms',
  STORES: 'stores_updata',
} as const

// 产品目录状态枚举
export const CATEGORY_STATUS = {
  ENABLED: 'enabled',
  DISABLED: 'disabled',
} as const

// 产品目录层级枚举
export const CATEGORY_LEVEL = {
  LEVEL_1: 1,
  LEVEL_2: 2,
  LEVEL_3: 3,
} as const

// 自动SKU状态枚举
export const AUTO_SKU_STATUS = {
  ENABLED: 'enabled',
  DISABLED: 'disabled',
} as const

// 产品状态枚举
export const PRODUCT_STATUS = {
  DRAFT: 'draft',
  ACTIVE: 'active',
  INACTIVE: 'inactive',
} as const

// 产品来源枚举
export const PRODUCT_SOURCE = {
  MANUAL: '手动添加',
  EXCEL: 'Excel导入',
  AMAZON: '采集自Amazon',
  EBAY: '采集自eBay',
  SHOPIFY: '采集自Shopify',
  ALIEXPRESS: '采集自AliExpress',
} as const

// 订单状态枚举
export const ORDER_STATUS = {
  PENDING: 'pending',
  PAID: 'paid',
  SHIPPED: 'shipped',
  DELIVERED: 'delivered',
  CANCELLED: 'cancelled',
} as const

// 采集任务状态枚举
export const SCRAPING_STATUS = {
  PENDING: 'pending',
  RUNNING: 'running',
  COMPLETED: 'completed',
  FAILED: 'failed',
} as const

// 数据库初始化SQL (用于后端)
export const INIT_SQL = {
  // 创建产品目录表
  CREATE_PRODUCT_CATEGORIES: `
    CREATE TABLE IF NOT EXISTS product_categories (
      id BIGINT AUTO_INCREMENT PRIMARY KEY,
      category_code VARCHAR(50) UNIQUE NOT NULL,
      chinese_name VARCHAR(200) NOT NULL,
      english_name VARCHAR(200) NOT NULL,
      status ENUM('enabled', 'disabled') DEFAULT 'enabled',
      auto_sku ENUM('enabled', 'disabled') DEFAULT 'disabled',
      category_level TINYINT NOT NULL,
      parent_id BIGINT NULL,
      category_description TEXT,
      attribute_tags JSON,
      sort_order INT DEFAULT 0,
      category_path VARCHAR(500),
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

      INDEX idx_category_code (category_code),
      INDEX idx_status (status),
      INDEX idx_category_level (category_level),
      INDEX idx_parent_id (parent_id),
      INDEX idx_category_path (category_path),
      INDEX idx_sort_order (sort_order),

      FOREIGN KEY (parent_id) REFERENCES product_categories(id) ON DELETE SET NULL,

      CONSTRAINT chk_category_level CHECK (category_level IN (1, 2, 3)),
      CONSTRAINT chk_parent_level CHECK (
        (category_level = 1 AND parent_id IS NULL) OR
        (category_level > 1 AND parent_id IS NOT NULL)
      )
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  `,

  // 创建铺货产品表
  CREATE_DROPSHIP_PRODUCTS: `
    CREATE TABLE IF NOT EXISTS product_dropship (
      id BIGINT AUTO_INCREMENT PRIMARY KEY,
      source VARCHAR(50) NOT NULL,
      sku VARCHAR(100) UNIQUE NOT NULL,
      ean VARCHAR(20) UNIQUE NOT NULL,
      category_id BIGINT NOT NULL,
      category VARCHAR(200) NOT NULL,
      english_title VARCHAR(500) NOT NULL,
      english_description TEXT NOT NULL,
      image1 VARCHAR(500) NOT NULL,
      image2 VARCHAR(500),
      image3 VARCHAR(500),
      image4 VARCHAR(500),
      image5 VARCHAR(500),
      cost_price DECIMAL(10,2),
      package_weight INT,
      package_length DECIMAL(8,2),
      package_width DECIMAL(8,2),
      package_height DECIMAL(8,2),
      purchase_link TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      claim_time TIMESTAMP NULL,
      claim_platform VARCHAR(100),
      listing_count INT DEFAULT 0,
      remarks TEXT,
      status VARCHAR(20) DEFAULT 'draft',
      
      INDEX idx_sku (sku),
      INDEX idx_ean (ean),
      INDEX idx_status (status),
      INDEX idx_category_id (category_id),
      INDEX idx_category (category),
      INDEX idx_source (source),
      INDEX idx_claim_platform (claim_platform),
      INDEX idx_created_at (created_at),

      FOREIGN KEY (category_id) REFERENCES product_categories(id) ON DELETE RESTRICT,
      FULLTEXT INDEX idx_search (english_title, english_description, sku, ean, category),
      
      CONSTRAINT chk_status CHECK (status IN ('draft', 'active', 'inactive')),
      CONSTRAINT chk_cost_price CHECK (cost_price >= 0),
      CONSTRAINT chk_package_weight CHECK (package_weight >= 0),
      CONSTRAINT chk_listing_count CHECK (listing_count >= 0)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  `,

  // 设置自增起始值
  SET_AUTO_INCREMENT: `
    ALTER TABLE product_dropship AUTO_INCREMENT = 10000001;
  `,

  // 创建订单表
  CREATE_ORDERS: `
    CREATE TABLE IF NOT EXISTS orders_updata (
      id VARCHAR(36) PRIMARY KEY,
      platform_order_id VARCHAR(100) NOT NULL,
      platform VARCHAR(50) NOT NULL,
      customer_id VARCHAR(100),
      total_amount DECIMAL(10,2),
      status VARCHAR(20) DEFAULT 'pending',
      shipping_address JSON,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      
      INDEX idx_platform_order_id (platform_order_id),
      INDEX idx_platform (platform),
      INDEX idx_status (status),
      INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  `,

  // 创建采集任务表
  CREATE_SCRAPING_TASKS: `
    CREATE TABLE IF NOT EXISTS scraping_tasks (
      id VARCHAR(36) PRIMARY KEY,
      platform VARCHAR(50) NOT NULL,
      url TEXT NOT NULL,
      status VARCHAR(20) DEFAULT 'pending',
      result JSON,
      error_message TEXT,
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      completed_at TIMESTAMP NULL,
      
      INDEX idx_platform (platform),
      INDEX idx_status (status),
      INDEX idx_created_at (created_at)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
  `
}

// 导出类型定义
export type CategoryStatus = typeof CATEGORY_STATUS[keyof typeof CATEGORY_STATUS]
export type CategoryLevel = typeof CATEGORY_LEVEL[keyof typeof CATEGORY_LEVEL]
export type AutoSkuStatus = typeof AUTO_SKU_STATUS[keyof typeof AUTO_SKU_STATUS]
export type ProductStatus = typeof PRODUCT_STATUS[keyof typeof PRODUCT_STATUS]
export type ProductSource = typeof PRODUCT_SOURCE[keyof typeof PRODUCT_SOURCE]
export type OrderStatus = typeof ORDER_STATUS[keyof typeof ORDER_STATUS]
export type ScrapingStatus = typeof SCRAPING_STATUS[keyof typeof SCRAPING_STATUS]

// 产品目录接口定义
export interface ProductCategory {
  id: number
  category_code: string
  chinese_name: string
  english_name: string
  status: CategoryStatus
  auto_sku: AutoSkuStatus
  category_level: CategoryLevel
  parent_id: number | null
  category_description?: string
  attribute_tags?: string[]
  sort_order: number
  category_path: string
  created_at: string
  updated_at: string
  children?: ProductCategory[]
}

// 产品目录树节点接口
export interface CategoryTreeNode {
  id: number
  label: string
  value: string
  level: number
  children?: CategoryTreeNode[]
  category: ProductCategory
}
