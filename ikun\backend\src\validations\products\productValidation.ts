/**
 * Product Validation Schemas
 * Joi validation rules for product-related requests
 */

import <PERSON><PERSON> from 'joi';

export const productValidation = {
  // GET /api/v1/products
  getProducts: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    search: Joi.string().trim().max(255).optional(),
    status: Joi.string().valid('draft', 'active', 'inactive').optional(),
    category_id: Joi.number().integer().positive().optional(),
    source: Joi.string().trim().max(100).optional(),
    start_date: Joi.date().iso().optional(),
    end_date: Joi.date().iso().min(Joi.ref('start_date')).optional(),
    sort: Joi.string().valid('asc', 'desc').default('desc'),
    sortBy: Joi.string().valid('created_at', 'updated_at', 'sku', 'english_title', 'cost_price').default('created_at')
  }),

  // POST /api/v1/products
  createProduct: Joi.object({
    source: Joi.string().trim().min(1).max(50).required(),
    sku: Joi.string().trim().min(1).max(100).required()
      .pattern(/^[A-Z0-9-_]+$/)
      .messages({
        'string.pattern.base': 'SKU must contain only uppercase letters, numbers, hyphens, and underscores'
      }),
    ean: Joi.string().trim().min(8).max(20).required()
      .pattern(/^[0-9]+$/)
      .messages({
        'string.pattern.base': 'EAN must contain only numbers'
      }),
    category_id: Joi.number().integer().positive().optional(),
    category: Joi.string().trim().min(1).max(200).optional(),
    english_title: Joi.string().trim().min(1).max(500).required(),
    selling_point: Joi.array().items(Joi.string().trim().max(100000)).max(5).optional(),
    english_description: Joi.string().trim().min(1).required(),
    image1: Joi.string().trim().min(1).max(500).required(),
    image2: Joi.string().trim().max(500).optional().allow(''),
    image3: Joi.string().trim().max(500).optional().allow(''),
    image4: Joi.string().trim().max(500).optional().allow(''),
    image5: Joi.string().trim().max(500).optional().allow(''),
    cost_price: Joi.number().precision(2).min(0).optional(),
    package_weight: Joi.number().integer().min(0).optional(),
    package_length: Joi.number().precision(2).min(0).optional(),
    package_width: Joi.number().precision(2).min(0).optional(),
    package_height: Joi.number().precision(2).min(0).optional(),
    purchase_link: Joi.string().trim().uri().optional().allow(''),
    remarks: Joi.string().trim().max(1000).optional().allow(''),
    status: Joi.string().valid('draft', 'active', 'inactive').default('draft')
  }),

  // PUT /api/v1/products/:id
  updateProduct: Joi.object({
    ean: Joi.string().trim().min(8).max(20).optional()
      .pattern(/^[0-9]+$/)
      .messages({
        'string.pattern.base': 'EAN must contain only numbers'
      }),
    category: Joi.string().trim().min(1).max(200).optional(),
    english_title: Joi.string().trim().min(1).max(500).optional(),
    selling_point: Joi.array().items(Joi.string().trim().max(100000)).max(5).optional(),
    english_description: Joi.string().trim().min(1).optional(),
    image1: Joi.string().trim().min(1).max(500).optional(),
    image2: Joi.string().trim().max(500).optional().allow(''),
    image3: Joi.string().trim().max(500).optional().allow(''),
    image4: Joi.string().trim().max(500).optional().allow(''),
    image5: Joi.string().trim().max(500).optional().allow(''),
    cost_price: Joi.number().precision(2).min(0).optional(),
    package_weight: Joi.number().integer().min(0).optional(),
    package_length: Joi.number().precision(2).min(0).optional(),
    package_width: Joi.number().precision(2).min(0).optional(),
    package_height: Joi.number().precision(2).min(0).optional(),
    purchase_link: Joi.string().trim().uri().optional().allow(''),
    remarks: Joi.string().trim().max(1000).optional().allow(''),
    status: Joi.string().valid('draft', 'active', 'inactive').optional()
  }),

  // POST /api/v1/products/import
  importProducts: Joi.object({
    products: Joi.array().items(
      Joi.object({
        source: Joi.string().trim().min(1).max(50).required(),
        sku: Joi.string().trim().min(1).max(100).required()
          .pattern(/^[A-Z0-9-_]+$/),
        ean: Joi.string().trim().min(8).max(20).required()
          .pattern(/^[0-9]+$/),
        category_id: Joi.number().integer().positive().required(),
        english_title: Joi.string().trim().min(1).max(500).required(),
        selling_point: Joi.array().items(Joi.string().trim().max(100000)).max(5).optional(),
        english_description: Joi.string().trim().min(1).required(),
        image1: Joi.string().trim().min(1).max(500).required(),
        image2: Joi.string().trim().max(500).optional().allow(''),
        image3: Joi.string().trim().max(500).optional().allow(''),
        image4: Joi.string().trim().max(500).optional().allow(''),
        image5: Joi.string().trim().max(500).optional().allow(''),
        cost_price: Joi.number().precision(2).min(0).optional(),
        package_weight: Joi.number().integer().min(0).optional(),
        package_length: Joi.number().precision(2).min(0).optional(),
        package_width: Joi.number().precision(2).min(0).optional(),
        package_height: Joi.number().precision(2).min(0).optional(),
        purchase_link: Joi.string().trim().uri().optional().allow(''),
        remarks: Joi.string().trim().max(1000).optional().allow(''),
        status: Joi.string().valid('draft', 'active', 'inactive').default('draft')
      })
    ).min(1).max(100).required()
  }),

  // GET /api/v1/products/export
  exportProducts: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    search: Joi.string().trim().max(255).optional(),
    status: Joi.string().valid('draft', 'active', 'inactive').optional(),
    category_id: Joi.number().integer().positive().optional(),
    source: Joi.string().trim().max(100).optional(),
    start_date: Joi.date().iso().optional(),
    end_date: Joi.date().iso().min(Joi.ref('start_date')).optional(),
    sort: Joi.string().valid('asc', 'desc').default('desc'),
    sortBy: Joi.string().valid('created_at', 'updated_at', 'sku', 'english_title', 'cost_price').default('created_at'),
    // 导出专用参数
    export_all: Joi.string().valid('true', 'false').optional(),
    selected_ids: Joi.alternatives().try(
      Joi.string().pattern(/^\d+(,\d+)*$/), // 逗号分隔的数字字符串
      Joi.array().items(Joi.string().pattern(/^\d+$/)) // 字符串数组
    ).optional()
  }),

  // Common parameter validation
  productId: Joi.object({
    id: Joi.number().integer().positive().required()
  }),

  categoryId: Joi.object({
    categoryId: Joi.number().integer().positive().required()
  }),

  // POST /api/v1/products/batch-claim
  batchClaim: Joi.object({
    product_ids: Joi.array()
      .items(Joi.number().integer().positive())
      .min(1)
      .max(100)
      .required()
      .messages({
        'array.min': 'At least one product must be selected',
        'array.max': 'Cannot claim more than 100 products at once'
      }),
    store_ids: Joi.array()
      .items(Joi.number().integer().positive())
      .min(1)
      .max(20)
      .required()
      .messages({
        'array.min': 'At least one store must be selected',
        'array.max': 'Cannot claim to more than 20 stores at once'
      })
  })
};
