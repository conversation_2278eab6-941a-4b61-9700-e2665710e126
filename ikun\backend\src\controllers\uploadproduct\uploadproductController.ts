/**
 * Upload Product Controller
 * Handles product listing management operations
 */

import { Request, Response, NextFunction } from 'express';
import { uploadproductService } from '@/services/uploadproduct/uploadproductService';
import { logger } from '@/utils/logger';
import { getBeijingTimeISO } from '@/utils/time';

class UploadProductController {
  // GET /api/v1/uploadproduct/listings
  public async getListings(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query = req.query as {
        platform?: string;
        page?: string;
        limit?: string;
        search?: string;
        status?: string;
        translation_status?: string;
        store_id?: string;
      };

      const result = await uploadproductService.getListings(query);

      // 设置禁用缓存的响应头
      res.set({
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      });

      res.status(200).json({
        success: true,
        message: 'Product listings retrieved successfully',
        data: result,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // POST /api/v1/uploadproduct/listings
  public async createListing(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const listingData = req.body;

      logger.info('Create upload product listing:', { listingData });

      const listing = await uploadproductService.createListing(listingData);

      res.status(201).json({
        success: true,
        message: 'Product listing created successfully',
        data: listing,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // GET /api/v1/uploadproduct/listings/:id
  public async getListingById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      logger.info('Get upload product listing details:', { id });

      const listing = await uploadproductService.getListingById(parseInt(id));

      res.status(200).json({
        success: true,
        message: 'Product listing details retrieved successfully',
        data: listing,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // PUT /api/v1/uploadproduct/listings/:id
  public async updateListing(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;
      const updateData = req.body;

      logger.info('Update upload product listing:', { id, updateData });

      const listing = await uploadproductService.updateListing(parseInt(id), updateData);

      res.status(200).json({
        success: true,
        message: 'Product listing updated successfully',
        data: listing,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // DELETE /api/v1/uploadproduct/listings/:id
  public async deleteListing(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { id } = req.params;

      logger.info('Delete upload product listing:', { id });

      await uploadproductService.deleteListing(parseInt(id));

      res.status(200).json({
        success: true,
        message: 'Product listing deleted successfully',
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // GET /api/v1/uploadproduct/categories
  public async getCategories(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query = req.query as {
        platform_code?: string;
        parent_category_id?: string;
        is_leaf?: string;
      };

      logger.info('Get upload product categories:', query);

      const categories = await uploadproductService.getCategories(query);

      res.status(200).json({
        success: true,
        message: 'Platform categories retrieved successfully',
        data: categories,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // GET /api/v1/uploadproduct/attributes
  public async getAttributes(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query = req.query as {
        platform_code?: string;
        category_id?: string;
        translation_status?: string;
      };

      logger.info('Get upload product category attributes:', query);

      const attributes = await uploadproductService.getAttributes(query);

      res.status(200).json({
        success: true,
        message: 'Category attributes retrieved successfully',
        data: attributes,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }
}

export const uploadproductController = new UploadProductController();
