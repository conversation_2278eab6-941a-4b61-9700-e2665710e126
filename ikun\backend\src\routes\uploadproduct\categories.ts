/**
 * Upload Product Categories Routes
 * Platform category management endpoints
 */

import { Router } from 'express';
import { uploadproductController } from '@/controllers/uploadproduct/uploadproductController';

const router = Router();

// GET /api/v1/uploadproduct/categories - Get platform categories
router.get('/', uploadproductController.getCategories.bind(uploadproductController));

export default router;
