/**
 * Store Base Model
 * 店铺基础数据模型 - 所有平台店铺的基础类
 */

import { BaseModel, QueryOptions } from '@/models/base/BaseModel';
import { database } from '@/database/connection';
import { logger } from '@/utils/logger';
import { ConflictError, BadRequestError } from '@/middleware/error/errorHandler';

export interface BaseStore {
  id: number;
  store_name: string;
  platform: string;
  status: 'active' | 'inactive' | 'suspended';
  description?: string;
  created_at: Date;
  updated_at: Date;
}

export abstract class StoreModel<T extends BaseStore = BaseStore> extends BaseModel<T> {
  protected abstract platform: string;
  protected timestamps = true;
  
  protected fillable = [
    'store_name',
    'platform',
    'status',
    'description'
  ];

  protected hidden: string[] = [];

  /**
   * 根据店铺名称查找
   */
  public async findByName(storeName: string): Promise<T | null> {
    return await this.findOne({ store_name: storeName });
  }

  /**
   * 检查店铺名称是否存在
   */
  public async nameExists(storeName: string, excludeId?: number): Promise<boolean> {
    if (excludeId) {
      const sql = `SELECT COUNT(*) as count FROM ${this.tableName} WHERE store_name = ? AND platform = ? AND id != ?`;
      const result = await database.queryOne<{ count: number }>(sql, [storeName, this.platform, excludeId]);
      return (result?.count || 0) > 0;
    }
    
    const result = await this.findOne({ store_name: storeName, platform: this.platform });
    return result !== null;
  }

  /**
   * 根据状态获取店铺
   */
  public async findByStatus(status: 'active' | 'inactive' | 'suspended', options: QueryOptions = {}): Promise<T[]> {
    const whereCondition = { status, platform: this.platform, ...options.where };
    return await this.findMany({ ...options, where: whereCondition });
  }

  /**
   * 获取活跃店铺
   */
  public async findActiveStores(options: QueryOptions = {}): Promise<T[]> {
    return await this.findByStatus('active', options);
  }

  /**
   * 更新店铺状态
   */
  public async updateStatus(id: number | string, status: 'active' | 'inactive' | 'suspended'): Promise<T> {
    const store = await this.update(id, { status } as Partial<T>);
    
    logger.info(`${this.platform} store status updated:`, { storeId: id, status });
    return store;
  }

  /**
   * 测试店铺连接
   */
  public abstract testConnection(id: number | string): Promise<{
    success: boolean;
    message: string;
    details?: any;
  }>;

  /**
   * 同步店铺数据
   */
  public abstract syncStoreData(id: number | string): Promise<{
    success: boolean;
    message: string;
    syncedData?: any;
  }>;

  /**
   * 获取店铺统计信息
   */
  public async getStoreStatistics(id: number | string): Promise<{
    products: number;
    orders: number;
    revenue: number;
    lastSync?: Date;
  }> {
    // 基础统计，子类可以重写
    return {
      products: 0,
      orders: 0,
      revenue: 0
    };
  }

  /**
   * 搜索店铺
   */
  public async searchStores(query: string, options: QueryOptions = {}): Promise<T[]> {
    const { page = 1, limit = 20 } = options;
    const offset = (page - 1) * limit;

    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE platform = ? AND (store_name LIKE ? OR description LIKE ?)
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const searchParam = `%${query}%`;
    const stores = await database.query<T>(sql, [this.platform, searchParam, searchParam, limit, offset]);

    return stores.map(store => this.formatOutput(store));
  }

  /**
   * 获取平台店铺统计
   */
  public async getPlatformStatistics(): Promise<{
    total: number;
    active: number;
    inactive: number;
    suspended: number;
  }> {
    // 总店铺数
    const totalResult = await database.queryOne<{ total: number }>(
      `SELECT COUNT(*) as total FROM ${this.tableName} WHERE platform = ?`,
      [this.platform]
    );
    const total = totalResult?.total || 0;

    // 按状态统计
    const statusStats = await database.query<{ status: string; count: number }>(
      `SELECT status, COUNT(*) as count FROM ${this.tableName} WHERE platform = ? GROUP BY status`,
      [this.platform]
    );

    const stats = {
      total,
      active: 0,
      inactive: 0,
      suspended: 0
    };

    statusStats.forEach(stat => {
      if (stat.status in stats) {
        (stats as any)[stat.status] = stat.count;
      }
    });

    return stats;
  }

  /**
   * 验证店铺配置
   */
  protected abstract validateStoreConfig(config: any): Promise<void>;

  /**
   * 准备插入数据
   */
  protected prepareForInsert(data: Partial<T>): Record<string, any> {
    const prepared = super.prepareForInsert(data);
    prepared.platform = this.platform;
    return prepared;
  }

  /**
   * 重写创建方法，添加验证
   */
  public async create(data: Partial<T>): Promise<T> {
    // 验证店铺名称唯一性
    if (data.store_name && await this.nameExists(data.store_name)) {
      throw new ConflictError(`Store name '${data.store_name}' already exists for ${this.platform}`);
    }

    return await super.create(data);
  }

  /**
   * 重写更新方法，添加验证
   */
  public async update(id: number | string, data: Partial<T>): Promise<T> {
    const current = await this.findByIdOrFail(id);
    
    // 店铺名称唯一性验证（排除自身）
    if (data.store_name && data.store_name !== current.store_name && await this.nameExists(data.store_name, Number(id))) {
      throw new ConflictError(`Store name '${data.store_name}' already exists for ${this.platform}`);
    }

    return await super.update(id, data);
  }
}

export default StoreModel;
