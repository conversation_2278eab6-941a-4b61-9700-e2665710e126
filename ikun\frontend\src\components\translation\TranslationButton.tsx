'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { 
  Languages, 
  Loader2, 
  CheckCircle, 
  AlertCircle 
} from 'lucide-react'
import { toast } from 'sonner'
import { apiClient } from '@/lib/api'

// 支持的语言代码
export type LanguageCode = 'en' | 'zh' | 'lt' | 'lv' | 'et' | 'fi' | 'pt' | 'es'

// 翻译按钮属性
interface TranslationButtonProps {
  text: string
  sourceLang: LanguageCode
  targetLangs: LanguageCode[]
  contentType?: 'title' | 'description' | 'selling_point'
  platform: string // 必需的平台参数
  source?: string // 翻译来源场景
  onTranslationComplete?: (translations: Record<string, string>) => void
  onTranslationError?: (error: string) => void
  disabled?: boolean
  size?: 'sm' | 'default' | 'lg'
  variant?: 'default' | 'outline' | 'secondary' | 'ghost'
  className?: string
}

// 翻译API响应类型
interface TranslationResponse {
  success: boolean
  translations?: Record<string, string>
  errors?: Record<string, string>
}

export function TranslationButton({
  text,
  sourceLang,
  targetLangs,
  contentType = 'title',
  platform,
  source,
  onTranslationComplete,
  onTranslationError,
  disabled = false,
  size = 'default',
  variant = 'outline',
  className = ''
}: TranslationButtonProps) {
  const [isTranslating, setIsTranslating] = useState(false)
  const [translationStatus, setTranslationStatus] = useState<'idle' | 'success' | 'error'>('idle')

  const handleTranslate = async () => {
    // 验证输入
    if (!text || !text.trim()) {
      toast.error('请输入要翻译的内容')
      return
    }

    if (targetLangs.length === 0) {
      toast.error('请选择目标语言')
      return
    }

    setIsTranslating(true)
    setTranslationStatus('idle')

    try {
      // 调用翻译API
      const response = await apiClient.post<TranslationResponse>('/translation/batch/forediting', {
        text: text.trim(),
        sourceLang,
        targetLangs,
        contentType,
        platform,
        source
      })

      if (response.success && response.translations) {
        setTranslationStatus('success')
        toast.success(`翻译完成，成功翻译到 ${Object.keys(response.translations).length} 种语言`)
        
        // 回调处理翻译结果
        if (onTranslationComplete) {
          onTranslationComplete(response.translations)
        }
      } else {
        setTranslationStatus('error')
        const errorMessage = response.errors 
          ? Object.values(response.errors).join(', ')
          : '翻译失败'
        
        toast.error(`翻译失败: ${errorMessage}`)
        
        if (onTranslationError) {
          onTranslationError(errorMessage)
        }
      }

    } catch (error) {
      setTranslationStatus('error')
      const errorMessage = error instanceof Error ? error.message : '翻译服务异常'
      
      console.error('Translation error:', error)
      toast.error(`翻译失败: ${errorMessage}`)
      
      if (onTranslationError) {
        onTranslationError(errorMessage)
      }
    } finally {
      setIsTranslating(false)
      
      // 3秒后重置状态
      setTimeout(() => {
        setTranslationStatus('idle')
      }, 3000)
    }
  }

  // 获取按钮图标
  const getIcon = () => {
    if (isTranslating) {
      return <Loader2 className="w-4 h-4 animate-spin" />
    }
    
    switch (translationStatus) {
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-600" />
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-600" />
      default:
        return <Languages className="w-4 h-4" />
    }
  }

  // 获取按钮文本
  const getButtonText = () => {
    if (isTranslating) {
      return '翻译中...'
    }
    
    switch (translationStatus) {
      case 'success':
        return '翻译成功'
      case 'error':
        return '翻译失败'
      default:
        return '翻译'
    }
  }

  // 获取按钮变体
  const getButtonVariant = () => {
    switch (translationStatus) {
      case 'success':
        return 'default'
      case 'error':
        return 'destructive'
      default:
        return variant
    }
  }

  return (
    <Button
      onClick={handleTranslate}
      disabled={disabled || isTranslating || !text?.trim()}
      size={size}
      variant={getButtonVariant()}
      className={`gap-2 ${className}`}
    >
      {getIcon()}
      {getButtonText()}
    </Button>
  )
}

// 语言显示名称映射
export const languageNames: Record<LanguageCode, string> = {
  en: '英语',
  zh: '中文',
  lt: '立陶宛语',
  lv: '拉脱维亚语',
  et: '爱沙尼亚语',
  fi: '芬兰语',
  pt: '葡萄牙语',
  es: '西班牙语'
}

// 获取语言显示名称
export function getLanguageName(code: LanguageCode): string {
  return languageNames[code] || code.toUpperCase()
}

// 获取目标语言显示文本
export function getTargetLanguagesText(targetLangs: LanguageCode[]): string {
  if (targetLangs.length === 0) return '无'
  if (targetLangs.length === 1) return getLanguageName(targetLangs[0])
  if (targetLangs.length <= 3) {
    return targetLangs.map(getLanguageName).join('、')
  }
  return `${targetLangs.slice(0, 2).map(getLanguageName).join('、')} 等${targetLangs.length}种语言`
}
