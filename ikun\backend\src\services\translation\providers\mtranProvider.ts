/**
 * MTran 翻译服务提供商
 * 基于原 mtranserver_api.py 的 Node.js 实现
 */

import axios, { AxiosInstance } from 'axios';
import { logger } from '@/utils/logger';
import { BaseTranslationProvider } from './baseProvider';
import {
  TranslationRequest,
  TranslationResponse,
  TranslationProviderConfig,
  LanguageCode
} from '../types';

export class MTranProvider extends BaseTranslationProvider {
  private httpClient: AxiosInstance;

  constructor(config: TranslationProviderConfig) {
    super('MTranServer', config);

    // 复用单例HTTP客户端，避免资源浪费
    this.httpClient = axios.create({
      baseURL: config.baseUrl,
      timeout: config.timeout || 30000,
      headers: {
        'Content-Type': 'application/json'
      },
      // 启用连接池复用
      maxRedirects: 5
    });
  }

  /**
   * 翻译单个文本
   */
  async translateText(request: TranslationRequest): Promise<TranslationResponse> {
    try {
      // 验证输入
      if (!this.validateText(request.text)) {
        return {
          success: false,
          error: '翻译文本不能为空'
        };
      }

      if (!this.validateLanguageCode(request.sourceLang) || !this.validateLanguageCode(request.targetLang)) {
        return {
          success: false,
          error: '不支持的语言代码'
        };
      }

      // 标准化语言代码 - 使用MTran服务支持的语言代码
      const sourceLang = this.mapToMTranLanguageCode(request.sourceLang);
      const targetLang = this.mapToMTranLanguageCode(request.targetLang);

      // 特殊处理：英文到爱沙尼亚语的标题翻译（使用中转翻译）
      if (targetLang === 'et' && sourceLang === 'en' && request.contentType === 'title') {
        return await this.translateViaChineseForEstonian(request.text);
      }

      // 直接翻译 - 如果首次失败则重试一次
      return await this.directTranslateWithRetry(request.text, sourceLang, targetLang);

    } catch (error) {
      logger.error('MTran translation failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '翻译服务异常'
      };
    }
  }

  /**
   * 带首次失败重试的翻译方法（单次翻译）
   */
  private async directTranslateWithRetry(
    text: string,
    sourceLang: string,
    targetLang: string
  ): Promise<TranslationResponse> {
    try {
      return await this.directTranslate(text, sourceLang, targetLang);
    } catch (error: any) {
      // 如果是连接错误，尝试重试一次（server.ts已预热，但仍可能偶发失败）
      if (error.message?.includes('socket hang up') ||
          error.message?.includes('ECONNRESET') ||
          error.code === 'ECONNRESET') {
        logger.warn('Request failed, retrying once...');
        await this.sleep(1000); // 等待1秒
        return await this.directTranslate(text, sourceLang, targetLang);
      }
      throw error;
    }
  }

  /**
   * 直接翻译（核心翻译方法）
   */
  private async directTranslate(
    text: string,
    sourceLang: string,
    targetLang: string
  ): Promise<TranslationResponse> {
    return await this.withRetry(async () => {
      const requestData = {
        from: sourceLang,
        to: targetLang,
        text: text
      };

      logger.info(`MTran直接翻译请求:`, {
        url: `${this.config.baseUrl}/translate`,
        data: requestData,
        textLength: text.length
      });

      const response = await this.httpClient.post('/translate', requestData);

      if (response.status === 200) {
        const result = response.data;
        
        // 验证返回格式
        if (typeof result === 'object' && result.result) {
          const translatedText = this.cleanTranslationResult(result.result);
          
          if (!translatedText) {
            throw new Error('翻译结果为空');
          }

          return {
            success: true,
            text: translatedText,
            sourceLang: sourceLang as LanguageCode,
            targetLang: targetLang as LanguageCode
          };
        } else {
          throw new Error('翻译返回格式异常');
        }
      } else {
        throw new Error(`翻译请求失败: HTTP ${response.status}`);
      }
    });
  }

  /**
   * 英文到爱沙尼亚语的中转翻译（通过中文）
   */
  private async translateViaChineseForEstonian(text: string): Promise<TranslationResponse> {
    try {
      logger.info(`Using Chinese intermediate translation for Estonian: ${text}`);

      // 第一步：英文 -> 中文
      const chineseResult = await this.directTranslateWithRetry(text, 'en', 'zh');

      if (!chineseResult.success || !chineseResult.text) {
        throw new Error('中文翻译失败');
      }

      // 移除中文翻译结果中的空格
      const chineseText = chineseResult.text.replace(/\s+/g, '');
      logger.info(`Chinese translation result: ${chineseText}`);

      // 第二步：中文 -> 爱沙尼亚语
      const estonianResult = await this.directTranslateWithRetry(chineseText, 'zh', 'et');
      
      if (!estonianResult.success || !estonianResult.text) {
        throw new Error('爱沙尼亚语翻译失败');
      }

      logger.info(`Estonian translation result: ${estonianResult.text}`);

      return {
        success: true,
        text: estonianResult.text,
        sourceLang: 'en',
        targetLang: 'et'
      };

    } catch (error) {
      logger.error('Chinese intermediate translation failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '中转翻译失败'
      };
    }
  }

  /**
   * 映射到MTran服务支持的语言代码
   */
  private mapToMTranLanguageCode(langCode: LanguageCode): string {
    const languageMap: Record<LanguageCode, string> = {
      'en': 'en',
      'zh': 'zh',
      'pt': 'pt',
      'es': 'es',
      'lt': 'lt',
      'lv': 'lv',
      'et': 'et',
      'fi': 'fi'
    };

    return languageMap[langCode] || langCode.toLowerCase();
  }

  /**
   * 批量翻译优化 - 并发处理多个翻译请求
   * 适用于SKU批量翻译场景
   */
  async translateBatchOptimized(
    requests: Array<{
      text: string;
      sourceLang: LanguageCode;
      targetLang: LanguageCode;
      contentType: 'title' | 'description' | 'selling_point';
      identifier?: string; // SKU或其他标识符
    }>,
    concurrency: number = 5 // 并发数限制
  ): Promise<Array<{
    identifier?: string;
    success: boolean;
    text?: string;
    error?: string;
    contentType: string;
    targetLang: string;
  }>> {
    logger.info(`Starting batch translation with ${requests.length} requests, concurrency: ${concurrency}`);

    // 分批处理，避免过多并发请求
    const results: any[] = [];

    for (let i = 0; i < requests.length; i += concurrency) {
      const batch = requests.slice(i, i + concurrency);

      const batchPromises = batch.map(async (request) => {
        try {
          const result = await this.translateText(request);
          return {
            identifier: request.identifier,
            success: result.success,
            text: result.text,
            error: result.error,
            contentType: request.contentType,
            targetLang: request.targetLang
          };
        } catch (error: any) {
          return {
            identifier: request.identifier,
            success: false,
            error: error.message,
            contentType: request.contentType,
            targetLang: request.targetLang
          };
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      // 批次间短暂延迟，避免服务器压力过大
      if (i + concurrency < requests.length) {
        await this.sleep(100);
      }
    }

    logger.info(`Batch translation completed. Success: ${results.filter(r => r.success).length}/${results.length}`);
    return results;
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      const testResult = await this.directTranslate('Hello', 'en', 'zh');
      return testResult.success;
    } catch (error) {
      logger.error('MTran health check failed:', error);
      return false;
    }
  }
}
