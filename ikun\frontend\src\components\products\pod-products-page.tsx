'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Construction, ArrowLeft } from 'lucide-react'

export function PodProductsPage() {
  return (
    <Card className="min-h-[calc(100vh-12rem)]">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Construction className="h-5 w-5" />
          POD产品管理
        </CardTitle>
      </CardHeader>
      <CardContent className="text-center py-20">
        <div className="space-y-6">
          <div className="text-8xl font-bold text-gray-300">404</div>
          <h2 className="text-3xl font-semibold text-gray-700">功能开发中</h2>
          <p className="text-gray-500 max-w-md mx-auto text-lg">
            POD（Print on Demand）产品管理功能正在开发中，敬请期待。
          </p>
          <div className="space-y-2 text-sm text-gray-400">
            <p>预计功能包括：</p>
            <ul className="list-disc list-inside space-y-1">
              <li>自定义设计产品管理</li>
              <li>按需打印订单处理</li>
              <li>设计模板库</li>
              <li>供应商对接</li>
            </ul>
          </div>
          <Button variant="outline" onClick={() => window.history.back()} className="mt-8">
            <ArrowLeft className="w-4 h-4 mr-2" />
            返回上一页
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
