/**
 * Database Connection Manager
 * MySQL connection pool and query utilities
 */

import mysql from 'mysql2/promise';
import { config } from '@/config/config';
import { logger } from '@/utils/logger';

class Database {
  private pool: mysql.Pool;
  private isInitialized: boolean = false;

  constructor() {
    this.pool = mysql.createPool({
      host: config.database.host,
      port: config.database.port,
      user: config.database.user,
      password: config.database.password,
      database: config.database.database,
      connectionLimit: config.database.connectionLimit,
      charset: 'utf8mb4',
      timezone: config.database.timezone, // 使用配置文件中的时区设置
      supportBigNumbers: true,
      bigNumberStrings: true,
      dateStrings: false, // 返回Date对象，便于时区处理
      multipleStatements: false
    });

    logger.info(`MySQL connection pool created with timezone: ${config.database.timezone}`);
  }

  /**
   * Initialize database - run migrations
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    try {
      logger.info('Initializing database...');

      // Test connection first
      await this.testConnection();

      // Import and run migrations
      const { migrationManager } = await import('./migration');
      await migrationManager.runMigrations();

      this.isInitialized = true;
      logger.info('Database initialized successfully');
    } catch (error) {
      logger.error('Database initialization failed:', error);
      throw error;
    }
  }

  /**
   * Test database connection
   */
  public async testConnection(): Promise<void> {
    try {
      const connection = await this.pool.getConnection();
      await connection.ping();
      connection.release();
      logger.info('Database connection test successful');
    } catch (error) {
      logger.error('Database connection test failed:', error);
      throw error;
    }
  }

  /**
   * Execute a query with parameters
   */
  public async query<T = any>(sql: string, params?: any[]): Promise<T[]> {
    try {
      const [rows] = await this.pool.execute(sql, params);
      return rows as T[];
    } catch (error) {
      logger.error('Database query error:', { sql, params, error });
      throw error;
    }
  }

  /**
   * Execute a query and return the first row
   */
  public async queryOne<T = any>(sql: string, params?: any[]): Promise<T | null> {
    const rows = await this.query<T>(sql, params);
    return rows.length > 0 ? (rows[0] as T) : null;
  }

  /**
   * Execute multiple queries in a transaction
   */
  public async transaction<T>(callback: (connection: mysql.PoolConnection) => Promise<T>): Promise<T> {
    const connection = await this.pool.getConnection();
    
    try {
      await connection.beginTransaction();
      const result = await callback(connection);
      await connection.commit();
      return result;
    } catch (error) {
      await connection.rollback();
      logger.error('Transaction error:', error);
      throw error;
    } finally {
      connection.release();
    }
  }

  /**
   * Get a connection from the pool
   */
  public async getConnection(): Promise<mysql.PoolConnection> {
    return await this.pool.getConnection();
  }

  /**
   * Execute SELECT queries and return rows
   */
  public async executeQuery<T = any>(sql: string, params?: any[]): Promise<T[]> {
    try {
      const [rows] = await this.pool.execute(sql, params);
      return rows as T[];
    } catch (error) {
      logger.error('Database query error:', { sql, params, error });
      throw error;
    }
  }

  /**
   * Execute INSERT/UPDATE/DELETE and return ResultSetHeader极其重要提示
   */
  public async executeUpdate(sql: string, params?: any[]): Promise<mysql.ResultSetHeader> {
    try {
      const [result] = await this.pool.execute(sql, params);
      return result as mysql.ResultSetHeader;
    } catch (error) {
      logger.error('Database execute update error:', { sql, params, error });
      throw error;
    }
  }

  /**
   * Execute raw SQL (for migrations, etc.)
   */
  public async execute(sql: string, params?: any[]): Promise<[any, mysql.FieldPacket[]]> {
    try {
      const result = await this.pool.execute(sql, params);
      return result;
    } catch (error) {
      logger.error('Database execute error:', { sql, params, error });
      throw error;
    }
  }



  /**
   * Close all connections
   */
  public async close(): Promise<void> {
    try {
      await this.pool.end();
      logger.info('Database connections closed');
    } catch (error) {
      logger.error('Error closing database connections:', error);
      throw error;
    }
  }

  /**
   * Get pool status
   */
  public getPoolStatus(): {
    connectionLimit: number;
  } {
    return {
      connectionLimit: config.database.connectionLimit
    };
  }
}

// Create and export database instance
export const database = new Database();

// Export types for use in other modules
export type { PoolConnection } from 'mysql2/promise';
export { RowDataPacket, ResultSetHeader, FieldPacket } from 'mysql2/promise';
