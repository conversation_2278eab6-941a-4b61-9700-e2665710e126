'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { CategorySelector } from '../category-selector'
import { DropshipProduct } from '@/types'
import {
  Edit,
  Save,
  RotateCcw,
  Image as ImageIcon,
  Package,
  Tag,
  FileText,
  Info
} from 'lucide-react'

interface EditProductCardProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  product: DropshipProduct | null
  onSave: (updateData: any) => Promise<void>
}

export function EditProductCard({ open, onOpenChange, product, onSave }: EditProductCardProps) {
  const [formData, setFormData] = useState<DropshipProduct | null>(null)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [hasChanges, setHasChanges] = useState(false)

  // 当产品数据变化时，更新表单数据
  useEffect(() => {
    if (product) {
      setFormData({ ...product })
      setHasChanges(false)
    }
  }, [product])

  // 验证必填字段
  const validateForm = () => {
    if (!formData) return false

    const newErrors: Record<string, string> = {}

    if (!formData.ean.trim()) newErrors.ean = 'EAN为必填项'
    if (!formData.category.trim()) newErrors.category = '产品类目为必填项'
    if (!formData.english_title.trim()) newErrors.english_title = '英文标题为必填项'
    if (!formData.english_description.trim()) newErrors.english_description = '英文描述为必填项'
    if (!formData.image1.trim()) newErrors.image1 = '主图为必填项'

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = async () => {
    if (formData && validateForm()) {
      // 只发送后端期望的可更新字段，确保所有字段都是正确的类型
      const updateData = {
        ean: String(formData.ean || ''),
        category: String(formData.category || ''),
        english_title: String(formData.english_title || ''),
        selling_point: formData.selling_point || [],
        english_description: String(formData.english_description || ''),
        image1: String(formData.image1 || ''),
        image2: String(formData.image2 || ''),
        image3: String(formData.image3 || ''),
        image4: String(formData.image4 || ''),
        image5: String(formData.image5 || ''),
        cost_price: Number(formData.cost_price) || 0,
        package_weight: Number(formData.package_weight) || 0,
        package_length: Number(formData.package_length) || 0,
        package_width: Number(formData.package_width) || 0,
        package_height: Number(formData.package_height) || 0,
        purchase_link: String(formData.purchase_link || ''),
        remarks: String(formData.remarks || ''),
        status: formData.status
      }

      try {
        await onSave(updateData)
        setHasChanges(false)
        onOpenChange(false)
      } catch (error) {
        console.error('保存产品失败:', error)
        // 这里可以添加错误提示
      }
    }
  }

  const handleReset = () => {
    if (product) {
      setFormData({ ...product })
      setErrors({})
      setHasChanges(false)
    }
  }

  const updateFormData = (field: string, value: any) => {
    if (!formData) return

    setFormData(prev => prev ? { ...prev, [field]: value } : null)
    setHasChanges(true)

    // 清除该字段的错误
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  // 处理类目选择
  const handleCategoryChange = (categoryPath: string, displayPath: string) => {
    if (!formData) return

    setFormData(prev => prev ? { ...prev, category: categoryPath } : null)
    setHasChanges(true)

    // 清除类目字段的错误
    if (errors.category) {
      setErrors(prev => ({ ...prev, category: '' }))
    }
  }

  // 处理卖点更新
  const updateSellingPoint = (index: number, value: string) => {
    if (!formData) return

    const newSellingPoints = [...(formData.selling_point || ['', '', '', '', ''])]
    newSellingPoints[index] = value

    setFormData(prev => prev ? { ...prev, selling_point: newSellingPoints } : null)
    setHasChanges(true)
  }

  if (!formData) {
    return null
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl h-[80vh] flex flex-col">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="w-5 h-5" />
            编辑铺货产品
            {hasChanges && <Badge variant="secondary" className="text-xs">已修改</Badge>}
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="basic" className="w-full flex flex-col flex-1 min-h-0">
          <TabsList className="grid w-full grid-cols-5 flex-shrink-0">
            <TabsTrigger value="basic" className="flex items-center gap-1">
              <Tag className="w-4 h-4" />
              基本信息
            </TabsTrigger>
            <TabsTrigger value="images" className="flex items-center gap-1">
              <ImageIcon className="w-4 h-4" />
              产品图片
            </TabsTrigger>
            <TabsTrigger value="package" className="flex items-center gap-1">
              <Package className="w-4 h-4" />
              包装信息
            </TabsTrigger>
            <TabsTrigger value="other" className="flex items-center gap-1">
              <FileText className="w-4 h-4" />
              其他信息
            </TabsTrigger>
            <TabsTrigger value="meta" className="flex items-center gap-1">
              <Info className="w-4 h-4" />
              元数据
            </TabsTrigger>
          </TabsList>

          {/* 基本信息标签页 */}
          <TabsContent value="basic" className="flex-1 overflow-y-auto min-h-[400px]">
            <Card className="h-full">
              <CardContent className="space-y-4 pt-6 h-full overflow-y-auto">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="sku">产品SKU *（不可修改）</Label>
                    <Input
                      id="sku"
                      value={formData.sku}
                      disabled
                      className="bg-muted"
                    />
                    <p className="text-xs text-muted-foreground">SKU创建后不可修改</p>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="ean">产品EAN *</Label>
                    <Input
                      id="ean"
                      value={formData.ean}
                      onChange={(e) => updateFormData('ean', e.target.value)}
                      placeholder="13位国际产品代码"
                      className={errors.ean ? 'border-red-500' : ''}
                    />
                    <div className="h-5">
                      {errors.ean && <p className="text-sm text-red-500">{errors.ean}</p>}
                    </div>
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">产品类目 *</Label>
                  <CategorySelector
                    value={formData.category}
                    onValueChange={handleCategoryChange}
                    placeholder="选择产品类目"
                    error={!!errors.category}
                  />
                  <div className="h-5">
                    {errors.category && <p className="text-sm text-red-500">{errors.category}</p>}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="english_title">英文标题 *</Label>
                  <Input
                    id="english_title"
                    value={formData.english_title}
                    onChange={(e) => updateFormData('english_title', e.target.value)}
                    placeholder="产品英文标题"
                    className={errors.english_title ? 'border-red-500' : ''}
                  />
                  <div className="h-5">
                    {errors.english_title && <p className="text-sm text-red-500">{errors.english_title}</p>}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="english_description">英文描述 *</Label>
                  <Textarea
                    id="english_description"
                    value={formData.english_description}
                    onChange={(e) => updateFormData('english_description', e.target.value)}
                    placeholder="产品英文描述"
                    rows={4}
                    className={errors.english_description ? 'border-red-500' : ''}
                  />
                  <div className="h-5">
                    {errors.english_description && <p className="text-sm text-red-500">{errors.english_description}</p>}
                  </div>
                </div>

                {/* 英文卖点 */}
                <div className="space-y-3">
                  <Label>五点描述</Label>
                  <div className="space-y-2">
                    {[0, 1, 2, 3, 4].map((index) => (
                      <div key={index} className="space-y-1">
                        <Label htmlFor={`selling_point_${index}`} className="text-sm text-muted-foreground">
                          卖点 {index + 1}
                        </Label>
                        <Input
                          id={`selling_point_${index}`}
                          value={(formData.selling_point || [])[index] || ''}
                          onChange={(e) => updateSellingPoint(index, e.target.value)}
                        />
                      </div>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="cost_price">成本价（¥）</Label>
                    <Input
                      id="cost_price"
                      type="number"
                      step="0.01"
                      value={formData.cost_price || 0}
                      onChange={(e) => updateFormData('cost_price', parseFloat(e.target.value) || 0)}
                      placeholder="0.00"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="status">产品状态</Label>
                    <Select value={formData.status} onValueChange={(value) => updateFormData('status', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="draft">草稿</SelectItem>
                        <SelectItem value="active">活跃</SelectItem>
                        <SelectItem value="inactive">禁用</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 产品图片标签页 */}
          <TabsContent value="images" className="flex-1 overflow-y-auto min-h-[400px]">
            <Card className="h-full">
              <CardContent className="space-y-4 pt-6 h-full overflow-y-auto">
                <div className="space-y-2">
                  <Label htmlFor="image1">主图 *</Label>
                  <Input
                    id="image1"
                    value={formData.image1}
                    onChange={(e) => updateFormData('image1', e.target.value)}
                    placeholder="主图URL或路径"
                    className={errors.image1 ? 'border-red-500' : ''}
                  />
                  <div className="h-5">
                    {errors.image1 && <p className="text-sm text-red-500">{errors.image1}</p>}
                  </div>
                </div>

                {[2, 3, 4, 5].map((num) => (
                  <div key={num} className="space-y-2">
                    <Label htmlFor={`image${num}`}>图片{num}</Label>
                    <Input
                      id={`image${num}`}
                      value={(formData as any)[`image${num}`] || ''}
                      onChange={(e) => updateFormData(`image${num}`, e.target.value)}
                      placeholder={`图片${num} URL或路径（可选）`}
                    />
                  </div>
                ))}
              </CardContent>
            </Card>
          </TabsContent>

          {/* 包装信息标签页 */}
          <TabsContent value="package" className="flex-1 overflow-y-auto min-h-[400px]">
            <Card className="h-full">
              <CardContent className="space-y-4 pt-6 h-full overflow-y-auto">
                <div className="space-y-2">
                  <Label htmlFor="package_weight">包装重量（g）</Label>
                  <Input
                    id="package_weight"
                    type="number"
                    value={formData.package_weight || 0}
                    onChange={(e) => updateFormData('package_weight', parseInt(e.target.value) || 0)}
                    placeholder="0"
                  />
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="package_length">长度（cm）</Label>
                    <Input
                      id="package_length"
                      type="number"
                      value={formData.package_length || 0}
                      onChange={(e) => updateFormData('package_length', parseInt(e.target.value) || 0)}
                      placeholder="0"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="package_width">宽度（cm）</Label>
                    <Input
                      id="package_width"
                      type="number"
                      value={formData.package_width || 0}
                      onChange={(e) => updateFormData('package_width', parseInt(e.target.value) || 0)}
                      placeholder="0"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="package_height">高度（cm）</Label>
                    <Input
                      id="package_height"
                      type="number"
                      value={formData.package_height || 0}
                      onChange={(e) => updateFormData('package_height', parseInt(e.target.value) || 0)}
                      placeholder="0"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* 其他信息标签页 */}
          <TabsContent value="other" className="flex-1 overflow-y-auto min-h-[400px]">
            <Card className="h-full">
              <CardContent className="space-y-4 pt-6 h-full overflow-y-auto">
                <div className="space-y-2">
                  <Label htmlFor="purchase_link">采购链接</Label>
                  <Input
                    id="purchase_link"
                    value={formData.purchase_link || ''}
                    onChange={(e) => updateFormData('purchase_link', e.target.value)}
                    placeholder="产品采购链接（可选）"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="remarks">备注</Label>
                  <Textarea
                    id="remarks"
                    value={formData.remarks || ''}
                    onChange={(e) => updateFormData('remarks', e.target.value)}
                    placeholder="产品备注信息（可选）"
                    rows={3}
                  />
                </div>

                {formData.claim_platform && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>认领平台</Label>
                      <Input value={formData.claim_platform} disabled className="bg-muted" />
                    </div>
                    <div className="space-y-2">
                      <Label>认领时间</Label>
                      <Input value={formData.claim_time || ''} disabled className="bg-muted" />
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          {/* 元数据标签页 */}
          <TabsContent value="meta" className="flex-1 overflow-y-auto min-h-[400px]">
            <Card className="h-full">
              <CardContent className="space-y-4 pt-6 h-full overflow-y-auto">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>产品ID</Label>
                    <Input value={formData.id} disabled className="bg-muted" />
                  </div>
                  <div className="space-y-2">
                    <Label>来源</Label>
                    <Input value={formData.source} disabled className="bg-muted" />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>创建时间</Label>
                    <Input value={new Date(formData.created_at).toLocaleString('zh-CN')} disabled className="bg-muted" />
                  </div>
                  <div className="space-y-2">
                    <Label>修改时间</Label>
                    <Input value={new Date(formData.updated_at).toLocaleString('zh-CN')} disabled className="bg-muted" />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>刊登次数</Label>
                  <Input value={formData.listing_count} disabled className="bg-muted" />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* 底部操作按钮 */}
        <div className="flex items-center justify-between pt-4 border-t flex-shrink-0">
          <div className="flex items-center gap-2">
            <Badge variant="outline">ID: {formData.id}</Badge>
            <Badge variant="outline">来源: {formData.source}</Badge>
            <Badge variant={formData.status === 'draft' ? 'secondary' : formData.status === 'active' ? 'default' : 'secondary'}>
              {formData.status === 'draft' ? '草稿' : formData.status === 'active' ? '活跃' : '禁用'}
            </Badge>
            {hasChanges && <Badge variant="destructive" className="text-xs">未保存</Badge>}
          </div>
          
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleReset} disabled={!hasChanges}>
              <RotateCcw className="w-4 h-4 mr-1" />
              重置
            </Button>
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              取消
            </Button>
            <Button onClick={handleSave} disabled={!hasChanges}>
              <Save className="w-4 h-4 mr-1" />
              保存
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
