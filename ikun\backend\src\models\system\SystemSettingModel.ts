/**
 * 系统设置模型
 * 管理系统配置和用户偏好设置
 */

import { BaseModel } from '@/models/base/BaseModel';
import { database } from '@/database/connection';
import { logger } from '@/utils/logger';

export interface SystemSetting {
  id: number;
  setting_key: string;
  setting_value: any; // JSON数据
  category: string;
  display_name: string;
  description?: string;
  is_public: boolean;
  is_editable: boolean;
  validation_rules?: any; // JSON数据
  default_value?: any; // JSON数据
  sort_order: number;
  created_at: Date;
  updated_at: Date;
}

export class SystemSettingModel extends BaseModel<SystemSetting> {
  protected tableName = 'system_settings';
  protected primaryKey = 'id';
  protected timestamps = true;

  protected fillable = [
    'setting_key',
    'setting_value',
    'category',
    'display_name',
    'description',
    'is_public',
    'is_editable',
    'validation_rules',
    'default_value',
    'sort_order'
  ];

  /**
   * 根据设置键获取设置值
   */
  public async getSettingValue(key: string): Promise<any> {
    try {
      const setting = await this.findOne({ setting_key: key });
      return setting ? setting.setting_value : null;
    } catch (error) {
      logger.error('Error getting setting value:', error);
      throw error;
    }
  }

  /**
   * 设置配置值
   */
  public async setSettingValue(key: string, value: any): Promise<boolean> {
    try {
      const setting = await this.findOne({ setting_key: key });
      if (!setting) {
        return false;
      }

      const jsonValue = typeof value === 'object' ? JSON.stringify(value) : value;
      await this.update(setting.id, { setting_value: jsonValue });
      return true;
    } catch (error) {
      logger.error('Error setting value:', error);
      throw error;
    }
  }

  /**
   * 根据分类获取设置
   */
  public async getSettingsByCategory(category: string): Promise<SystemSetting[]> {
    try {
      return await this.findMany({
        where: { category },
        orderBy: 'sort_order, setting_key'
      });
    } catch (error) {
      logger.error('Error getting settings by category:', error);
      throw error;
    }
  }

  /**
   * 获取公开设置（前端可见）
   */
  public async getPublicSettings(): Promise<SystemSetting[]> {
    try {
      return await this.findMany({
        where: { is_public: true },
        orderBy: 'category, sort_order'
      });
    } catch (error) {
      logger.error('Error getting public settings:', error);
      throw error;
    }
  }

  /**
   * 获取所有设置分类
   */
  public async getCategories(): Promise<string[]> {
    try {
      const query = `
        SELECT DISTINCT category 
        FROM ${this.tableName} 
        ORDER BY category ASC
      `;
      
      const results = await database.query(query);
      return results.map((row: any) => row.category);
    } catch (error) {
      logger.error('Error getting categories:', error);
      throw error;
    }
  }

  /**
   * 批量更新设置
   */
  public async batchUpdateSettings(settings: Array<{key: string, value: any}>): Promise<boolean> {
    try {
      // 使用事务处理批量更新
      return await this.transaction(async () => {
        for (const setting of settings) {
          await this.setSettingValue(setting.key, setting.value);
        }
        return true;
      });
    } catch (error) {
      logger.error('Error batch updating settings:', error);
      throw error;
    }
  }

  /**
   * 检查设置键是否存在
   */
  public async settingExists(key: string): Promise<boolean> {
    try {
      const count = await this.count({ setting_key: key });
      return count > 0;
    } catch (error) {
      logger.error('Error checking setting existence:', error);
      throw error;
    }
  }

  /**
   * 创建新设置
   */
  public async createSetting(data: Partial<SystemSetting>): Promise<SystemSetting> {
    try {
      // 检查设置键是否已存在
      if (await this.settingExists(data.setting_key!)) {
        throw new Error(`Setting key '${data.setting_key}' already exists`);
      }

      // 处理JSON字段
      const processedData = {
        ...data,
        setting_value: typeof data.setting_value === 'object' 
          ? JSON.stringify(data.setting_value) 
          : data.setting_value,
        validation_rules: typeof data.validation_rules === 'object'
          ? JSON.stringify(data.validation_rules)
          : data.validation_rules,
        default_value: typeof data.default_value === 'object'
          ? JSON.stringify(data.default_value)
          : data.default_value
      };

      const result = await this.create(processedData);
      return result;
    } catch (error) {
      logger.error('Error creating setting:', error);
      throw error;
    }
  }

  /**
   * 重置设置为默认值
   */
  public async resetToDefault(key: string): Promise<boolean> {
    try {
      const setting = await this.findOne({ setting_key: key });
      if (!setting || !setting.default_value) {
        return false;
      }

      await this.update(setting.id, { setting_value: setting.default_value });
      return true;
    } catch (error) {
      logger.error('Error resetting setting to default:', error);
      throw error;
    }
  }

  /**
   * 获取翻译服务配置
   */
  public async getTranslationProviders(): Promise<any> {
    return await this.getSettingValue('translation_providers');
  }

  /**
   * 获取翻译场景配置
   */
  public async getTranslationScenarios(): Promise<any> {
    return await this.getSettingValue('translation_scenarios');
  }

  /**
   * 获取系统基础配置
   */
  public async getSystemBasic(): Promise<any> {
    return await this.getSettingValue('system_basic');
  }
}

export default SystemSettingModel;
