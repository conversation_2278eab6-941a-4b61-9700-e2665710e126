/**
 * Dropship Product Model
 * 铺货产品数据模型 - 支持与分类表关联
 */

import { BaseModel, QueryOptions, PaginatedResult } from '@/models/base/BaseModel';
import { IRelationalModel, ISearchableModel, RelationType, RelationConfig, SearchOptions } from '@/models/base/ModelInterface';
import { DropshipProduct } from '@/types';
import { database } from '@/database/connection';
import { logger } from '@/utils/logger';
import { getBeijingTime } from '@/utils/time';
import { BadRequestError, ConflictError } from '@/middleware/error/errorHandler';

export class ProductModel extends BaseModel<DropshipProduct> implements IRelationalModel<DropshipProduct>, ISearchableModel<DropshipProduct> {
  protected tableName = 'product_dropship';
  protected primaryKey = 'id';
  protected timestamps = true;
  
  protected fillable = [
    'source',
    'sku',
    'ean',
    'category_id',
    'category',
    'english_title',
    'english_description',
    'selling_point',
    'image1',
    'image2',
    'image3',
    'image4',
    'image5',
    'cost_price',
    'package_weight',
    'package_length',
    'package_width',
    'package_height',
    'purchase_link',
    'claim_time',
    'claim_platform',
    'listing_count',
    'remarks',
    'status'
  ];

  protected hidden: string[] = [];

  // 定义关联关系
  protected relations: Record<string, RelationConfig> = {
    category: {
      type: RelationType.BELONGS_TO,
      model: 'CategoryModel',
      foreignKey: 'category_id',
      localKey: 'id'
    }
  };

  // 可搜索字段
  protected searchableFields = [
    'sku',
    'ean',
    'english_title',
    'english_description',
    'category',
    'source',
    'remarks'
  ];

  /**
   * 获取关联关系配置
   */
  public getRelation(relationName: string): RelationConfig | undefined {
    return this.relations[relationName];
  }

  /**
   * 获取可搜索字段
   */
  public getSearchableFields(): string[] {
    return this.searchableFields;
  }

  /**
   * 根据SKU查找产品
   */
  public async findBySku(sku: string): Promise<DropshipProduct | null> {
    return await this.findOne({ sku });
  }

  /**
   * 根据EAN查找产品
   */
  public async findByEan(ean: string): Promise<DropshipProduct | null> {
    return await this.findOne({ ean });
  }

  /**
   * 检查SKU是否存在
   */
  public async skuExists(sku: string, excludeId?: number): Promise<boolean> {
    if (excludeId) {
      const sql = `SELECT COUNT(*) as count FROM ${this.tableName} WHERE sku = ? AND id != ?`;
      const result = await database.queryOne<{ count: number }>(sql, [sku, excludeId]);
      return (result?.count || 0) > 0;
    }
    
    const result = await this.findOne({ sku });
    return result !== null;
  }

  /**
   * 检查EAN是否存在
   */
  public async eanExists(ean: string, excludeId?: number): Promise<boolean> {
    if (excludeId) {
      const sql = `SELECT COUNT(*) as count FROM ${this.tableName} WHERE ean = ? AND id != ?`;
      const result = await database.queryOne<{ count: number }>(sql, [ean, excludeId]);
      return (result?.count || 0) > 0;
    }
    
    const result = await this.findOne({ ean });
    return result !== null;
  }

  /**
   * 根据分类获取产品
   */
  public async findByCategory(categoryId: number, options: QueryOptions = {}): Promise<PaginatedResult<DropshipProduct>> {
    const whereCondition = { category_id: categoryId, ...options.where };
    return await this.paginate({ ...options, where: whereCondition });
  }

  /**
   * 根据状态获取产品
   */
  public async findByStatus(status: 'draft' | 'active' | 'inactive', options: QueryOptions = {}): Promise<PaginatedResult<DropshipProduct>> {
    const whereCondition = { status, ...options.where };
    return await this.paginate({ ...options, where: whereCondition });
  }

  /**
   * 根据来源获取产品
   */
  public async findBySource(source: string, options: QueryOptions = {}): Promise<PaginatedResult<DropshipProduct>> {
    const whereCondition = { source, ...options.where };
    return await this.paginate({ ...options, where: whereCondition });
  }

  /**
   * 搜索产品 (实现ISearchableModel接口)
   */
  public async search(query: string, options: SearchOptions = {}): Promise<PaginatedResult<DropshipProduct>> {
    try {
      const {
        page = 1,
        limit = 20,
        fields = this.searchableFields,
        exact = false,
        fuzzy = true
      } = options;

      const offset = (page - 1) * limit;

      // 构建搜索条件
      const searchConditions = fields.map(field => {
        if (exact) {
          return `p.${field} = ?`;
        } else {
          return `p.${field} LIKE ?`;
        }
      }).join(' OR ');

      const searchParams = fields.map(() => exact ? query : `%${query}%`);

      // 添加额外的WHERE条件
      let whereClause = `WHERE (${searchConditions})`;
      let params = [...searchParams];

      if (options.where) {
        const { whereClause: additionalWhere, params: additionalParams } = this.buildWhereClause(options.where);
        if (additionalWhere) {
          whereClause += ` AND ${additionalWhere.replace('WHERE ', '')}`;
          params.push(...additionalParams);
        }
      }

      // 获取总数 - 简化查询，不使用JOIN避免参数问题
      const countSql = `
        SELECT COUNT(*) as total
        FROM ${this.tableName} p
        ${whereClause}
      `;

      const countResult = await database.queryOne<{ total: number }>(countSql, params);
      const total = countResult?.total || 0;

      // 安全的排序字段验证
      const allowedOrderFields = ['id', 'sku', 'english_title', 'cost_price', 'created_at', 'updated_at', 'status'];
      const safeOrderBy = allowedOrderFields.includes(options.orderBy || '') ? options.orderBy : 'created_at';
      const safeOrderDirection = (options.orderDirection === 'ASC' || options.orderDirection === 'DESC') ? options.orderDirection : 'DESC';

      // 主查询 - 使用字符串拼接避免参数问题
      const sql = `
        SELECT p.*
        FROM ${this.tableName} p
        ${whereClause}
        ORDER BY p.${safeOrderBy} ${safeOrderDirection}
        LIMIT ${Number(limit)} OFFSET ${Number(offset)}
      `;

      logger.debug('Search SQL debug:', {
        sql,
        params,
        paramsCount: params.length,
        whereClause,
        limit: Number(limit),
        offset: Number(offset)
      });

      const items = await database.query<DropshipProduct>(sql, params);

      // 如果有数据，获取分类信息
      const itemsWithCategory = await Promise.all(
        items.map(async (item) => {
          if (item.category_id) {
            const category = await database.queryOne<{chinese_name: string, english_name: string}>(
              'SELECT chinese_name, english_name FROM product_categories WHERE id = ?',
              [item.category_id]
            );
            return {
              ...item,
              category_chinese_name: category?.chinese_name || null,
              category_english_name: category?.english_name || null
            };
          }
          return {
            ...item,
            category_chinese_name: null,
            category_english_name: null
          };
        })
      );

      return {
        items: itemsWithCategory.map(item => this.formatOutput(item)),
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      logger.error('Error in search:', {
        error: (error as Error).message,
        query,
        options
      });
      throw error;
    }
  }

  /**
   * 高级搜索
   */
  public async advancedSearch(filters: {
    query?: string;
    category_id?: number;
    status?: 'draft' | 'active' | 'inactive';
    source?: string;
    price_min?: number;
    price_max?: number;
    start_date?: string;
    end_date?: string;
  }, options: QueryOptions = {}): Promise<PaginatedResult<DropshipProduct>> {
    try {
      const { page = 1, limit = 20 } = options;
      const offset = (page - 1) * limit;

      let whereConditions: string[] = [];
      let params: any[] = [];

      // 文本搜索
      if (filters.query && filters.query.trim()) {
        const searchConditions = this.searchableFields.map(field => `p.${field} LIKE ?`).join(' OR ');
        whereConditions.push(`(${searchConditions})`);
        // 为每个搜索字段添加相同的搜索参数
        this.searchableFields.forEach(() => {
          params.push(`%${filters.query}%`);
        });
      }

      // 分类筛选
      if (filters.category_id) {
        whereConditions.push('p.category_id = ?');
        params.push(filters.category_id);
      }

      // 状态筛选
      if (filters.status) {
        whereConditions.push('p.status = ?');
        params.push(filters.status);
      }

      // 来源筛选
      if (filters.source) {
        whereConditions.push('p.source = ?');
        params.push(filters.source);
      }

      // 价格范围筛选
      if (filters.price_min !== undefined && filters.price_min !== null) {
        whereConditions.push('p.cost_price >= ?');
        params.push(filters.price_min);
      }

      if (filters.price_max !== undefined && filters.price_max !== null) {
        whereConditions.push('p.cost_price <= ?');
        params.push(filters.price_max);
      }

      // 日期范围筛选
      if (filters.start_date) {
        whereConditions.push('p.created_at >= ?');
        params.push(filters.start_date);
      }

      if (filters.end_date) {
        whereConditions.push('p.created_at <= ?');
        params.push(filters.end_date);
      }

      const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

      // 获取总数 - 简化查询，不使用JOIN避免参数问题
      const countSql = `
        SELECT COUNT(*) as total
        FROM ${this.tableName} p
        ${whereClause}
      `;

      const countResult = await database.queryOne<{ total: number }>(countSql, params);
      const total = countResult?.total || 0;

      // 安全的排序字段验证
      const allowedOrderFields = ['id', 'sku', 'english_title', 'cost_price', 'created_at', 'updated_at', 'status'];
      const safeOrderBy = allowedOrderFields.includes(options.orderBy || '') ? options.orderBy : 'created_at';
      const safeOrderDirection = (options.orderDirection === 'ASC' || options.orderDirection === 'DESC') ? options.orderDirection : 'DESC';

      // 主查询 - 使用字符串拼接避免参数问题
      const sql = `
        SELECT p.*
        FROM ${this.tableName} p
        ${whereClause}
        ORDER BY p.${safeOrderBy} ${safeOrderDirection}
        LIMIT ${Number(limit)} OFFSET ${Number(offset)}
      `;

      logger.debug('Advanced search SQL debug:', {
        sql,
        params,
        paramsCount: params.length,
        whereClause,
        limit: Number(limit),
        offset: Number(offset)
      });

      const items = await database.query<DropshipProduct>(sql, params);

      // 如果有数据，获取分类信息
      const itemsWithCategory = await Promise.all(
        items.map(async (item) => {
          if (item.category_id) {
            const category = await database.queryOne<{chinese_name: string, english_name: string}>(
              'SELECT chinese_name, english_name FROM product_categories WHERE id = ?',
              [item.category_id]
            );
            return {
              ...item,
              category_chinese_name: category?.chinese_name || null,
              category_english_name: category?.english_name || null
            };
          }
          return {
            ...item,
            category_chinese_name: null,
            category_english_name: null
          };
        })
      );

      return {
        items: itemsWithCategory.map(item => this.formatOutput(item)),
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      logger.error('Error in advancedSearch:', {
        error: (error as Error).message,
        filters,
        options
      });
      throw error;
    }
  }

  /**
   * 批量导入产品（优化版本）
   */
  public async batchImport(products: Partial<DropshipProduct>[]): Promise<DropshipProduct[]> {
    if (products.length === 0) {
      return [];
    }

    const results: DropshipProduct[] = [];
    const BATCH_SIZE = 50; // 每批插入50条记录，避免SQL过长

    await this.transaction(async (connection) => {
      // 分批处理
      for (let i = 0; i < products.length; i += BATCH_SIZE) {
        const batch = products.slice(i, i + BATCH_SIZE);

        try {
          // 预处理数据：添加时间戳
          const processedBatch: Partial<DropshipProduct>[] = [];
          const currentTime = getBeijingTime();

          for (const productData of batch) {
            // 验证必填字段
            if (!productData.sku || !productData.ean) {
              throw new BadRequestError(`SKU and EAN are required for product: ${productData.sku || 'unknown'}`);
            }

            // 添加时间戳（ID由数据库自动生成）
            const finalData = {
              ...productData,
              created_at: currentTime,
              updated_at: currentTime
            };

            processedBatch.push(finalData);
          }

          // 执行批量插入
          const insertedProducts = await this.performBatchInsert(connection, processedBatch);
          results.push(...insertedProducts);

          logger.info(`Batch imported ${batch.length} products (${i + 1}-${i + batch.length})`);

        } catch (error) {
          logger.error(`Batch import failed for batch ${i}-${i + batch.length}:`, error);

          // 如果批量插入失败，尝试逐个插入
          for (const productData of batch) {
            try {
              // 跳过验证，直接插入（因为已经在上面验证过）
              const finalData = {
                ...productData,
                created_at: getBeijingTime(),
                updated_at: getBeijingTime()
              };

              const insertedProduct = await super.create(finalData); // 使用父类方法跳过验证
              results.push(insertedProduct);
            } catch (singleError) {
              logger.warn('Failed to import single product:', {
                sku: productData.sku,
                error: (singleError as Error).message
              });
            }
          }
        }
      }
    });

    logger.info('Batch import completed:', {
      total: products.length,
      success: results.length,
      failed: products.length - results.length
    });

    return results;
  }

  /**
   * 执行批量插入（使用原生SQL）
   */
  private async performBatchInsert(connection: any, products: Partial<DropshipProduct>[]): Promise<DropshipProduct[]> {
    if (products.length === 0) {
      return [];
    }

    // 获取第一个产品的字段作为模板
    const firstProduct = products[0];
    const fields = Object.keys(firstProduct).filter(key => firstProduct[key as keyof DropshipProduct] !== undefined);

    // 构建批量插入SQL
    const placeholders = fields.map(() => '?').join(', ');
    const valuesSql = products.map(() => `(${placeholders})`).join(', ');

    const sql = `
      INSERT INTO ${this.tableName} (${fields.join(', ')})
      VALUES ${valuesSql}
    `;

    // 准备参数数组
    const params: any[] = [];
    products.forEach(product => {
      fields.forEach(field => {
        params.push(product[field as keyof DropshipProduct]);
      });
    });

    try {
      // 执行批量插入
      await connection.execute(sql, params);

      // 返回插入的产品
      return products.map(p => ({ ...p } as DropshipProduct));

    } catch (error) {
      logger.error('Batch insert SQL failed:', {
        fieldsCount: fields.length,
        productsCount: products.length,
        paramsLength: params.length,
        error
      });
      throw error;
    }
  }

  /**
   * 更新产品状态
   */
  public async updateStatus(id: number | string, status: 'draft' | 'active' | 'inactive'): Promise<DropshipProduct> {
    return await this.update(id, { status } as Partial<DropshipProduct>);
  }

  /**
   * 更新刊登次数
   */
  public async incrementListingCount(id: number | string): Promise<DropshipProduct> {
    const sql = `
      UPDATE ${this.tableName} 
      SET listing_count = listing_count + 1, updated_at = CURRENT_TIMESTAMP 
      WHERE id = ?
    `;
    
    await database.execute(sql, [id]);
    return await this.findByIdOrFail(id);
  }

  /**
   * 认领产品
   */
  public async claimProduct(id: number | string, platform: string): Promise<DropshipProduct> {
    return await this.update(id, {
      claim_time: getBeijingTime(),
      claim_platform: platform
    } as Partial<DropshipProduct>);
  }

  /**
   * 获取统计信息
   */
  public async getStatistics(): Promise<{
    total: number;
    by_status: Record<string, number>;
    by_source: Record<string, number>;
    by_category: Array<{ category_id: number; category_name: string; count: number }>;
  }> {
    // 总数统计
    const totalResult = await database.queryOne<{ total: number }>(
      `SELECT COUNT(*) as total FROM ${this.tableName}`
    );
    const total = totalResult?.total || 0;

    // 按状态统计
    const statusStats = await database.query<{ status: string; count: number }>(
      `SELECT status, COUNT(*) as count FROM ${this.tableName} GROUP BY status`
    );
    const by_status: Record<string, number> = {};
    statusStats.forEach(stat => {
      by_status[stat.status] = stat.count;
    });

    // 按来源统计
    const sourceStats = await database.query<{ source: string; count: number }>(
      `SELECT source, COUNT(*) as count FROM ${this.tableName} GROUP BY source`
    );
    const by_source: Record<string, number> = {};
    sourceStats.forEach(stat => {
      by_source[stat.source] = stat.count;
    });

    // 按分类统计
    const categoryStats = await database.query<{ category_id: number; category_name: string; count: number }>(
      `
      SELECT p.category_id, c.chinese_name as category_name, COUNT(*) as count
      FROM ${this.tableName} p
      LEFT JOIN product_categories c ON p.category_id = c.id
      GROUP BY p.category_id, c.chinese_name
      ORDER BY count DESC
      LIMIT 10
      `
    );

    return {
      total,
      by_status,
      by_source,
      by_category: categoryStats
    };
  }

  /**
   * 关联查询实现
   */
  public async with(relations: string[]): Promise<DropshipProduct[]> {
    // TODO: 实现关联查询逻辑
    throw new Error('Relation loading not implemented yet');
  }

  public async findWithRelations(id: number | string, relations: string[]): Promise<DropshipProduct | null> {
    // TODO: 实现单个记录的关联查询
    throw new Error('Relation loading not implemented yet');
  }

  /**
   * 验证产品数据
   */
  protected async validateProductData(data: Partial<DropshipProduct>): Promise<void> {
    // SKU唯一性验证
    if (data.sku && await this.skuExists(data.sku)) {
      throw new ConflictError(`SKU '${data.sku}' already exists`);
    }

    // EAN唯一性验证
    if (data.ean && await this.eanExists(data.ean)) {
      throw new ConflictError(`EAN '${data.ean}' already exists`);
    }

    // 分类存在性验证
    if (data.category_id) {
      const categoryExists = await database.queryOne(
        'SELECT id FROM product_categories WHERE id = ?',
        [data.category_id]
      );
      
      if (!categoryExists) {
        throw new BadRequestError(`Category with ID ${data.category_id} not found`);
      }
    }
  }

  /**
   * 重写创建方法，添加验证
   */
  public async create(data: Partial<DropshipProduct>): Promise<DropshipProduct> {
    await this.validateProductData(data);
    return await super.create(data);
  }

  /**
   * 重写更新方法，添加验证
   */
  public async update(id: number | string, data: Partial<DropshipProduct>): Promise<DropshipProduct> {
    // 获取当前记录用于排除自身的唯一性检查
    const current = await this.findByIdOrFail(id);
    
    // SKU唯一性验证（排除自身）
    if (data.sku && data.sku !== current.sku && await this.skuExists(data.sku, Number(id))) {
      throw new ConflictError(`SKU '${data.sku}' already exists`);
    }

    // EAN唯一性验证（排除自身）
    if (data.ean && data.ean !== current.ean && await this.eanExists(data.ean, Number(id))) {
      throw new ConflictError(`EAN '${data.ean}' already exists`);
    }

    // 分类存在性验证
    if (data.category_id && data.category_id !== current.category_id) {
      const categoryExists = await database.queryOne(
        'SELECT id FROM product_categories WHERE id = ?',
        [data.category_id]
      );
      
      if (!categoryExists) {
        throw new BadRequestError(`Category with ID ${data.category_id} not found`);
      }
    }

    return await super.update(id, data);
  }

  /**
   * 准备插入数据
   */
  protected prepareForInsert(data: Partial<DropshipProduct>): Record<string, any> {
    const prepared = super.prepareForInsert(data);

    // 序列化JSON字段
    if (prepared.selling_point && Array.isArray(prepared.selling_point)) {
      prepared.selling_point = JSON.stringify(prepared.selling_point);
    }

    return prepared;
  }

  /**
   * 准备更新数据
   */
  protected prepareForUpdate(data: Partial<DropshipProduct>): Record<string, any> {
    const prepared = super.prepareForUpdate(data);

    // 序列化JSON字段
    if (prepared.selling_point && Array.isArray(prepared.selling_point)) {
      prepared.selling_point = JSON.stringify(prepared.selling_point);
    }

    return prepared;
  }

  /**
   * 格式化输出数据
   */
  protected formatOutput(data: DropshipProduct): DropshipProduct {
    const formatted = super.formatOutput(data);

    // 解析JSON字段
    if (formatted.selling_point && typeof formatted.selling_point === 'string') {
      try {
        formatted.selling_point = JSON.parse(formatted.selling_point as string);
      } catch (error) {
        logger.warn('Failed to parse selling_point:', { productId: formatted.id, error });
        formatted.selling_point = [];
      }
    }

    return formatted;
  }
}

export default ProductModel;
