'use client'

import { ReactNode } from 'react'
import { TopNavigation } from './top-navigation'
import { cn } from '@/lib/utils'

interface DashboardLayoutProps {
  children: ReactNode
  className?: string
}

export function DashboardLayout({ children, className }: DashboardLayoutProps) {
  return (
    <div className="min-h-screen bg-background">
      {/* 顶部导航 */}
      <TopNavigation />

      {/* 主内容区域 */}
      <main className={cn("flex-1", className)}>
        <div className="w-full p-4">
          {children}
        </div>
      </main>
    </div>
  )
}
