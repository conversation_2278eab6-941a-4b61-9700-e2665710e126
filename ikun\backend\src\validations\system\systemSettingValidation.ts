/**
 * 系统设置验证规则
 */

import Joi from 'joi';

export const systemSettingValidation = {
  // 更新单个设置
  updateSetting: Joi.object({
    value: Joi.any().required()
  }),

  // 批量更新设置
  batchUpdateSettings: Joi.object({
    settings: Joi.array().items(
      Joi.object({
        key: Joi.string().required(),
        value: Joi.any().required()
      })
    ).min(1).required()
  }),

  // 翻译服务提供商配置验证
  translationProviders: Joi.object({
    globally_enabled: Joi.boolean().required(),
    mtran: Joi.object({
      name: Joi.string().required(),
      baseUrl: Joi.string().uri().required(),
      timeout: Joi.number().integer().min(1000).max(300000),
      enabled: Joi.boolean(),
      description: Joi.string().allow('')
    }),
    deepseek_huoshan: Joi.object({
      name: Joi.string().required(),
      baseUrl: Joi.string().uri().required(),
      timeout: Joi.number().integer().min(1000).max(300000),
      enabled: Joi.boolean(),
      description: Joi.string().allow(''),
      mult_models: Joi.array().items(
        Joi.object({
          models_name: Joi.string().required(),
          models_id: Joi.string().required(),
          apikey: Joi.string().required(),
          max_token: Joi.number().integer().min(0),
          today_used_token: Joi.number().integer().min(0),
          input_price: Joi.number().min(0),
          output_price: Joi.number().min(0)
        })
      ),
      one_model: Joi.array().items(
        Joi.object({
          models_name: Joi.string().required(),
          models_id: Joi.string().required(),
          apikey: Joi.string().required(),
          max_token: Joi.number().integer().min(0),
          today_used_token: Joi.number().integer().min(0),
          input_price: Joi.number().min(0),
          output_price: Joi.number().min(0)
        })
      ),
      prompts: Joi.object({
        title: Joi.string().required(),
        description: Joi.string().required(),
        selling_point: Joi.string().required()
      })
    }),
    with_openai: Joi.object({
      name: Joi.string().required(),
      description: Joi.string().allow(''),
      timeout: Joi.number().integer().min(1000).max(300000),
      enabled: Joi.boolean(),
      providers: Joi.array().items(
        Joi.object({
          models_name: Joi.string().required(),
          baseUrl: Joi.string().uri().required(),
          models_id: Joi.string().required(),
          apikey: Joi.string().required(),
          max_token: Joi.number().integer().min(0),
          today_used_token: Joi.number().integer().min(0)
        })
      )
    })
  }),

  // 翻译场景配置验证（支持新的嵌套结构）
  translationScenarios: Joi.object().pattern(
    Joi.string(),
    Joi.object({
      platform_name: Joi.string().required(),
      enabled: Joi.boolean(),
      scenarios: Joi.object({
        form_editing: Joi.object({
          name: Joi.string().required(),
          description: Joi.string().allow(''),
          enabled: Joi.boolean(),
          content_types: Joi.object({
            title: Joi.alternatives().try(
              Joi.string(), // 向后兼容
              Joi.object({
                provider: Joi.string().required(),
                sub_service: Joi.string().allow(null)
              })
            ),
            description: Joi.alternatives().try(
              Joi.string(), // 向后兼容
              Joi.object({
                provider: Joi.string().required(),
                sub_service: Joi.string().allow(null)
              })
            ),
            selling_point: Joi.alternatives().try(
              Joi.string(), // 向后兼容
              Joi.object({
                provider: Joi.string().required(),
                sub_service: Joi.string().allow(null)
              })
            )
          })
        }),
        batch_translation: Joi.object({
          name: Joi.string().required(),
          description: Joi.string().allow(''),
          enabled: Joi.boolean(),
          content_types: Joi.object({
            title: Joi.alternatives().try(
              Joi.string(), // 向后兼容
              Joi.object({
                provider: Joi.string().required(),
                sub_service: Joi.string().allow(null)
              })
            ),
            description: Joi.alternatives().try(
              Joi.string(), // 向后兼容
              Joi.object({
                provider: Joi.string().required(),
                sub_service: Joi.string().allow(null)
              })
            ),
            selling_point: Joi.alternatives().try(
              Joi.string(), // 向后兼容
              Joi.object({
                provider: Joi.string().required(),
                sub_service: Joi.string().allow(null)
              })
            )
          })
        }),
        translation_task: Joi.object({
          name: Joi.string().required(),
          description: Joi.string().allow(''),
          enabled: Joi.boolean(),
          content_types: Joi.object({
            title: Joi.alternatives().try(
              Joi.string(), // 向后兼容
              Joi.object({
                provider: Joi.string().required(),
                sub_service: Joi.string().allow(null)
              })
            ),
            description: Joi.alternatives().try(
              Joi.string(), // 向后兼容
              Joi.object({
                provider: Joi.string().required(),
                sub_service: Joi.string().allow(null)
              })
            ),
            selling_point: Joi.alternatives().try(
              Joi.string(), // 向后兼容
              Joi.object({
                provider: Joi.string().required(),
                sub_service: Joi.string().allow(null)
              })
            )
          })
        })
      })
    })
  ),

  // 系统基础配置验证
  systemBasicConfig: Joi.object({
    company_name: Joi.string().max(200),
    system_title: Joi.string().max(200),
    proxy_host: Joi.string().allow('').max(255),
    proxy_port: Joi.string().allow('').max(10),
    proxy_username: Joi.string().allow('').max(100),
    proxy_password: Joi.string().allow('').max(100)
  }),

  // Token统计配置验证
  tokenStatistics: Joi.object({
    deepseek_huoshan: Joi.object({
      models: Joi.object().pattern(
        Joi.string(),
        Joi.object({
          yesterday_input_tokens: Joi.number().integer().min(0),
          yesterday_output_tokens: Joi.number().integer().min(0),
          today_input_tokens: Joi.number().integer().min(0),
          today_output_tokens: Joi.number().integer().min(0),
          total_input_tokens: Joi.number().integer().min(0),
          total_output_tokens: Joi.number().integer().min(0)
        })
      )
    }),
    openai_compatible: Joi.object({
      models: Joi.object().pattern(
        Joi.string(),
        Joi.object({
          yesterday_input_tokens: Joi.number().integer().min(0),
          yesterday_output_tokens: Joi.number().integer().min(0),
          today_input_tokens: Joi.number().integer().min(0),
          today_output_tokens: Joi.number().integer().min(0),
          total_input_tokens: Joi.number().integer().min(0),
          total_output_tokens: Joi.number().integer().min(0)
        })
      )
    }),
    global_statistics: Joi.object({
      total_input_tokens: Joi.number().integer().min(0),
      total_output_tokens: Joi.number().integer().min(0),
      total_api_calls: Joi.number().integer().min(0)
    })
  }),

  // 创建设置验证
  createSetting: Joi.object({
    setting_key: Joi.string().max(100).required(),
    setting_value: Joi.any(),
    category: Joi.string().max(50).required(),
    display_name: Joi.string().max(200).required(),
    description: Joi.string().allow(''),
    is_public: Joi.boolean().default(false),
    is_editable: Joi.boolean().default(true),
    validation_rules: Joi.any(),
    default_value: Joi.any(),
    sort_order: Joi.number().integer().default(0)
  }),

  // 查询参数验证
  getSettingsByCategory: Joi.object({
    category: Joi.string().max(50).required()
  }),

  getSetting: Joi.object({
    key: Joi.string().max(100).required()
  })
};
