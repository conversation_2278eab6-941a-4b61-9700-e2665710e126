import { useState, useCallback } from 'react'
import { Store, PaginationResult } from '@/types'

interface UseStoresReturn {
  stores: Store[]
  loading: boolean
  error: string | null
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  } | null
  fetchStores: (params?: any) => Promise<void>
  createStore: (storeData: Partial<Store>) => Promise<Store>
  updateStore: (id: number, updateData: Partial<Store>) => Promise<Store>
  deleteStore: (id: number) => Promise<void>
  syncStore: (id: number) => Promise<void>
}

export function useStores(): UseStoresReturn {
  const [stores, setStores] = useState<Store[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [pagination, setPagination] = useState<{
    page: number
    limit: number
    total: number
    totalPages: number
  } | null>(null)

  const fetchStores = useCallback(async (params?: any) => {
    setLoading(true)
    setError(null)
    
    try {
      const token = localStorage.getItem('auth_token')
      if (!token) {
        throw new Error('请先登录')
      }

      const queryParams = new URLSearchParams()
      if (params) {
        Object.keys(params).forEach(key => {
          if (params[key] !== undefined && params[key] !== '') {
            queryParams.append(key, params[key])
          }
        })
      }

      const response = await fetch(`/api/v1/stores?${queryParams.toString()}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || '获取店铺列表失败')
      }

      const result: { data: PaginationResult<Store> } = await response.json()
      setStores(result.data.data)
      setPagination(result.data.pagination)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '获取店铺列表失败'
      setError(errorMessage)
      console.error('获取店铺列表失败:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  const createStore = useCallback(async (storeData: Partial<Store>): Promise<Store> => {
    const token = localStorage.getItem('auth_token')
    if (!token) {
      throw new Error('请先登录')
    }

    const response = await fetch('/api/v1/stores', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(storeData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || '创建店铺失败')
    }

    const result: { data: Store } = await response.json()
    
    // 重新获取店铺列表
    await fetchStores()
    
    return result.data
  }, [fetchStores])

  const updateStore = useCallback(async (id: number, updateData: Partial<Store>): Promise<Store> => {
    const token = localStorage.getItem('auth_token')
    if (!token) {
      throw new Error('请先登录')
    }

    const response = await fetch(`/api/v1/stores/${id}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updateData)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || '更新店铺失败')
    }

    const result: { data: Store } = await response.json()
    
    // 重新获取店铺列表
    await fetchStores()
    
    return result.data
  }, [fetchStores])

  const deleteStore = useCallback(async (id: number): Promise<void> => {
    const token = localStorage.getItem('auth_token')
    if (!token) {
      throw new Error('请先登录')
    }

    const response = await fetch(`/api/v1/stores/${id}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || '删除店铺失败')
    }

    // 重新获取店铺列表
    await fetchStores()
  }, [fetchStores])

  const syncStore = useCallback(async (id: number): Promise<void> => {
    const token = localStorage.getItem('auth_token')
    if (!token) {
      throw new Error('请先登录')
    }

    const response = await fetch(`/api/v1/stores/${id}/sync`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || '同步店铺失败')
    }

    // 重新获取店铺列表以更新状态
    await fetchStores()
  }, [fetchStores])

  return {
    stores,
    loading,
    error,
    pagination,
    fetchStores,
    createStore,
    updateStore,
    deleteStore,
    syncStore
  }
}
