/**
 * 翻译服务配置管理
 * 支持从数据库动态加载翻译提供商配置和场景配置
 */

import { logger } from '@/utils/logger';
import {
  TranslationServiceConfig,
  TranslationProviderConfig,
  LanguageCode
} from './types';

// 翻译场景配置接口
interface TranslationScenario {
  name: string;
  description?: string;
  provider: string;
  fallback_provider?: string;
  enabled: boolean;
  rules?: Record<string, string>;
  content_types: {
    title: string | { provider: string; sub_service?: string };
    description: string | { provider: string; sub_service?: string };
    selling_point: string | { provider: string; sub_service?: string };
  };
}

interface TranslationScenarios {
  [platformCode: string]: {
    scenarios: {
      [scenarioKey: string]: TranslationScenario;
    };
  };
}

/**
 * 翻译服务配置类
 */
export class TranslationConfig {
  private static instance: TranslationConfig;
  private providers: any = {}; // 翻译提供商配置（从数据库加载）
  private scenarios: TranslationScenarios = {}; // 翻译场景配置（从数据库加载）
  private fallbackConfig: TranslationServiceConfig; // 环境变量备用配置

  private constructor() {
    this.fallbackConfig = this.loadFallbackConfig();
  }

  /**
   * 获取单例实例
   */
  public static getInstance(): TranslationConfig {
    if (!TranslationConfig.instance) {
      TranslationConfig.instance = new TranslationConfig();
    }
    return TranslationConfig.instance;
  }

  /**
   * 加载硬编码默认配置
   */
  private loadFallbackConfig(): TranslationServiceConfig {
    return {
      defaultProvider: 'mtran',
      providers: {
        mtran: {
          name: 'MTranServer',
          baseUrl: 'http://111.168.2.109:8989',
          retryCount: 3,
          retryInterval: 5,
          timeout: 30000
        }
      },
      defaultSourceLang: 'en',
      defaultTargetLangs: ['lt', 'lv', 'et', 'fi'],
      enableCache: false,
      cacheExpiry: 3600
    };
  }

  /**
   * 从数据库更新所有翻译配置（全量更新）
   */
  public updateAllConfigs(translationProviders: any, translationScenarios: TranslationScenarios): void {
    try {
      this.providers = translationProviders || {};
      this.scenarios = translationScenarios || {};

      const providersCount = this.countActualProviders();

      logger.info('[TranslationConfig] 翻译配置已全量更新', {
        providersCount,
        scenariosCount: Object.keys(this.scenarios).length
      });
    } catch (error) {
      logger.error('[TranslationConfig] 更新翻译配置失败:', error);
    }
  }

  /**
   * 按照新规则统计实际可用的翻译提供商数量
   */
  private countActualProviders(): number {
    let count = 0;

    try {
      // 1. MTran本地翻译 - 如果启用就算1个
      if (this.providers.mtran && this.providers.mtran.enabled) {
        count += 1;
      }

      // 2. DeepSeek火山引擎 - 分别统计one_model和mult_models
      if (this.providers.deepseek_huoshan && this.providers.deepseek_huoshan.enabled) {
        // one_model有模型配置就算1个
        if (this.providers.deepseek_huoshan.one_model &&
            Array.isArray(this.providers.deepseek_huoshan.one_model) &&
            this.providers.deepseek_huoshan.one_model.length > 0) {
          count += 1;
        }

        // mult_models有模型配置就算1个
        if (this.providers.deepseek_huoshan.mult_models &&
            Array.isArray(this.providers.deepseek_huoshan.mult_models) &&
            this.providers.deepseek_huoshan.mult_models.length > 0) {
          count += 1;
        }
      }

      // 3. 兼容OpenAI - providers数组中每个有models_name的就算1个
      if (this.providers.with_openai &&
          this.providers.with_openai.enabled &&
          this.providers.with_openai.providers &&
          Array.isArray(this.providers.with_openai.providers)) {

        const validProviders = this.providers.with_openai.providers.filter((provider: any) =>
          provider && provider.models_name && provider.models_name.trim() !== ''
        );
        count += validProviders.length;
      }

      logger.debug('[TranslationConfig] 提供商统计详情', {
        mtran: this.providers.mtran?.enabled ? 1 : 0,
        deepseek_one_model: (this.providers.deepseek_huoshan?.one_model?.length > 0) ? 1 : 0,
        deepseek_mult_models: (this.providers.deepseek_huoshan?.mult_models?.length > 0) ? 1 : 0,
        openai_providers: this.providers.with_openai?.providers?.filter((p: any) => p?.models_name)?.length || 0,
        total: count
      });

    } catch (error) {
      logger.error('[TranslationConfig] 统计提供商数量失败:', error);
    }

    return count;
  }

  /**
   * 根据翻译请求参数智能选择提供商
   */
  public selectProvider(
    contentType: 'title' | 'description' | 'selling_point',
    sourceLang: LanguageCode,
    targetLang: LanguageCode,
    platform?: string,
    source?: string
  ): { provider: string; subService?: string } {
    logger.info('[TranslationConfig] 开始选择翻译提供商', {
      contentType,
      sourceLang,
      targetLang,
      platform,
      source,
      availableScenarios: Object.keys(this.scenarios),
      scenariosCount: Object.keys(this.scenarios).length
    });

    try {
      // 1. 尝试根据平台和场景选择
      if (platform && this.scenarios[platform]) {
        logger.info('[TranslationConfig] 找到平台场景配置', {
          platform,
          scenariosInPlatform: Object.keys(this.scenarios[platform].scenarios)
        });

        const platformScenarios = this.scenarios[platform].scenarios;

        // 如果指定了source，优先使用对应的场景
        if (source && platformScenarios[source]) {
          const scenario = platformScenarios[source];
          logger.info('[TranslationConfig] 使用指定的source场景', {
            platform,
            source,
            scenarioEnabled: scenario.enabled,
            hasContentType: !!scenario.content_types[contentType]
          });

          if (scenario.enabled && scenario.content_types[contentType]) {
            const contentConfig = scenario.content_types[contentType];

            // 处理字符串或对象类型的配置
            if (typeof contentConfig === 'string') {
              logger.info('[TranslationConfig] 找到source场景的内容类型配置(字符串)', {
                platform,
                source,
                contentType,
                provider: contentConfig
              });

              return {
                provider: contentConfig,
                subService: undefined
              };
            } else {
              logger.info('[TranslationConfig] 找到source场景的内容类型配置(对象)', {
                platform,
                source,
                contentType,
                provider: contentConfig.provider,
                subService: contentConfig.sub_service
              });

              return {
                provider: contentConfig.provider,
                subService: contentConfig.sub_service
              };
            }
          }
        }

        // 查找启用的场景（fallback逻辑）
        for (const [scenarioKey, scenario] of Object.entries(platformScenarios)) {
          logger.info('[TranslationConfig] 检查场景', {
            platform,
            scenarioKey,
            scenarioEnabled: scenario.enabled,
            hasContentType: !!scenario.content_types[contentType],
            contentTypes: Object.keys(scenario.content_types)
          });

          if (scenario.enabled && scenario.content_types[contentType]) {
            const contentConfig = scenario.content_types[contentType];

            logger.info('[TranslationConfig] 找到匹配的内容类型配置', {
              platform,
              scenarioKey,
              contentType,
              contentConfig,
              configType: typeof contentConfig
            });

            if (typeof contentConfig === 'object' && contentConfig.provider) {
              logger.info('[TranslationConfig] ✅ 场景选择提供商（对象格式）', {
                platform,
                scenarioKey,
                contentType,
                sourceLang,
                targetLang,
                selectedProvider: contentConfig.provider,
                subService: contentConfig.sub_service,
                selectionMethod: 'scenario-based-object'
              });

              return {
                provider: contentConfig.provider,
                subService: contentConfig.sub_service
              };
            } else if (typeof contentConfig === 'string') {
              logger.info('[TranslationConfig] ✅ 场景选择提供商（字符串格式）', {
                platform,
                scenarioKey,
                contentType,
                sourceLang,
                targetLang,
                selectedProvider: contentConfig,
                selectionMethod: 'scenario-based-string'
              });

              return { provider: contentConfig };
            }
          }
        }

        logger.error('[TranslationConfig] 平台场景配置存在但未找到匹配的启用场景', {
          platform,
          contentType,
          availableScenarios: Object.keys(platformScenarios),
          enabledScenarios: Object.entries(platformScenarios)
            .filter(([, scenario]) => scenario.enabled)
            .map(([key]) => key)
        });
      } else {
        logger.error('[TranslationConfig] 未找到平台场景配置', {
          platform,
          hasScenarios: Object.keys(this.scenarios).length > 0,
          availablePlatforms: Object.keys(this.scenarios)
        });
      }

      // 统一的错误处理：无论是哪种情况，都抛出配置错误
      throw new Error('无法配置翻译，请检查重试');
    } catch (error) {
      // 直接重新抛出错误，不做额外处理
      throw error;
    }
  }



  /**
   * 获取完整配置（兼容旧接口）
   */
  public getConfig(): TranslationServiceConfig {
    return { ...this.fallbackConfig };
  }

  /**
   * 获取默认提供商
   */
  public getDefaultProvider(): string {
    // 优先使用MTran作为默认提供商
    return 'mtran';
  }

  /**
   * 获取提供商配置
   */
  public getProviderConfig(providerName: string): TranslationProviderConfig | null {
    // 从数据库配置中获取
    if (this.providers[providerName]) {
      return this.convertToProviderConfig(providerName, this.providers[providerName]);
    }

    // 备用：硬编码配置（仅MTran）
    return this.fallbackConfig.providers[providerName] || null;
  }

  /**
   * 获取所有提供商配置
   */
  public getAllProviderConfigs(): Record<string, TranslationProviderConfig> {
    const configs: Record<string, TranslationProviderConfig> = {};

    // 转换数据库配置
    Object.keys(this.providers).forEach(providerName => {
      const config = this.convertToProviderConfig(providerName, this.providers[providerName]);
      if (config) {
        configs[providerName] = config;
      }
    });

    // 只有在数据库没有配置时才使用硬编码配置（仅MTran）
    Object.keys(this.fallbackConfig.providers).forEach(providerName => {
      if (!configs[providerName]) {
        configs[providerName] = this.fallbackConfig.providers[providerName];
      }
    });

    return configs;
  }

  /**
   * 转换数据库配置为提供商配置格式
   */
  private convertToProviderConfig(providerName: string, dbConfig: any): TranslationProviderConfig | null {
    try {
      if (providerName === 'mtran') {
        return {
          name: 'MTranServer',
          baseUrl: dbConfig.baseUrl,
          retryCount: 3,
          retryInterval: 5,
          timeout: dbConfig.timeout || 30000
        };
      } else if (providerName === 'deepseek_huoshan') {
        // DeepSeek火山引擎可能有多个模型
        const models = dbConfig.one_model || dbConfig.multi_model || [];
        if (models.length > 0) {
          const firstModel = Array.isArray(models) ? models[0] : models;
          return {
            name: 'DeepSeek火山引擎',
            baseUrl: 'https://ark.cn-beijing.volces.com/api/v3',
            apiKey: firstModel.apikey,
            retryCount: 3,
            retryInterval: 5,
            timeout: 30000
          };
        }
      } else if (providerName === 'with_openai') {
        // OpenAI兼容服务可能有多个提供商
        const providers = dbConfig.providers || [];
        if (providers.length > 0) {
          const firstProvider = providers[0];
          return {
            name: firstProvider.models_name || '兼容OpenAI',
            baseUrl: firstProvider.baseUrl,
            apiKey: firstProvider.apikey,
            retryCount: 3,
            retryInterval: 5,
            timeout: 30000
          };
        }
      }
      return null;
    } catch (error) {
      logger.error(`[TranslationConfig] 转换提供商配置失败 ${providerName}:`, error);
      return null;
    }
  }

  /**
   * 获取默认源语言
   */
  public getDefaultSourceLang(): LanguageCode {
    return this.fallbackConfig.defaultSourceLang;
  }

  /**
   * 获取默认目标语言列表
   */
  public getDefaultTargetLangs(): LanguageCode[] {
    return [...this.fallbackConfig.defaultTargetLangs];
  }

  /**
   * 是否启用缓存
   */
  public isCacheEnabled(): boolean {
    return this.fallbackConfig.enableCache;
  }

  /**
   * 获取缓存过期时间
   */
  public getCacheExpiry(): number {
    return this.fallbackConfig.cacheExpiry;
  }

  /**
   * 获取翻译场景配置
   */
  public getScenarios(): TranslationScenarios {
    return this.scenarios;
  }

  /**
   * 获取提供商数据库配置
   */
  public getProviders(): any {
    return this.providers;
  }

  /**
   * 获取实际可用的翻译提供商数量
   */
  public getProvidersCount(): number {
    return this.countActualProviders();
  }

  /**
   * 验证配置
   */
  public validateConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // 检查是否有可用的提供商
    if (Object.keys(this.providers).length === 0) {
      errors.push('No translation providers configured');
    }

    // 检查MTran配置
    if (this.providers.mtran && !this.providers.mtran.baseUrl) {
      errors.push('MTran provider missing baseUrl');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * 重新加载配置（保留接口兼容性）
   */
  public reloadConfig(): void {
    logger.info('[TranslationConfig] 配置重新加载请求（数据库配置通过updateAllConfigs更新）');
  }
}
