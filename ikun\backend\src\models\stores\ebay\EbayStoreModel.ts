/**
 * eBay Store Model
 * eBay平台店铺数据模型
 */

import { StoreModel, BaseStore } from '@/models/stores/StoreModel';
import { database } from '@/database/connection';
import { logger } from '@/utils/logger';
import { BadRequestError } from '@/middleware/error/errorHandler';

export interface EbayStore extends BaseStore {
  site: string;
  user_token: string;
  app_id: string;
  dev_id: string;
  cert_id: string;
  last_sync?: Date;
  sync_status?: 'idle' | 'syncing' | 'error';
  api_calls_remaining?: number;
  api_calls_reset_time?: Date;
  store_subscription?: string;
}

export class EbayStoreModel extends StoreModel<EbayStore> {
  protected tableName = 'ebay_stores';
  protected platform = 'ebay';
  
  protected fillable = [
    'store_name',
    'platform',
    'status',
    'description',
    'site',
    'user_token',
    'app_id',
    'dev_id',
    'cert_id',
    'last_sync',
    'sync_status',
    'api_calls_remaining',
    'api_calls_reset_time',
    'store_subscription'
  ];

  protected hidden = ['user_token', 'app_id', 'dev_id', 'cert_id'];

  /**
   * 根据站点获取店铺
   */
  public async findBySite(site: string): Promise<EbayStore[]> {
    return await this.findMany({ where: { site } });
  }

  /**
   * 根据应用ID查找店铺
   */
  public async findByAppId(appId: string): Promise<EbayStore | null> {
    return await this.findOne({ app_id: appId });
  }

  /**
   * 检查应用ID是否存在
   */
  public async appIdExists(appId: string, excludeId?: number): Promise<boolean> {
    if (excludeId) {
      const sql = `SELECT COUNT(*) as count FROM ${this.tableName} WHERE app_id = ? AND id != ?`;
      const result = await database.queryOne<{ count: number }>(sql, [appId, excludeId]);
      return (result?.count || 0) > 0;
    }
    
    const result = await this.findOne({ app_id: appId });
    return result !== null;
  }

  /**
   * 测试eBay API连接
   */
  public async testConnection(id: number | string): Promise<{
    success: boolean;
    message: string;
    details?: any;
  }> {
    try {
      const store = await this.findByIdOrFail(id);
      
      // TODO: 实现eBay Trading API连接测试
      // 这里应该调用eBay Trading API来验证凭据
      
      // 模拟API调用
      const testResult = {
        success: true,
        message: 'eBay API connection successful',
        details: {
          site: store.site,
          app_id: store.app_id,
          timestamp: new Date()
        }
      };

      // 更新连接状态
      await this.update(id, {
        sync_status: 'idle',
        last_sync: new Date()
      } as Partial<EbayStore>);

      logger.info('eBay store connection tested:', { storeId: id, success: true });
      return testResult;
      
    } catch (error) {
      logger.error('eBay store connection test failed:', { storeId: id, error });
      
      await this.update(id, {
        sync_status: 'error'
      } as Partial<EbayStore>);

      return {
        success: false,
        message: 'eBay API connection failed',
        details: { error: (error as Error).message }
      };
    }
  }

  /**
   * 同步eBay店铺数据
   */
  public async syncStoreData(id: number | string): Promise<{
    success: boolean;
    message: string;
    syncedData?: any;
  }> {
    try {
      const store = await this.findByIdOrFail(id);
      
      // 更新同步状态
      await this.update(id, {
        sync_status: 'syncing'
      } as Partial<EbayStore>);

      // TODO: 实现eBay数据同步逻辑
      // 1. 同步商品列表
      // 2. 同步订单信息
      // 3. 同步库存信息
      // 4. 同步店铺信息

      const syncResult = {
        listings: 0,
        orders: 0,
        inventory: 0,
        timestamp: new Date()
      };

      // 更新同步状态和时间
      await this.update(id, {
        sync_status: 'idle',
        last_sync: new Date()
      } as Partial<EbayStore>);

      logger.info('eBay store data synced:', { storeId: id, result: syncResult });

      return {
        success: true,
        message: 'eBay store data synchronized successfully',
        syncedData: syncResult
      };
      
    } catch (error) {
      logger.error('eBay store sync failed:', { storeId: id, error });
      
      await this.update(id, {
        sync_status: 'error'
      } as Partial<EbayStore>);

      return {
        success: false,
        message: 'eBay store sync failed',
        syncedData: { error: (error as Error).message }
      };
    }
  }

  /**
   * 获取eBay店铺统计信息
   */
  public async getStoreStatistics(id: number | string): Promise<{
    products: number;
    orders: number;
    revenue: number;
    lastSync?: Date;
  }> {
    const store = await this.findByIdOrFail(id);

    // TODO: 实现从eBay API获取统计数据
    // 这里应该查询相关的商品、订单表或调用eBay API

    return {
      products: 0,
      orders: 0,
      revenue: 0,
      lastSync: store.last_sync
    };
  }

  /**
   * 更新API调用配额信息
   */
  public async updateApiCalls(id: number | string, remaining: number, resetTime: Date): Promise<EbayStore> {
    return await this.update(id, {
      api_calls_remaining: remaining,
      api_calls_reset_time: resetTime
    } as Partial<EbayStore>);
  }

  /**
   * 检查API调用配额是否足够
   */
  public async checkApiCalls(id: number | string, required: number = 1): Promise<boolean> {
    const store = await this.findByIdOrFail(id);
    
    if (!store.api_calls_remaining || !store.api_calls_reset_time) {
      return true; // 如果没有配额信息，假设可用
    }

    // 检查配额是否已重置
    if (new Date() > store.api_calls_reset_time) {
      return true;
    }

    return store.api_calls_remaining >= required;
  }

  /**
   * 获取需要同步的店铺
   */
  public async getStoresForSync(maxAge: number = 3600000): Promise<EbayStore[]> {
    const cutoffTime = new Date(Date.now() - maxAge);
    
    const sql = `
      SELECT * FROM ${this.tableName} 
      WHERE status = 'active' 
      AND (last_sync IS NULL OR last_sync < ?)
      AND (sync_status IS NULL OR sync_status != 'syncing')
      ORDER BY last_sync ASC NULLS FIRST
    `;
    
    const stores = await database.query<EbayStore>(sql, [cutoffTime]);
    return stores.map(store => this.formatOutput(store));
  }

  /**
   * 创建eBay商品刊登
   */
  public async createListing(storeId: number | string, listingData: {
    title: string;
    description: string;
    category_id: string;
    price: number;
    quantity: number;
    condition: string;
    listing_type: string;
    duration: string;
  }): Promise<{ success: boolean; listing_id?: string; error?: string }> {
    try {
      const store = await this.findByIdOrFail(storeId);
      
      // TODO: 实现eBay商品刊登逻辑
      // 调用eBay Trading API的AddItem或AddFixedPriceItem
      
      logger.info('eBay listing created:', { storeId, title: listingData.title });
      
      return {
        success: true,
        listing_id: 'mock-listing-id'
      };
      
    } catch (error) {
      logger.error('eBay listing creation failed:', { storeId, error });
      
      return {
        success: false,
        error: (error as Error).message
      };
    }
  }

  /**
   * 获取eBay商品刊登列表
   */
  public async getListings(storeId: number | string, options: {
    status?: 'active' | 'ended' | 'sold';
    page?: number;
    limit?: number;
  } = {}): Promise<{
    listings: any[];
    total: number;
    page: number;
    limit: number;
  }> {
    try {
      const store = await this.findByIdOrFail(storeId);
      
      // TODO: 实现获取eBay商品刊登列表
      // 调用eBay Trading API的GetMyeBaySelling
      
      return {
        listings: [],
        total: 0,
        page: options.page || 1,
        limit: options.limit || 20
      };
      
    } catch (error) {
      logger.error('Failed to get eBay listings:', { storeId, error });
      throw error;
    }
  }

  /**
   * 验证eBay店铺配置
   */
  protected async validateStoreConfig(config: Partial<EbayStore>): Promise<void> {
    if (!config.site) {
      throw new BadRequestError('Site is required');
    }

    if (!config.user_token) {
      throw new BadRequestError('User Token is required');
    }

    if (!config.app_id) {
      throw new BadRequestError('App ID is required');
    }

    if (!config.dev_id) {
      throw new BadRequestError('Dev ID is required');
    }

    if (!config.cert_id) {
      throw new BadRequestError('Cert ID is required');
    }

    // 验证站点代码格式
    const validSites = ['US', 'UK', 'DE', 'FR', 'IT', 'ES', 'CA', 'AU', 'IN', 'SG', 'HK'];
    if (!validSites.includes(config.site)) {
      throw new BadRequestError(`Invalid site: ${config.site}`);
    }

    // 检查应用ID唯一性
    if (config.app_id && await this.appIdExists(config.app_id)) {
      throw new BadRequestError(`App ID '${config.app_id}' already exists`);
    }
  }

  /**
   * 重写创建方法，添加eBay特定验证
   */
  public async create(data: Partial<EbayStore>): Promise<EbayStore> {
    await this.validateStoreConfig(data);
    
    // 设置默认值
    const storeData = {
      ...data,
      sync_status: 'idle' as const,
      platform: this.platform
    };

    return await super.create(storeData);
  }

  /**
   * 重写更新方法，添加eBay特定验证
   */
  public async update(id: number | string, data: Partial<EbayStore>): Promise<EbayStore> {
    const current = await this.findByIdOrFail(id);
    
    // 如果更新应用ID，检查唯一性
    if (data.app_id && data.app_id !== current.app_id && await this.appIdExists(data.app_id, Number(id))) {
      throw new BadRequestError(`App ID '${data.app_id}' already exists`);
    }

    return await super.update(id, data);
  }

  /**
   * 格式化输出数据（隐藏敏感信息）
   */
  protected formatOutput(data: EbayStore): EbayStore {
    const formatted = super.formatOutput(data);
    
    // 部分隐藏敏感信息
    if (formatted.user_token) {
      formatted.user_token = `${formatted.user_token.substring(0, 8)}****`;
    }
    
    if (formatted.app_id) {
      formatted.app_id = `${formatted.app_id.substring(0, 4)}****`;
    }
    
    if (formatted.dev_id) {
      formatted.dev_id = '****';
    }
    
    if (formatted.cert_id) {
      formatted.cert_id = '****';
    }
    
    return formatted;
  }
}

export default EbayStoreModel;
