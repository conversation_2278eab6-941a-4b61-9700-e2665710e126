/**
 * Base Model Class
 * 提供所有模型的基础功能和通用方法
 */

import { database, PoolConnection } from '@/database/connection';
import { logger } from '@/utils/logger';
import { getBeijingTime } from '@/utils/time';
import { NotFoundError, BadRequestError } from '@/middleware/error/errorHandler';

export interface QueryOptions {
  page?: number;
  limit?: number;
  orderBy?: string;
  orderDirection?: 'ASC' | 'DESC';
  where?: Record<string, any>;
}

export interface PaginatedResult<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export abstract class BaseModel<T = any> {
  protected abstract tableName: string;
  protected primaryKey: string = 'id';
  protected timestamps: boolean = true;
  protected fillable: string[] = [];
  protected hidden: string[] = [];

  /**
   * 查找单条记录
   */
  public async findById(id: number | string): Promise<T | null> {
    try {
      const sql = `SELECT * FROM ${this.tableName} WHERE ${this.primaryKey} = ?`;
      const result = await database.queryOne<T>(sql, [id]);
      
      if (result) {
        return this.formatOutput(result);
      }
      
      return null;
    } catch (error) {
      logger.error(`Error finding ${this.tableName} by ID:`, { id, error });
      throw error;
    }
  }

  /**
   * 查找单条记录，不存在则抛出异常
   */
  public async findByIdOrFail(id: number | string): Promise<T> {
    const result = await this.findById(id);
    
    if (!result) {
      throw new NotFoundError(`${this.tableName} with ID ${id} not found`);
    }
    
    return result;
  }

  /**
   * 根据条件查找单条记录
   */
  public async findOne(where: Record<string, any>): Promise<T | null> {
    try {
      const { whereClause, params } = this.buildWhereClause(where);
      const sql = `SELECT * FROM ${this.tableName} ${whereClause} LIMIT 1`;
      
      const result = await database.queryOne<T>(sql, params);
      
      if (result) {
        return this.formatOutput(result);
      }
      
      return null;
    } catch (error) {
      logger.error(`Error finding one ${this.tableName}:`, { where, error });
      throw error;
    }
  }

  /**
   * 查找多条记录
   */
  public async findMany(options: QueryOptions = {}): Promise<T[]> {
    try {
      const {
        page = 1,
        limit = 20,
        orderBy = this.primaryKey,
        orderDirection = 'DESC',
        where = {}
      } = options;

      const offset = (page - 1) * limit;
      const { whereClause, params } = this.buildWhereClause(where);

      // 使用字符串拼接避免参数问题
      const sql = `
        SELECT * FROM ${this.tableName}
        ${whereClause}
        ORDER BY ${orderBy} ${orderDirection}
        LIMIT ${Number(limit)} OFFSET ${Number(offset)}
      `;

      logger.debug(`FindMany SQL for ${this.tableName}:`, {
        sql,
        params,
        paramsCount: params.length,
        limit: Number(limit),
        offset: Number(offset)
      });

      const results = await database.query<T>(sql, params);

      return results.map(item => this.formatOutput(item));
    } catch (error) {
      logger.error(`Error finding many ${this.tableName}:`, { options, error });
      throw error;
    }
  }

  /**
   * 分页查询
   */
  public async paginate(options: QueryOptions = {}): Promise<PaginatedResult<T>> {
    try {
      const {
        page = 1,
        limit = 20,
        where = {}
      } = options;

      // 获取总数
      const { whereClause, params } = this.buildWhereClause(where);
      const countSql = `SELECT COUNT(*) as total FROM ${this.tableName} ${whereClause}`;
      const countResult = await database.queryOne<{ total: number }>(countSql, params);
      const total = countResult?.total || 0;

      // 获取数据
      const items = await this.findMany(options);

      return {
        items,
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit)
      };
    } catch (error) {
      logger.error(`Error paginating ${this.tableName}:`, { options, error });
      throw error;
    }
  }

  /**
   * 创建记录
   */
  public async create(data: Partial<T>): Promise<T> {
    try {
      const cleanData = this.filterFillable(data);
      const insertData = this.prepareForInsert(cleanData);
      
      const fields = Object.keys(insertData);
      const placeholders = fields.map(() => '?').join(', ');
      const values = Object.values(insertData);
      
      const sql = `
        INSERT INTO ${this.tableName} (${fields.join(', ')})
        VALUES (${placeholders})
      `;
      
      const result = await database.executeUpdate(sql, values);

      logger.info(`Created ${this.tableName}:`, { id: result.insertId });

      return await this.findByIdOrFail(result.insertId);
    } catch (error) {
      logger.error(`Error creating ${this.tableName}:`, { data, error });
      throw error;
    }
  }

  /**
   * 更新记录
   */
  public async update(id: number | string, data: Partial<T>): Promise<T> {
    try {
      // 确保记录存在
      await this.findByIdOrFail(id);
      
      const cleanData = this.filterFillable(data);
      const updateData = this.prepareForUpdate(cleanData);
      
      if (Object.keys(updateData).length === 0) {
        return await this.findByIdOrFail(id);
      }
      
      const fields = Object.keys(updateData);
      const setClause = fields.map(field => `${field} = ?`).join(', ');
      const values = [...Object.values(updateData), id];
      
      const sql = `
        UPDATE ${this.tableName} 
        SET ${setClause}${this.timestamps ? ', updated_at = CURRENT_TIMESTAMP' : ''}
        WHERE ${this.primaryKey} = ?
      `;
      
      await database.executeUpdate(sql, values);
      
      logger.info(`Updated ${this.tableName}:`, { id });
      
      return await this.findByIdOrFail(id);
    } catch (error) {
      logger.error(`Error updating ${this.tableName}:`, { id, data, error });
      throw error;
    }
  }

  /**
   * 删除记录
   */
  public async delete(id: number | string): Promise<void> {
    try {
      // 确保记录存在
      await this.findByIdOrFail(id);
      
      const sql = `DELETE FROM ${this.tableName} WHERE ${this.primaryKey} = ?`;
      await database.executeUpdate(sql, [id]);
      
      logger.info(`Deleted ${this.tableName}:`, { id });
    } catch (error) {
      logger.error(`Error deleting ${this.tableName}:`, { id, error });
      throw error;
    }
  }

  /**
   * 检查记录是否存在
   */
  public async exists(id: number | string): Promise<boolean> {
    const result = await this.findById(id);
    return result !== null;
  }

  /**
   * 获取记录总数
   */
  public async count(where: Record<string, any> = {}): Promise<number> {
    try {
      const { whereClause, params } = this.buildWhereClause(where);
      const sql = `SELECT COUNT(*) as total FROM ${this.tableName} ${whereClause}`;
      const result = await database.queryOne<{ total: number }>(sql, params);
      return result?.total || 0;
    } catch (error) {
      logger.error(`Error counting ${this.tableName}:`, { where, error });
      throw error;
    }
  }

  /**
   * 在事务中执行操作
   */
  public async transaction<R>(callback: (connection: PoolConnection) => Promise<R>): Promise<R> {
    return await database.transaction(callback);
  }

  /**
   * 构建WHERE子句
   */
  protected buildWhereClause(where: Record<string, any>): { whereClause: string; params: any[] } {
    if (Object.keys(where).length === 0) {
      return { whereClause: '', params: [] };
    }

    const conditions: string[] = [];
    const params: any[] = [];

    Object.entries(where).forEach(([key, value]) => {
      if (value === null) {
        conditions.push(`${key} IS NULL`);
      } else if (Array.isArray(value)) {
        const placeholders = value.map(() => '?').join(', ');
        conditions.push(`${key} IN (${placeholders})`);
        params.push(...value);
      } else {
        conditions.push(`${key} = ?`);
        params.push(value);
      }
    });

    return {
      whereClause: `WHERE ${conditions.join(' AND ')}`,
      params
    };
  }

  /**
   * 过滤可填充字段
   */
  protected filterFillable(data: Partial<T>): Partial<T> {
    if (this.fillable.length === 0) {
      return data;
    }

    const filtered: Partial<T> = {};
    this.fillable.forEach(field => {
      if (field in data) {
        (filtered as any)[field] = (data as any)[field];
      }
    });

    return filtered;
  }

  /**
   * 准备插入数据
   */
  protected prepareForInsert(data: Partial<T>): Record<string, any> {
    const prepared = { ...data };

    if (this.timestamps) {
      const beijingTime = getBeijingTime();
      (prepared as any).created_at = beijingTime;
      (prepared as any).updated_at = beijingTime;
    }

    return prepared as Record<string, any>;
  }

  /**
   * 准备更新数据
   */
  protected prepareForUpdate(data: Partial<T>): Record<string, any> {
    const prepared = { ...data };
    
    // 移除主键和时间戳字段
    delete (prepared as any)[this.primaryKey];
    delete (prepared as any).created_at;
    delete (prepared as any).updated_at;
    
    return prepared as Record<string, any>;
  }

  /**
   * 格式化输出数据
   */
  protected formatOutput(data: T): T {
    if (!data) return data;
    
    const formatted = { ...data };
    
    // 移除隐藏字段
    this.hidden.forEach(field => {
      delete (formatted as any)[field];
    });
    
    return formatted;
  }
}

export default BaseModel;
