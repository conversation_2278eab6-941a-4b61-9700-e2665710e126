/**
 * 翻译API路由
 */

import { Router } from 'express';
import { translationController } from '@/controllers/translation/translationController';
import { validateRequest } from '@/middleware/validation/validateRequest';
import { translationValidation } from '@/validations/translation/translationValidation';

const router = Router();

// POST /api/v1/translation/text - 单文本翻译
router.post('/text',
  validateRequest({ body: translationValidation.translateText }),
  translationController.translateText.bind(translationController)
);

// POST /api/v1/translation/batch/forediting - 表单提交翻译
router.post('/batch/forediting',
  validateRequest({ body: translationValidation.translateBatch }),
  translationController.translateBatch.bind(translationController)
);

// POST /api/v1/translation/batch/forbatch - 产品批量翻译
router.post('/batch/forbatch',
  validateRequest({ body: translationValidation.translateProductBatch }),
  translationController.translateProductBatch.bind(translationController)
);

// POST /api/v1/translation/batch/fortask - 翻译任务批量翻译

// GET /api/v1/translation/config - 获取翻译配置
router.get('/config',
  translationController.getConfig.bind(translationController)
);

// GET /api/v1/translation/stats - 获取翻译统计
router.get('/stats',
  translationController.getStats.bind(translationController)
);

// GET /api/v1/translation/health - 健康检查
router.get('/health',
  translationController.healthCheck.bind(translationController)
);

// POST /api/v1/translation/stats/reset - 重置统计信息
router.post('/stats/reset',
  translationController.resetStats.bind(translationController)
);

export default router;
