# 产品上架功能完整技术文档

## 📋 功能概述

产品上架功能是将铺货产品表中的产品发布到不同电商平台的核心功能。不同平台对产品上架的参数要求不同，需要灵活的数据结构来支持多平台、多语言的产品上架需求。

## 🎯 设计目标

1. **多平台支持**: 支持Amazon、eBay、Shopify等多个电商平台
2. **灵活参数**: 适应不同平台的参数要求差异
3. **多语言支持**: 支持产品标题、描述的多语言版本
4. **类目管理**: 管理平台类目和类目参数
5. **状态跟踪**: 完整的上架状态管理和错误处理
6. **数据关联**: 与铺货产品表、店铺表的关联关系

## 🗄️ 数据表设计方案

### 1. 产品上架主表
**表名**: `uploadproduct_listings`

#### 功能说明
- 存储产品上架的核心信息
- 关联铺货产品表和店铺表，冗余关键字段便于快速查询
- 支持多语言标题和描述
- 管理上架状态和平台特定参数
- 完整的价格体系支持（原价、折扣价、折扣率）
- 参数版本控制和状态管理，支持类目参数更新
- 冗余主图信息，便于列表页面快速展示

#### 字段设计
```sql
CREATE TABLE uploadproduct_listings (
  id INT PRIMARY KEY AUTO_INCREMENT,

  -- 关联字段
  dropship_product_id INT NOT NULL,           -- 关联铺货产品表(product_dropship)
  platform_code VARCHAR(50) NOT NULL,        -- 平台代码(amazon/ebay/shopify等)
  store_id INT NOT NULL,                      -- 关联店铺表(stores_updata)
  platform_category_id VARCHAR(100),         -- 平台类目ID
  

  -- 冗余关键基础信息（便于快速查询和列表显示）
  sku VARCHAR(100) NOT NULL,                  -- 产品SKU（关键标识，冗余存储）
  ean VARCHAR(50),                            -- 产品EAN（关键标识，冗余存储）
  english_title VARCHAR(255),                 -- 英文标题（冗余存储）
  image1 VARCHAR(255),                        -- 主图URL（冗余存储）

  -- 上架平台特定信息（如果遇到SKU或者EAN在平台重复，需要修改，不一定会修改，只是留着备用，需要就修改）
  upstores_sku VARCHAR(100),                 -- 允许修改上架平台SKU
  upstores_ean VARCHAR(50),                   -- 允许修改上架平台EAN

  -- 多语言内容（基于英文标题翻译，与平台required_languages对应）
  multi_titles JSON,                          -- 多语言标题，结构与平台语言配置对应
  multi_descriptions JSON,                    -- 多语言描述，结构与平台语言配置对应
  listings_translation_status ENUM('pending', 'completed') DEFAULT 'pending', -- 多语言翻译状态

  -- 平台特定参数（JSON格式存储）
  platform_data JSON,                        -- 平台特定的产品参数，上架产品需要到
  platform_attributes JSON,                  -- 平台特定的类目参数和值
  attributes_status ENUM('current', 'outdated', 'needs_review') DEFAULT 'current', -- 参数状态

  -- 价格和库存信息
  original_price DECIMAL(10,2),              -- 原价
  discounted_price DECIMAL(10,2),            -- 折扣价
  discount_percentage INT,                   -- 折扣百分比
  discount_start_date TIMESTAMP NULL,        -- 折扣开始时间
  discount_end_date TIMESTAMP NULL,          -- 折扣结束时间
  stock_quantity INT DEFAULT 0,              -- 库存数量
  currency VARCHAR(10) DEFAULT 'EUR',        -- 货币单位
  
  -- 状态管理
  status ENUM('draft', 'pending', 'active', 'failed', 'inactive') DEFAULT 'draft', -- 上架状态
  listing_id VARCHAR(100),                   -- 平台返回的listing ID
  
  -- 错误和同步信息
  error_message TEXT,                        -- 上架失败的错误信息
  last_sync_at TIMESTAMP NULL,               -- 最后同步时间
  
  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  uplisting_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. 平台类目表
**表名**: `platform_categories`

#### 功能说明
- 存储各平台的类目结构
- 支持多级类目树形结构
- 存储类目的多语言名称
- 关联平台特定的类目参数

#### 字段设计
```sql
CREATE TABLE platform_categories (
  id INT PRIMARY KEY AUTO_INCREMENT,
  platform_code VARCHAR(50) NOT NULL,        -- 平台代码
  category_id VARCHAR(100) NOT NULL,         -- 平台类目ID
  parent_category_id VARCHAR(100),           -- 父类目ID
  level INT NOT NULL,                        -- 类目层级
  name_en VARCHAR(255) NOT NULL,             -- 英文名称
  name_local VARCHAR(255),                   -- 本地语言名称
  required_attributes JSON,                  -- 必填参数列表
  optional_attributes JSON,                  -- 可选参数列表
  status ENUM('active', 'inactive') DEFAULT 'active',
  last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 平台参数表
**表名**: `platform_attributes`

#### 功能说明
- 存储各平台的参数定义
- 支持不同类型的参数（文本、数字、选择等）
- 存储参数的多语言名称和描述
- 存储预定义的选项值

#### 字段设计
```sql
CREATE TABLE platform_attributes (
  id INT PRIMARY KEY AUTO_INCREMENT,
  platform_code VARCHAR(50) NOT NULL,        -- 平台代码
  attribute_id VARCHAR(100) NOT NULL,        -- 参数ID
  name_en VARCHAR(255) NOT NULL,             -- 英文名称
  name_local VARCHAR(255),                   -- 本地语言名称
  description TEXT,                          -- 参数描述
  type ENUM('text', 'number', 'select', 'multi_select', 'boolean', 'date') NOT NULL, -- 参数类型
  is_required BOOLEAN DEFAULT FALSE,         -- 是否必填
  validation_rules JSON,                     -- 验证规则
  predefined_values JSON,                    -- 预定义选项值
  status ENUM('active', 'inactive') DEFAULT 'active',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 🔄 数据关系图

```
+-------------------+       +----------------------+       +------------------+
| product_dropship  |       | uploadproduct_listings |       | stores_updata   |
+-------------------+       +----------------------+       +------------------+
| id                |<----->| dropship_product_id  |       | id              |
| sku               |       | store_id             |<----->| platform_code   |
| ean               |       | platform_code        |<----->| store_name      |
| english_title     |       | sku (冗余)            |       | status          |
| english_description|      | ean (冗余)            |       | api_key         |
| ...               |       | english_title (冗余)   |       | ...            |
+-------------------+       | multi_titles         |       +------------------+
                            | multi_descriptions   |
                            | platform_attributes  |       +------------------+
                            | ...                  |       | platforms        |
                            +----------------------+       +------------------+
                                      |                    | platform_code   |
                                      |                    | platform_name   |
                                      v                    | ...            |
                            +----------------------+       +------------------+
                            | platform_categories  |
                            +----------------------+
                            | platform_code        |
                            | category_id          |
                            | name_en              |
                            | required_attributes  |
                            | ...                  |
                            +----------------------+
```

## 📊 平台属性JSON结构示例

平台特定的类目参数和值存储在`platform_attributes`字段中，采用JSON格式：

```json
{
  "category_id": "electronics_phones",
  "category_name": "手机",
  "category_name_cn": "手机",
  "schema_version": "2024-06-23T10:30:00Z",
  "attributes": {
    "brand": {
      "name": "brand",
      "name_cn": "品牌",
      "type": "select",
      "required": true,
      "has_predefined_options": true,
      "value": "Apple",
      "options": ["Apple", "Samsung", "Huawei"],
      "options_cn": ["苹果", "三星", "华为"]
    },
    "model": {
      "name": "model",
      "name_cn": "型号",
      "type": "text",
      "required": true,
      "has_predefined_options": false,
      "value": "iPhone 15 Pro",
      "max_length": 100
    },
    "color": {
      "name": "color",
      "name_cn": "颜色",
      "type": "select",
      "required": false,
      "has_predefined_options": true,
      "value": "深空黑",
      "options": ["Space Black", "Silver", "Gold"],
      "options_cn": ["深空黑", "银色", "金色"]
    }
  }
}
```

## 🌐 多语言内容JSON结构示例

多语言标题和描述存储在`multi_titles`和`multi_descriptions`字段中：

```json
// multi_titles示例
{
  "LT": "iPhone 15 Pro - Profesionalus išmanusis telefonas",
  "LV": "iPhone 15 Pro - Profesionāls viedtālrunis",
  "ET": "iPhone 15 Pro - Professionaalne nutitelefon",
  "FI": "iPhone 15 Pro - Ammattimainen älypuhelin"
}

// multi_descriptions示例
{
  "LT": "iPhone 15 Pro yra naujausias „Apple" flagmanas...",
  "LV": "iPhone 15 Pro ir jaunākais Apple flagmanis...",
  "ET": "iPhone 15 Pro on Apple'i uusim tipptasemel nutitelefon...",
  "FI": "iPhone 15 Pro on Applen uusin lippulaivamalli..."
}
```

## 🔄 数据同步机制

### 1. 冗余字段同步
当更新`product_dropship`表中的基础信息时，需要同步更新`uploadproduct_listings`表中的冗余字段：

```sql
-- 示例：更新product_dropship后同步冗余字段
UPDATE uploadproduct_listings
SET 
  english_title = (SELECT english_title FROM product_dropship WHERE id = dropship_product_id),
  ean = (SELECT ean FROM product_dropship WHERE id = dropship_product_id),
  image1 = (SELECT image1 FROM product_dropship WHERE id = dropship_product_id)
WHERE dropship_product_id = ?;
```

### 2. 查询优化
查询时优先使用`product_dropship`表的数据，如果为空则使用冗余字段：

```sql
SELECT 
  l.*,
  COALESCE(p.english_title, l.english_title) as display_title,
  COALESCE(p.image1, l.image1) as display_image
FROM uploadproduct_listings l
LEFT JOIN product_dropship p ON l.dropship_product_id = p.id
WHERE l.platform_code = ?;
```

**说明**: 本文档为产品上架功能的完整技术文档，包含数据表设计方案和功能开发总结。

---

# 产品上架功能开发总结

## 📋 功能概述

✅ **已完成产品上架功能的完整前端开发**，包括完整的用户界面、数据管理和API集成。该功能支持多平台（Worten、PHH）产品上架管理，具备多语言翻译、状态管理、批量操作等核心功能。

## 🎉 最新更新 - 完整功能恢复

### ✅ 已解决的问题
1. **Select 组件空值错误** - 修复了 Radix UI Select 组件的空字符串值问题
2. **缺少侧边导航栏** - 添加了 DashboardLayout 和 AuthGuard 包装
3. **组件导入问题** - 恢复了完整的 UploadProductPageFull 组件使用
4. **API认证问题** - 修复了所有API请求的认证头问题

### 🚀 当前功能状态
- ✅ **完整的用户界面** - 包含侧边导航、工具栏、筛选、表格等
- ✅ **产品表单功能** - 新增/编辑产品上架表单完全可用
- ✅ **多平台支持** - Worten 和 PHH 平台页面正常工作
- ✅ **API集成** - 后端API路由已创建并可正常调用
- ✅ **认证保护** - 页面包含完整的认证和权限控制

## ✅ 已完成的功能

### 1. **前端页面结构**
```
frontend/src/app/uploadproduct/
├── worten/page.tsx          # Worten平台页面
├── phh/page.tsx            # PHH平台页面
└── test/page.tsx           # API测试页面

frontend/src/components/uploadproduct/
├── upload-product-page-full.tsx    # 主页面组件
├── upload-product-form.tsx         # 产品表单组件
├── upload-product-detail.tsx       # 产品详情组件
└── README.md                       # 功能说明文档
```

### 2. **数据管理Hooks**
```
frontend/src/hooks/
├── useUploadProducts.ts     # 产品上架数据管理
└── useDropshipProducts.ts   # 铺货产品数据管理
```

### 3. **后端API路由**
```
backend/src/routes/uploadproduct/
├── index.ts                 # 路由入口
├── listings.ts             # 产品列表管理
├── categories.ts           # 平台类目管理
└── platforms.ts            # 平台配置管理
```

### 4. **核心功能实现**
- ✅ 产品上架表单（新增/编辑）
- ✅ 产品列表管理（筛选/排序/分页）
- ✅ 多语言翻译按钮集成
- ✅ 平台类目选择器
- ✅ 产品状态管理
- ✅ 批量操作功能
- ✅ 图片预览和管理

## 🔄 技术实现要点

### 1. **多语言翻译实现**
- 使用 TranslationButton 组件
- 支持单个字段翻译和批量翻译
- 自动识别目标语言（基于平台配置）
- 翻译状态跟踪和错误处理

### 2. **表单验证策略**
- 动态验证模式（新增/编辑）
- 编辑模式下多语言字段不强制必填
- 新增模式保持严格验证
- 表单提交时手动验证并显示错误

### 3. **数据同步机制**
- 更新product_dropship时自动同步到uploadproduct_listings冗余字段
- 查询时使用COALESCE优先显示product_dropship数据
- 不删除冗余字段，通过同步保证数据一致性

## 📊 开发成果总结

### 已完成的核心功能
- ✅ 完整的用户界面和交互体验
- ✅ 多平台支持（Worten、PHH）
- ✅ 多语言管理
- ✅ 状态管理和批量操作
- ✅ 数据表结构和API路由框架
- ✅ 类型定义和错误处理

下一步需要完善后端的具体业务逻辑实现，包括数据库操作、平台API集成和翻译服务等。
