'use client'

import { useState, useEffect } from 'react'
import { AddStoreCard, EditStoreCard } from './card'
import { useStores } from '@/hooks/useStores'
import { usePlatforms } from '@/hooks/usePlatforms'
import { useConfirm } from '@/components/ui/confirm-dialog'
import { useToast } from '@/hooks/use-toast'
import { Store, Platform } from '@/types'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import {
  Plus,
  Edit,
  Trash2,
  Search,
  RotateCcw,
  MoreHorizontal,
  RefreshCw,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  AlertCircle,
  CheckCircle
} from 'lucide-react'

// 平台标签颜色映射
const platformColors: Record<string, string> = {
  'ebay': 'bg-blue-100 text-blue-800',
  'amazon': 'bg-orange-100 text-orange-800', 
  'phh': 'bg-purple-100 text-purple-800',
  'shopify': 'bg-green-100 text-green-800',
  'aliexpress': 'bg-red-100 text-red-800',
  'walmart': 'bg-yellow-100 text-yellow-800',
  'tiktok': 'bg-pink-100 text-pink-800'
}

export function StoreManagementPage() {
  const [searchValue, setSearchValue] = useState('')
  const [selectedPlatform, setSelectedPlatform] = useState<string>('all')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const { confirm } = useConfirm()
  const { toast } = useToast()

  // 卡片弹窗状态
  const [showAddCard, setShowAddCard] = useState(false)
  const [showEditCard, setShowEditCard] = useState(false)
  const [editingStore, setEditingStore] = useState<Store | null>(null)

  // 使用API hooks
  const {
    stores,
    loading: storesLoading,
    error: storesError,
    pagination,
    fetchStores,
    createStore,
    updateStore,
    deleteStore,
    syncStore
  } = useStores()

  const {
    platforms,
    loading: platformsLoading,
    error: platformsError,
    fetchPlatforms
  } = usePlatforms()

  // 初始化数据
  useEffect(() => {
    fetchPlatforms()
    fetchStores()
  }, [])

  // 筛选处理函数
  const handleReset = () => {
    setSearchValue('')
    setSelectedPlatform('all')
    setSelectedStatus('all')
    fetchStores()
  }

  // 应用筛选
  const handleApplyFilters = () => {
    const params: any = {}

    if (searchValue) {
      params.search = searchValue
    }

    if (selectedPlatform && selectedPlatform !== 'all') {
      params.platform_code = selectedPlatform
    }

    if (selectedStatus && selectedStatus !== 'all') {
      params.status = selectedStatus
    }

    fetchStores(params)
  }

  // 监听筛选条件变化，自动应用筛选
  useEffect(() => {
    const timer = setTimeout(() => {
      handleApplyFilters()
    }, 500) // 防抖处理

    return () => clearTimeout(timer)
  }, [searchValue, selectedPlatform, selectedStatus])

  // 店铺操作处理函数
  const handleAddStore = async (storeData: Partial<Store>) => {
    try {
      await createStore(storeData)
      setShowAddCard(false)
      toast({
        description: "店铺已成功添加",
        variant: "default"
      })
    } catch (error) {
      console.error('创建店铺失败:', error)
      toast({
        description: "添加店铺时发生错误，请重试",
        variant: "destructive"
      })
    }
  }

  const handleEditStore = async (updateData: any) => {
    if (!editingStore) return

    try {
      await updateStore(editingStore.id, updateData)
      setShowEditCard(false)
      setEditingStore(null)
      toast({
        description: "店铺信息已成功更新",
        variant: "default"
      })
    } catch (error) {
      console.error('更新店铺失败:', error)
      toast({
        description: "更新店铺信息时发生错误，请重试",
        variant: "destructive"
      })
    }
  }

  const handleDeleteStore = async (storeId: number) => {
    const confirmed = await confirm({
      title: '删除店铺',
      description: '确定要删除这个店铺吗？此操作不可撤销。',
      confirmText: '删除',
      cancelText: '取消',
      variant: 'destructive'
    })

    if (!confirmed) {
      return
    }

    try {
      await deleteStore(storeId)
      toast({
        description: "店铺已成功删除",
        variant: "default"
      })
    } catch (error) {
      console.error('删除店铺失败:', error)
      toast({
        description: "删除店铺时发生错误，请重试",
        variant: "destructive"
      })
    }
  }

  const handleSyncStore = async (storeId: number) => {
    try {
      await syncStore(storeId)
      toast({
        description: "店铺同步已启动",
        variant: "default"
      })
    } catch (error) {
      console.error('同步店铺失败:', error)
      toast({
        description: "同步店铺时发生错误，请重试",
        variant: "destructive"
      })
    }
  }

  const handleOpenEdit = (store: Store) => {
    setEditingStore(store)
    setShowEditCard(true)
  }

  const getStatusBadge = (status: string) => {
    const statusMap = {
      active: { label: '正常', className: 'bg-green-100 text-green-800', icon: CheckCircle },
      failed: { label: '失败', className: 'bg-red-100 text-red-800', icon: AlertCircle }
    }
    const config = statusMap[status as keyof typeof statusMap] || statusMap.active
    const Icon = config.icon
    return (
      <Badge className={`${config.className} flex items-center gap-1`}>
        <Icon className="w-3 h-3" />
        {config.label}
      </Badge>
    )
  }

  const getPlatformBadge = (platformCode: string, platformName: string) => {
    const colorClass = platformColors[platformCode] || 'bg-gray-100 text-gray-800'
    return (
      <Badge className={colorClass}>
        {platformName}
      </Badge>
    )
  }

  // 显示加载状态
  if (storesLoading && stores.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    )
  }

  // 显示错误状态
  if (storesError) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <p className="text-red-600 mb-4">加载失败: {storesError}</p>
          <Button onClick={() => fetchStores()} variant="outline">
            重试
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-[calc(100vh-4rem)]">
      <Card className="flex-1 flex flex-col">
        <CardContent className="p-0 flex-1 flex flex-col">
          {/* 工具栏 */}
          <div className="border-b p-4">
            {/* 主要操作按钮 */}
            <div className="flex flex-wrap gap-2 mb-4">
              <Button size="sm" variant="outline" onClick={() => setShowAddCard(true)}>
                <Plus className="w-4 h-4 mr-1" />
                新增店铺
              </Button>
              <Button size="sm" variant="outline" onClick={() => fetchStores()}>
                <RefreshCw className="w-4 h-4 mr-1" />
                刷新
              </Button>
            </div>

            {/* 筛选和搜索栏 */}
            <div className="flex flex-wrap gap-2 items-center">
              {/* 搜索框 */}
              <div className="relative flex-1 min-w-[200px]">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder="搜索店铺名称或平台..."
                  value={searchValue}
                  onChange={(e) => setSearchValue(e.target.value)}
                  className="pl-10"
                />
              </div>

              {/* 平台筛选 */}
              <Select value={selectedPlatform} onValueChange={setSelectedPlatform}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="选择平台" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部平台</SelectItem>
                  {platforms.map((platform) => (
                    <SelectItem key={platform.platform_code} value={platform.platform_code}>
                      {platform.platform_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* 状态筛选 */}
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger className="w-[120px]">
                  <SelectValue placeholder="选择状态" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">全部状态</SelectItem>
                  <SelectItem value="active">正常</SelectItem>
                  <SelectItem value="failed">失败</SelectItem>
                </SelectContent>
              </Select>

              {/* 重置按钮 */}
              <Button size="sm" variant="outline" onClick={handleReset}>
                <RotateCcw className="w-4 h-4 mr-1" />
                重置
              </Button>
            </div>
          </div>

          {/* 店铺列表 */}
          <div className="flex-1 overflow-auto">
            {stores.length === 0 ? (
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <p className="text-muted-foreground mb-4">暂无店铺数据</p>
                  <Button onClick={() => setShowAddCard(true)} variant="outline">
                    <Plus className="w-4 h-4 mr-2" />
                    添加第一个店铺
                  </Button>
                </div>
              </div>
            ) : (
              <div className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {stores.map((store) => (
                    <Card key={store.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between mb-3">
                          <div className="flex-1">
                            <h3 className="font-medium text-sm mb-1 truncate" title={store.store_name}>
                              {store.store_name}
                            </h3>
                            <div className="flex items-center gap-2 mb-2">
                              {getPlatformBadge(store.platform_code, store.platform_name || store.platform_code)}
                              {getStatusBadge(store.status)}
                            </div>
                          </div>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => handleOpenEdit(store)}>
                                <Edit className="mr-2 h-4 w-4" />
                                编辑
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleSyncStore(store.id)}>
                                <RefreshCw className="mr-2 h-4 w-4" />
                                同步
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                onClick={() => handleDeleteStore(store.id)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                删除
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </div>

                        <div className="space-y-2 text-xs text-muted-foreground">
                          {store.site && (
                            <div className="flex items-center gap-1">
                              <Link className="w-3 h-3" />
                              <span>站点: {store.site}</span>
                            </div>
                          )}
                          <div className="flex items-center gap-1">
                            <Settings className="w-3 h-3" />
                            <span>创建: {new Date(store.created_at).toLocaleDateString()}</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 添加店铺卡片 */}
      {showAddCard && (
        <AddStoreCard
          platforms={platforms}
          onSubmit={handleAddStore}
          onCancel={() => setShowAddCard(false)}
        />
      )}

      {/* 编辑店铺卡片 */}
      {showEditCard && editingStore && (
        <EditStoreCard
          store={editingStore}
          platforms={platforms}
          onSubmit={handleEditStore}
          onCancel={() => {
            setShowEditCard(false)
            setEditingStore(null)
          }}
        />
      )}
    </div>
  )
}
