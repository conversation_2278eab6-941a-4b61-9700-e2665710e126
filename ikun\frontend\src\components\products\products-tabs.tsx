'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { DropshipProductsPage } from './dropship-products-page'
import { PodProductsPage } from './pod-products-page'

export function ProductsTabs() {
  return (
    <Tabs defaultValue="dropship" className="w-full">
      <TabsList className="grid w-full grid-cols-2 max-w-md">
        <TabsTrigger value="dropship">铺货产品</TabsTrigger>
        <TabsTrigger value="pod">POD产品</TabsTrigger>
      </TabsList>
      
      <TabsContent value="dropship" className="mt-6">
        <DropshipProductsPage />
      </TabsContent>
      
      <TabsContent value="pod" className="mt-6">
        <PodProductsPage />
      </TabsContent>
    </Tabs>
  )
}
