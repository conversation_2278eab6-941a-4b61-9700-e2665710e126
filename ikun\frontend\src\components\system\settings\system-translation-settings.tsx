'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { ConfirmDialog, useConfirm } from '@/components/ui/confirm-dialog'
import { Loader2, Save, Globe, Settings, BarChart3, Plus, Edit, Trash2, ChevronDown, ChevronRight, Wifi } from 'lucide-react'
import { useSystemSettings } from '@/hooks/useSystemSettings'
import { toast } from 'sonner'
import { testProviderConnection, ConnectionTestResult } from './connection-tester'

// 表单验证模式
const deepseekModelSchema = z.object({
  models_name: z.string().min(1, '模型名称不能为空'),
  models_id: z.string().min(1, '模型ID不能为空'),
  apikey: z.string().min(1, 'API密钥不能为空'),
  max_token: z.coerce.number().min(0, '最大Token不能小于0'),
  today_used_token: z.coerce.number().min(0, '今日使用Token不能小于0').default(0),
  input_price: z.coerce.number().min(0, '输入价格不能小于0'),
  output_price: z.coerce.number().min(0, '输出价格不能小于0')
})

const mtranConfigSchema = z.object({
  baseUrl: z.string().url('请输入有效的URL'),
  timeout: z.coerce.number().min(1000, '超时时间不能少于1秒').max(300000, '超时时间不能超过5分钟')
})

const openaiProviderSchema = z.object({
  models_name: z.string().min(1, '提供商名称不能为空'),
  baseUrl: z.string().url('请输入有效的URL'),
  models_id: z.string().min(1, '模型ID不能为空'),
  apikey: z.string().min(1, 'API密钥不能为空'),
  max_token: z.coerce.number().min(0, '最大Token不能小于0').optional(),
  today_used_token: z.coerce.number().min(0, '今日使用Token不能小于0').default(0)
})

const promptsSchema = z.object({
  title: z.string().min(1, '标题提示词不能为空'),
  description: z.string().min(1, '描述提示词不能为空'),
  selling_point: z.string().min(1, '卖点提示词不能为空')
})

// 平台配置
const platformConfigs = [
  { code: 'worten', name: 'Worten', languages: ['PT', 'ES'] },
  { code: 'phh', name: 'PHH平台', languages: ['LT', 'LV', 'EE', 'FI'] }
]

// 平台翻译配置 - 定义每个平台支持的内容类型
const platformTranslationConfig = {
  worten: {
    needsTitle: true,
    needsDescription: true,
    needsSellingPoints: false  // Worten 不需要卖点
  },
  phh: {
    needsTitle: true,
    needsDescription: true,
    needsSellingPoints: true   // PHH 需要卖点
  }
}

// 翻译场景
const translationScenarios = [
  { key: 'form_editing', name: '表单翻译接口', },
  { key: 'form_batch', name: '批量翻译接口', },
  { key: 'form_task', name: '任务翻译接口', }
]

// 所有内容类型
const allContentTypes = [
  { key: 'title', name: '标题' },
  { key: 'description', name: '描述' },
  { key: 'selling_point', name: '卖点' }
]

// 根据平台获取支持的内容类型
const getPlatformContentTypes = (platformCode: string) => {
  const config = platformTranslationConfig[platformCode as keyof typeof platformTranslationConfig]
  if (!config) {
    // 默认支持所有内容类型
    return allContentTypes
  }

  return allContentTypes.filter(contentType => {
    switch (contentType.key) {
      case 'title':
        return config.needsTitle
      case 'description':
        return config.needsDescription
      case 'selling_point':
        return config.needsSellingPoints
      default:
        return true
    }
  })
}

// 翻译服务选项
const translationServiceOptions = [
  { value: 'mtran', label: 'MTran本地翻译' },
  { value: 'deepseek_single', label: 'DeepSeek单模型' },
  { value: 'deepseek_multi', label: 'DeepSeek多模型' },
  { value: 'openai_compatible', label: 'OpenAI兼容服务' }
]

export function SystemTranslationSettings() {
  const { fetchSetting, updateSetting } = useSystemSettings()
  const { confirm } = useConfirm()
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [providers, setProviders] = useState<any>(null)
  const [scenarios, setScenarios] = useState<any>(null)
  const [activeTab, setActiveTab] = useState('providers')

  // 编辑状态
  const [editingModel, setEditingModel] = useState<any>(null)
  const [editingProvider, setEditingProvider] = useState<any>(null)
  const [editingMtran, setEditingMtran] = useState<any>(null)
  const [editingScenario, setEditingScenario] = useState<string | null>(null) // 格式: "platform_scenario"
  const [isModelDialogOpen, setIsModelDialogOpen] = useState(false)
  const [isProviderDialogOpen, setIsProviderDialogOpen] = useState(false)
  const [isMtranDialogOpen, setIsMtranDialogOpen] = useState(false)

  // 测试连接状态
  const [testingConnection, setTestingConnection] = useState(false)
  const [connectionResult, setConnectionResult] = useState<ConnectionTestResult | null>(null)

  // 表单实例
  const modelForm = useForm({
    resolver: zodResolver(deepseekModelSchema),
    defaultValues: {
      models_name: '',
      models_id: '',
      apikey: '',
      max_token: 0,
      today_used_token: 0,
      input_price: 0,
      output_price: 0
    }
  })

  const providerForm = useForm({
    resolver: zodResolver(openaiProviderSchema),
    defaultValues: {
      models_name: '',
      baseUrl: '',
      models_id: '',
      apikey: '',
      max_token: 0,
      today_used_token: 0
    }
  })

  const mtranForm = useForm({
    resolver: zodResolver(mtranConfigSchema),
    defaultValues: {
      baseUrl: '',
      timeout: 30000
    }
  })

  // 加载翻译服务提供商配置
  const loadProviders = async () => {
    try {
      setLoading(true)
      const providersData = await fetchSetting('translation_providers')
      setProviders(providersData.setting_value)
    } catch (error) {
      console.error('加载翻译服务提供商配置失败:', error)
      toast.error('加载翻译服务提供商配置失败')
    } finally {
      setLoading(false)
    }
  }

  // 加载翻译场景配置
  const loadScenarios = async () => {
    try {
      setLoading(true)
      const scenariosData = await fetchSetting('translation_scenarios')
      setScenarios(scenariosData.setting_value)
    } catch (error) {
      console.error('加载翻译场景配置失败:', error)
      toast.error('加载翻译场景配置失败')
    } finally {
      setLoading(false)
    }
  }

  // 更新全局翻译开关
  const updateGloballyEnabled = async (enabled: boolean) => {
    try {
      setSaving(true)
      const updatedProviders = {
        ...providers,
        globally_enabled: enabled
      }
      await updateSetting('translation_providers', updatedProviders)
      setProviders(updatedProviders)
      toast.success('全局翻译开关更新成功')
    } catch (error) {
      console.error('更新全局翻译开关失败:', error)
      toast.error('更新全局翻译开关失败')
    } finally {
      setSaving(false)
    }
  }

  // 更新服务提供商状态
  const updateProviderStatus = async (providerKey: string, enabled: boolean) => {
    try {
      setSaving(true)
      const updatedProviders = {
        ...providers,
        [providerKey]: {
          ...providers[providerKey],
          enabled
        }
      }
      await updateSetting('translation_providers', updatedProviders)
      setProviders(updatedProviders)
      toast.success('服务提供商状态更新成功')
    } catch (error) {
      console.error('更新服务提供商状态失败:', error)
      toast.error('更新服务提供商状态失败')
    } finally {
      setSaving(false)
    }
  }

  // DeepSeek模型管理
  const openModelDialog = (model: any = null, isMultiModel: boolean = true) => {
    setEditingModel({ ...model, isMultiModel, isNew: !model })
    setConnectionResult(null) // 重置连接结果
    if (model) {
      modelForm.reset(model)
    } else {
      modelForm.reset({
        models_name: '',
        models_id: '',
        apikey: '',
        max_token: 0,
        today_used_token: 0,
        input_price: 0,
        output_price: 0
      })
    }
    setIsModelDialogOpen(true)
  }

  const saveModel = async (data: any) => {
    try {
      setSaving(true)
      const updatedProviders = { ...providers }



      if (editingModel.isMultiModel) {
        // 多模型配置
        if (!updatedProviders.deepseek_huoshan.mult_models) {
          updatedProviders.deepseek_huoshan.mult_models = []
        }

        if (editingModel.isNew) {
          updatedProviders.deepseek_huoshan.mult_models.push(data)
        } else {
          const index = updatedProviders.deepseek_huoshan.mult_models.findIndex(
            (m: any) => m.models_id === editingModel.models_id
          )
          if (index !== -1) {
            updatedProviders.deepseek_huoshan.mult_models[index] = data
          }
        }
      } else {
        // 单模型配置 - 现在支持数组格式
        if (!updatedProviders.deepseek_huoshan.one_model) {
          updatedProviders.deepseek_huoshan.one_model = []
        }

        if (editingModel.isNew) {
          updatedProviders.deepseek_huoshan.one_model.push(data)
        } else {
          const index = updatedProviders.deepseek_huoshan.one_model.findIndex(
            (m: any) => m.models_id === editingModel.models_id
          )
          if (index !== -1) {
            updatedProviders.deepseek_huoshan.one_model[index] = data
          }
        }
      }

      // 保存翻译提供商配置
      await updateSetting('translation_providers', updatedProviders)
      setProviders(updatedProviders)
      setIsModelDialogOpen(false)
      toast.success('模型配置保存成功')
    } catch (error) {
      console.error('保存模型配置失败:', error)
      toast.error('保存模型配置失败')
    } finally {
      setSaving(false)
    }
  }

  const deleteModel = async (modelId: string, isMultiModel: boolean, modelName?: string) => {
    const confirmed = await confirm({
      title: '确认删除',
      description: `确定要删除${isMultiModel ? '模型' : '单模型配置'} "${modelName || modelId}" 吗？此操作不可撤销。`,
      variant: 'destructive',
      confirmText: '删除',
      cancelText: '取消'
    })

    if (!confirmed) return

    try {
      setSaving(true)
      const updatedProviders = { ...providers }

      if (isMultiModel) {
        updatedProviders.deepseek_huoshan.mult_models =
          updatedProviders.deepseek_huoshan.mult_models.filter(
            (m: any) => m.models_id !== modelId
          )
      } else {
        // 单模型配置 - 从数组中删除指定模型
        if (updatedProviders.deepseek_huoshan.one_model && Array.isArray(updatedProviders.deepseek_huoshan.one_model)) {
          updatedProviders.deepseek_huoshan.one_model =
            updatedProviders.deepseek_huoshan.one_model.filter(
              (m: any) => m.models_id !== modelId
            )
        }
      }

      await updateSetting('translation_providers', updatedProviders)
      setProviders(updatedProviders)
      toast.success('模型删除成功')
    } catch (error) {
      console.error('删除模型失败:', error)
      toast.error('删除模型失败')
    } finally {
      setSaving(false)
    }
  }

  // OpenAI提供商管理
  const openProviderDialog = (provider: any = null) => {
    setEditingProvider({ ...provider, isNew: !provider })
    setConnectionResult(null) // 重置连接结果
    if (provider) {
      providerForm.reset(provider)
    } else {
      providerForm.reset({
        models_name: '',
        baseUrl: '',
        models_id: '',
        apikey: '',
        max_token: 0,
        today_used_token: 0
      })
    }
    setIsProviderDialogOpen(true)
  }

  const saveProvider = async (data: any) => {
    try {
      setSaving(true)
      const updatedProviders = { ...providers }

      if (!updatedProviders.with_openai.providers) {
        updatedProviders.with_openai.providers = []
      }

      if (editingProvider.isNew) {
        updatedProviders.with_openai.providers.push(data)
      } else {
        const index = updatedProviders.with_openai.providers.findIndex(
          (p: any) => p.models_id === editingProvider.models_id
        )
        if (index !== -1) {
          updatedProviders.with_openai.providers[index] = data
        }
      }

      await updateSetting('translation_providers', updatedProviders)
      setProviders(updatedProviders)
      setIsProviderDialogOpen(false)
      toast.success('提供商配置保存成功')
    } catch (error) {
      console.error('保存提供商配置失败:', error)
      toast.error('保存提供商配置失败')
    } finally {
      setSaving(false)
    }
  }

  const deleteProvider = async (modelId: string, providerName?: string) => {
    const confirmed = await confirm({
      title: '确认删除',
      description: `确定要删除提供商 "${providerName || modelId}" 吗？此操作不可撤销。`,
      variant: 'destructive',
      confirmText: '删除',
      cancelText: '取消'
    })

    if (!confirmed) return

    try {
      setSaving(true)
      const updatedProviders = { ...providers }

      updatedProviders.with_openai.providers =
        updatedProviders.with_openai.providers.filter(
          (p: any) => p.models_id !== modelId
        )

      await updateSetting('translation_providers', updatedProviders)
      setProviders(updatedProviders)
      toast.success('提供商删除成功')
    } catch (error) {
      console.error('删除提供商失败:', error)
      toast.error('删除提供商失败')
    } finally {
      setSaving(false)
    }
  }

  // MTran配置管理
  const openMtranDialog = () => {
    const mtranConfig = providers.mtran
    setEditingMtran(mtranConfig)
    mtranForm.reset({
      baseUrl: mtranConfig?.baseUrl || '',
      timeout: mtranConfig?.timeout || 30000
    })
    setIsMtranDialogOpen(true)
  }

  const saveMtranConfig = async (data: any) => {
    try {
      setSaving(true)
      const updatedProviders = {
        ...providers,
        mtran: {
          ...providers.mtran,
          ...data
        }
      }

      await updateSetting('translation_providers', updatedProviders)
      setProviders(updatedProviders)
      setIsMtranDialogOpen(false)
      toast.success('MTran配置保存成功')
    } catch (error) {
      console.error('保存MTran配置失败:', error)
      toast.error('保存MTran配置失败')
    } finally {
      setSaving(false)
    }
  }

  // 场景编辑状态管理
  const startEditingScenario = (platformCode: string, scenarioKey: string) => {
    setEditingScenario(`${platformCode}_${scenarioKey}`)
  }

  const stopEditingScenario = async () => {
    // 完成编辑时自动保存当前配置
    if (editingScenario) {
      try {
        // 这里可以添加额外的保存逻辑，如果需要的话
        // 当前的配置已经通过updateScenarioService实时保存了
        toast.success('翻译场景配置已保存')
      } catch (error) {
        console.error('保存翻译场景配置失败:', error)
        toast.error('保存翻译场景配置失败')
      }
    }
    setEditingScenario(null)
  }

  const isScenarioEditing = (platformCode: string, scenarioKey: string) => {
    return editingScenario === `${platformCode}_${scenarioKey}`
  }



  // 翻译场景配置管理
  const updateScenarioService = async (platformCode: string, scenarioKey: string, contentType: string, provider: string, subService?: string) => {
    try {
      setSaving(true)
      const updatedScenarios = { ...scenarios }

      // 更新为新的嵌套结构
      updatedScenarios[platformCode].scenarios[scenarioKey].content_types[contentType] = {
        provider: provider,
        sub_service: subService || null
      }

      await updateSetting('translation_scenarios', updatedScenarios)
      setScenarios(updatedScenarios)
      toast.success('翻译场景配置更新成功')
    } catch (error) {
      console.error('更新翻译场景配置失败:', error)
      toast.error('更新翻译场景配置失败')
    } finally {
      setSaving(false)
    }
  }

  // 获取显示用的服务名称
  const getServiceDisplayName = (config: any) => {
    if (typeof config === 'string') {
      // 向后兼容旧格式
      return config
    }

    if (!config || !config.provider) {
      return 'mtran'
    }

    const { provider, sub_service } = config

    if (provider === 'mtran') {
      return 'MTran翻译模型'
    } else if (provider === 'deepseek_huoshan') {
      if (sub_service) {
        // 判断是单模型还是多模型
        const isOneModel = providers.deepseek_huoshan?.one_model &&
          Array.isArray(providers.deepseek_huoshan.one_model) &&
          providers.deepseek_huoshan.one_model.some((m: any) => m.models_name === sub_service)

        const modelType = isOneModel ? '单模型' : '多模型'
        return `火山引擎-[${modelType}] ${sub_service}`
      }
      return 'DeepSeek火山引擎'
    } else if (provider === 'with_openai') {
      return sub_service ? `OpenAI兼容-${sub_service}` : 'OpenAI兼容服务xxxx'
    }

    return provider
  }

  // 测试连接
  const handleTestConnection = async (providerType: 'deepseek_huoshan' | 'with_openai') => {
    setTestingConnection(true)
    setConnectionResult(null)

    try {
      let params
      let modelName
      if (providerType === 'deepseek_huoshan') {
        const formData = modelForm.getValues()
        params = {
          baseUrl: 'https://ark.cn-beijing.volces.com/api/v3',
          apiKey: formData.apikey,
          modelId: formData.models_id,
          timeout: 30000
        }
        modelName = formData.models_name || formData.models_id
      } else {
        const formData = providerForm.getValues()
        params = {
          baseUrl: formData.baseUrl,
          apiKey: formData.apikey,
          modelId: formData.models_id,
          timeout: 30000
        }
        modelName = formData.models_name || formData.models_id
      }

      const result = await testProviderConnection(providerType, params, modelName)
      setConnectionResult(result)

      if (result.success) {
        toast.success(result.message)
      } else {
        toast.error(result.message)
      }
    } catch (error: any) {
      const errorResult: ConnectionTestResult = {
        success: false,
        message: `测试失败: ${error.message}`
      }
      setConnectionResult(errorResult)
      toast.error(errorResult.message)
    } finally {
      setTestingConnection(false)
    }
  }

  // 处理标签页切换 - 每次切换都重新获取数据
  const handleTabChange = (value: string) => {
    setActiveTab(value)
    if (value === 'providers') {
      loadProviders()
    } else if (value === 'scenarios') {
      loadScenarios()
    }
  }

  // 初始化加载翻译服务提供商配置（默认显示providers标签页）
  useEffect(() => {
    loadProviders()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>加载翻译配置中...</span>
      </div>
    )
  }

  if (!providers) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <p className="text-muted-foreground mb-4">暂无翻译配置数据</p>
          <Button onClick={loadProviders}>
            重新加载
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <Tabs value={activeTab} onValueChange={handleTabChange} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="providers" className="flex items-center">
            <Settings className="h-4 w-4 mr-2" />
            翻译服务提供商
          </TabsTrigger>
          <TabsTrigger value="scenarios" className="flex items-center">
            <BarChart3 className="h-4 w-4 mr-2" />
            翻译场景配置
          </TabsTrigger>
        </TabsList>

        {/* 翻译服务提供商配置 */}
        <TabsContent value="providers" className="space-y-6">
          {/* 全局开关 */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-lg flex items-center">
                <Globe className="h-5 w-5 mr-2" />
                全局翻译开关
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <p className="font-medium">启用翻译服务</p>
                  <p className="text-sm text-muted-foreground">
                    关闭后所有翻译功能将不可用
                  </p>
                </div>
                <Switch
                  checked={providers.globally_enabled}
                  onCheckedChange={updateGloballyEnabled}
                  disabled={saving}
                />
              </div>
            </CardContent>
          </Card>
          {/* MTran 翻译服务 */}
          <Card>
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg flex items-center">
                    <Globe className="h-5 w-5 mr-2" />
                    MTran翻译服务
                  </CardTitle>
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={openMtranDialog}
                    disabled={saving}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Badge variant={providers.mtran?.enabled ? "default" : "secondary"}>
                    {providers.mtran?.enabled ? "已启用" : "已禁用"}
                  </Badge>
                  <Switch
                    checked={providers.mtran?.enabled || false}
                    onCheckedChange={(enabled) => updateProviderStatus('mtran', enabled)}
                    disabled={saving}
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">服务地址</p>
                  <p className="font-mono">{providers.mtran?.baseUrl}</p>
                </div>
                <div>
                  <p className="text-muted-foreground">超时时间</p>
                  <p>{providers.mtran?.timeout}ms</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* DeepSeek 翻译服务 */}
          <Card>
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg flex items-center">
                    <Globe className="h-5 w-5 mr-2" />
                    DeepSeek火山引擎
                  </CardTitle>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant={providers.deepseek_huoshan?.enabled ? "default" : "secondary"}>
                    {providers.deepseek_huoshan?.enabled ? "已启用" : "已禁用"}
                  </Badge>
                  <Switch
                    checked={providers.deepseek_huoshan?.enabled || false}
                    onCheckedChange={(enabled) => updateProviderStatus('deepseek_huoshan', enabled)}
                    disabled={saving}
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">服务地址</p>
                    <p className="font-mono">{providers.deepseek_huoshan?.baseUrl}</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">超时时间</p>
                    <p>{providers.deepseek_huoshan?.timeout}ms</p>
                  </div>
                </div>

                {/* 多模型配置 */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <p className="text-sm font-medium">多模型配置</p>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => openModelDialog(null, true)}
                      disabled={saving}
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      添加模型
                    </Button>
                  </div>
                  <div className="grid grid-cols-1 gap-2">
                    {providers.deepseek_huoshan?.mult_models?.map((model: any, index: number) => (
                      <div key={index} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <p className="font-medium">{model.models_name}</p>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline">{model.models_id}</Badge>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => openModelDialog(model, true)}
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => deleteModel(model.models_id, true, model.models_name)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                        <div className="grid grid-cols-3 gap-2 text-xs text-muted-foreground">
                          <div>最大Token: {model.max_token?.toLocaleString()}</div>
                          <div>今日已用: {model.today_used_token?.toLocaleString()}</div>
                          <div>价格: ¥{model.input_price}/¥{model.output_price}</div>
                        </div>
                      </div>
                    )) || (
                      <div className="text-center py-4 text-muted-foreground">
                        暂无多模型配置
                      </div>
                    )}
                  </div>
                </div>

                {/* 单模型配置 */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <p className="text-sm font-medium">单模型配置</p>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => openModelDialog(null, false)}
                      disabled={saving}
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      添加模型
                    </Button>
                  </div>
                  <div className="grid grid-cols-1 gap-2">
                    {providers.deepseek_huoshan?.one_model && Array.isArray(providers.deepseek_huoshan.one_model) && providers.deepseek_huoshan.one_model.length > 0 ? (
                      providers.deepseek_huoshan.one_model.map((model: any, index: number) => (
                        <div key={index} className="p-3 border rounded-lg">
                          <div className="flex items-center justify-between mb-2">
                            <p className="font-medium">{model.models_name}</p>
                            <div className="flex items-center space-x-2">
                              <Badge variant="outline">{model.models_id}</Badge>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => openModelDialog(model, false)}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button
                                size="sm"
                                variant="ghost"
                                onClick={() => deleteModel(model.models_id, false, model.models_name)}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                          <div className="grid grid-cols-3 gap-2 text-xs text-muted-foreground">
                            <div>最大Token: {model.max_token?.toLocaleString()}</div>
                            <div>今日已用: {model.today_used_token?.toLocaleString()}</div>
                            <div>价格: ¥{model.input_price}/¥{model.output_price}</div>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="text-center py-4 text-muted-foreground">
                        暂无单模型配置
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* OpenAI兼容服务 */}
          <Card>
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-lg flex items-center">
                    <Globe className="h-5 w-5 mr-2" />
                    OpenAI兼容服务
                  </CardTitle>
                </div>
                <div className="flex items-center space-x-2">
                  <Badge variant={providers.with_openai?.enabled ? "default" : "secondary"}>
                    {providers.with_openai?.enabled ? "已启用" : "已禁用"}
                  </Badge>
                  <Switch
                    checked={providers.with_openai?.enabled || false}
                    onCheckedChange={(enabled) => updateProviderStatus('with_openai', enabled)}
                    disabled={saving}
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <p className="text-muted-foreground">超时时间</p>
                    <p>{providers.with_openai?.timeout}ms</p>
                  </div>
                  <div>
                    <p className="text-muted-foreground">提供商数量</p>
                    <p>{providers.with_openai?.providers?.length || 0}个</p>
                  </div>
                </div>

                {/* 提供商列表 */}
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <p className="text-sm font-medium">提供商配置</p>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => openProviderDialog()}
                      disabled={saving}
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      添加提供商
                    </Button>
                  </div>
                  <div className="grid grid-cols-1 gap-2">
                    {providers.with_openai?.providers?.map((provider: any, index: number) => (
                      <div key={index} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <p className="font-medium">{provider.models_name}</p>
                          <div className="flex items-center space-x-2">
                            <Badge variant="outline">{provider.models_id}</Badge>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => openProviderDialog(provider)}
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => deleteProvider(provider.models_id, provider.models_name)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                        <div className="grid grid-cols-3 gap-2 text-xs text-muted-foreground">
                          <div>服务地址: {provider.baseUrl}</div>
                          <div>最大Token: {provider.max_token?.toLocaleString() || 0}</div>
                          <div>今日已用: {provider.today_used_token?.toLocaleString() || 0}</div>
                        </div>
                      </div>
                    )) || (
                      <div className="text-center py-4 text-muted-foreground">
                        暂无提供商配置
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 翻译场景配置 */}
        <TabsContent value="scenarios" className="space-y-6">
          {!scenarios ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <p className="text-muted-foreground mb-4">暂无翻译场景配置数据</p>
                <Button onClick={loadScenarios}>
                  加载场景配置
                </Button>
              </div>
            </div>
          ) : (
            platformConfigs.map((platform) => {
            const platformScenarios = scenarios[platform.code]
            if (!platformScenarios) return null

            return (
              <Card key={platform.code}>
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg flex items-center">
                        <BarChart3 className="h-5 w-5 mr-2" />
                        {platform.name}
                      </CardTitle>
                      <CardDescription className="text-sm">
                        支持语言: {platform.languages.join(', ')}
                      </CardDescription>
                    </div>
                    <Badge variant={platformScenarios.enabled ? "default" : "secondary"}>
                      {platformScenarios.enabled ? "已启用" : "已禁用"}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {translationScenarios.map((scenario) => {
                      const scenarioConfig = platformScenarios.scenarios[scenario.key]
                      if (!scenarioConfig) return null

                      return (
                        <div key={scenario.key} className="border rounded-lg p-4">
                          <div className="flex items-center justify-between mb-3">
                            <div>
                              <h4 className="font-medium">{scenario.name}</h4>
                            </div>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                if (isScenarioEditing(platform.code, scenario.key)) {
                                  stopEditingScenario()
                                } else {
                                  startEditingScenario(platform.code, scenario.key)
                                }
                              }}
                            >
                              <Edit className="h-4 w-4 mr-1" />
                              {isScenarioEditing(platform.code, scenario.key) ? '完成' : '编辑'}
                            </Button>
                          </div>

                          <div className={`grid gap-4 ${getPlatformContentTypes(platform.code).length === 2 ? 'grid-cols-2' : 'grid-cols-3'}`}>
                            {getPlatformContentTypes(platform.code).map((contentType) => {
                              const currentConfig = scenarioConfig.content_types[contentType.key]
                              const currentProvider = typeof currentConfig === 'string' ? currentConfig : currentConfig?.provider || 'mtran'
                              const currentSubService = typeof currentConfig === 'object' ? currentConfig?.sub_service : null

                              return (
                                <div key={contentType.key} className="p-3 border rounded space-y-2">
                                  <p className="text-sm font-medium">{contentType.name}</p>

                                  {isScenarioEditing(platform.code, scenario.key) ? (
                                    // 编辑模式：显示选择器
                                    <>
                                      {/* 主服务选择 */}
                                      <Select
                                        value={currentProvider}
                                        onValueChange={(provider) => {
                                          if (provider === 'mtran') {
                                            updateScenarioService(platform.code, scenario.key, contentType.key, provider, undefined)
                                          } else {
                                            // 对于需要子服务的提供商，保持当前子服务或设置默认值
                                            let defaultSubService = null
                                            if (provider === 'deepseek_huoshan') {
                                              // 优先选择单模型中的第一个，如果没有则选择多模型中的第一个
                                              if (providers.deepseek_huoshan?.one_model && Array.isArray(providers.deepseek_huoshan.one_model) && providers.deepseek_huoshan.one_model.length > 0) {
                                                defaultSubService = providers.deepseek_huoshan.one_model[0].models_name
                                              } else if (providers.deepseek_huoshan?.mult_models && providers.deepseek_huoshan.mult_models.length > 0) {
                                                defaultSubService = providers.deepseek_huoshan.mult_models[0].models_name
                                              }
                                            } else if (provider === 'with_openai') {
                                              defaultSubService = providers.with_openai?.providers?.[0]?.models_name || null
                                            }
                                            updateScenarioService(platform.code, scenario.key, contentType.key, provider, defaultSubService)
                                          }
                                        }}
                                        disabled={saving}
                                      >
                                        <SelectTrigger className="h-8">
                                          <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                          <SelectItem value="mtran">MTran本地翻译</SelectItem>
                                          <SelectItem value="deepseek_huoshan">DeepSeek火山引擎</SelectItem>
                                          <SelectItem value="with_openai">OpenAI兼容服务</SelectItem>
                                        </SelectContent>
                                      </Select>

                                      {/* 子服务选择 */}
                                      {currentProvider === 'deepseek_huoshan' && (
                                        <Select
                                          value={currentSubService || ''}
                                          onValueChange={(subService) => {
                                            updateScenarioService(platform.code, scenario.key, contentType.key, currentProvider, subService)
                                          }}
                                          disabled={saving}
                                        >
                                          <SelectTrigger className="h-7 text-xs">
                                            <SelectValue />
                                          </SelectTrigger>
                                          <SelectContent>
                                            {/* 单模型配置选项 */}
                                            {providers.deepseek_huoshan?.one_model && Array.isArray(providers.deepseek_huoshan.one_model) &&
                                              providers.deepseek_huoshan.one_model.map((model: any) => (
                                                <SelectItem key={model.models_name} value={model.models_name}>
                                                  [单模型]-{model.models_name}
                                                </SelectItem>
                                              ))
                                            }
                                            {/* 多模型配置选项 */}
                                            {providers.deepseek_huoshan?.mult_models?.map((model: any) => (
                                              <SelectItem key={model.models_name} value={model.models_name}>
                                                [多模型]-{model.models_name}
                                              </SelectItem>
                                            ))}
                                          </SelectContent>
                                        </Select>
                                      )}

                                      {currentProvider === 'with_openai' && (
                                        <Select
                                          value={currentSubService || ''}
                                          onValueChange={(subService) => {
                                            updateScenarioService(platform.code, scenario.key, contentType.key, currentProvider, subService)
                                          }}
                                          disabled={saving}
                                        >
                                          <SelectTrigger className="h-7 text-xs">
                                            <SelectValue placeholder="选择提供商" />
                                          </SelectTrigger>
                                          <SelectContent>
                                            {providers.with_openai?.providers?.map((provider: any) => (
                                              <SelectItem key={provider.models_name} value={provider.models_name}>
                                                {provider.models_name}
                                              </SelectItem>
                                            ))}
                                          </SelectContent>
                                        </Select>
                                      )}
                                    </>
                                  ) : (
                                    // 显示模式：只显示当前配置
                                    <div className="text-sm text-muted-foreground">
                                      {getServiceDisplayName(currentConfig)}
                                    </div>
                                  )}
                                </div>
                              )
                            })}
                          </div>
                        </div>
                      )
                    })}
                  </div>
                </CardContent>
              </Card>
            )
          }))}
        </TabsContent>
      </Tabs>

      {/* DeepSeek模型编辑对话框 */}
      <Dialog open={isModelDialogOpen} onOpenChange={setIsModelDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {editingModel?.isNew ? '添加' : '编辑'}{editingModel?.isMultiModel ? '多' : '单'}模型配置
            </DialogTitle>
            <DialogDescription>
              配置DeepSeek火山引擎的{editingModel?.isMultiModel ? '多' : '单'}模型参数
            </DialogDescription>
          </DialogHeader>
          <Form {...modelForm}>
            <form onSubmit={modelForm.handleSubmit(saveModel)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={modelForm.control}
                  name="models_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>模型名称</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入模型名称" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={modelForm.control}
                  name="models_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>模型ID</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入模型ID" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={modelForm.control}
                name="apikey"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API密钥</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="请输入API密钥" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-3 gap-4">
                <FormField
                  control={modelForm.control}
                  name="max_token"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>最大Token</FormLabel>
                      <FormControl>
                        <Input type="number" placeholder="0" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={modelForm.control}
                  name="input_price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>输入价格</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.001" placeholder="0.000" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={modelForm.control}
                  name="output_price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>输出价格</FormLabel>
                      <FormControl>
                        <Input type="number" step="0.001" placeholder="0.000" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* 连接测试结果 */}
              {connectionResult && (
                <div className={`p-3 rounded-md text-sm ${
                  connectionResult.success
                    ? 'bg-green-50 text-green-700 border border-green-200'
                    : 'bg-red-50 text-red-700 border border-red-200'
                }`}>
                  <div className="flex items-center">
                    <div className={`w-2 h-2 rounded-full mr-2 ${
                      connectionResult.success ? 'bg-green-500' : 'bg-red-500'
                    }`} />
                    {connectionResult.message}
                  </div>
                  {connectionResult.responseTime && (
                    <div className="text-xs mt-1 opacity-75">
                      响应时间: {connectionResult.responseTime}ms
                    </div>
                  )}
                </div>
              )}

              <DialogFooter className="flex justify-between">
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => handleTestConnection('deepseek_huoshan')}
                  disabled={testingConnection || saving}
                >
                  {testingConnection && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {!testingConnection && <Wifi className="mr-2 h-4 w-4" />}
                  测试连接
                </Button>
                <div className="flex space-x-2">
                  <Button type="button" variant="outline" onClick={() => setIsModelDialogOpen(false)}>
                    取消
                  </Button>
                  <Button
                    type="submit"
                    disabled={saving || (connectionResult !== null && !connectionResult.success)}
                  >
                    {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    保存
                  </Button>
                </div>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* OpenAI提供商编辑对话框 */}
      <Dialog open={isProviderDialogOpen} onOpenChange={setIsProviderDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingProvider?.isNew ? '添加' : '编辑'}OpenAI兼容提供商
            </DialogTitle>
            <DialogDescription>
              配置OpenAI兼容服务的提供商参数和提示词模板
            </DialogDescription>
          </DialogHeader>
          <Form {...providerForm}>
            <form onSubmit={providerForm.handleSubmit(saveProvider)} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={providerForm.control}
                  name="models_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>提供商名称</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入提供商名称" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={providerForm.control}
                  name="models_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>模型ID</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入模型ID" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={providerForm.control}
                name="baseUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>服务地址</FormLabel>
                    <FormControl>
                      <Input placeholder="https://api.example.com/v1" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={providerForm.control}
                name="apikey"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>API密钥</FormLabel>
                    <FormControl>
                      <Input type="password" placeholder="请输入API密钥" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={providerForm.control}
                name="max_token"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>最大Token（可选）</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder="0" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* 连接测试结果 */}
              {connectionResult && (
                <div className={`p-3 rounded-md text-sm ${
                  connectionResult.success
                    ? 'bg-green-50 text-green-700 border border-green-200'
                    : 'bg-red-50 text-red-700 border border-red-200'
                }`}>
                  <div className="flex items-center">
                    <div className={`w-2 h-2 rounded-full mr-2 ${
                      connectionResult.success ? 'bg-green-500' : 'bg-red-500'
                    }`} />
                    {connectionResult.message}
                  </div>
                  {connectionResult.responseTime && (
                    <div className="text-xs mt-1 opacity-75">
                      响应时间: {connectionResult.responseTime}ms
                    </div>
                  )}
                </div>
              )}

              <DialogFooter className="flex justify-between">
                <Button
                  type="button"
                  variant="secondary"
                  onClick={() => handleTestConnection('with_openai')}
                  disabled={testingConnection || saving}
                >
                  {testingConnection && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  {!testingConnection && <Wifi className="mr-2 h-4 w-4" />}
                  测试连接
                </Button>
                <div className="flex space-x-2">
                  <Button type="button" variant="outline" onClick={() => setIsProviderDialogOpen(false)}>
                    取消
                  </Button>
                  <Button
                    type="submit"
                    disabled={saving || (connectionResult !== null && !connectionResult.success)}
                  >
                    {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                    保存
                  </Button>
                </div>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* MTran配置编辑对话框 */}
      <Dialog open={isMtranDialogOpen} onOpenChange={setIsMtranDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>编辑MTran配置</DialogTitle>
            <DialogDescription>
              修改MTran翻译服务的基础配置参数
            </DialogDescription>
          </DialogHeader>

          <Form {...mtranForm}>
            <form onSubmit={mtranForm.handleSubmit(saveMtranConfig)} className="space-y-4">
              <FormField
                control={mtranForm.control}
                name="baseUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>服务地址</FormLabel>
                    <FormControl>
                      <Input placeholder="http://*************:8989" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={mtranForm.control}
                name="timeout"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>超时时间（毫秒）</FormLabel>
                    <FormControl>
                      <Input type="number" placeholder="30000" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsMtranDialogOpen(false)}>
                  取消
                </Button>
                <Button type="submit" disabled={saving}>
                  {saving && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                  保存
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </div>
  )
}
