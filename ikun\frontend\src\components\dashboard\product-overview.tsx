'use client'

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { formatDate, getStatusColor, getStatusText } from '@/lib/utils'

// 模拟数据
const recentProducts = [
  {
    id: '1',
    sku: 'SKU001',
    title: '无线蓝牙耳机 - 高音质降噪',
    status: 'active',
    price: 299.99,
    createdAt: '2024-01-15T10:30:00Z',
  },
  {
    id: '2',
    sku: 'SKU002',
    title: '智能手表 - 健康监测',
    status: 'pending',
    price: 899.99,
    createdAt: '2024-01-14T15:20:00Z',
  },
  {
    id: '3',
    sku: 'SKU003',
    title: '便携式充电宝 - 20000mAh',
    status: 'active',
    price: 159.99,
    createdAt: '2024-01-13T09:15:00Z',
  },
  {
    id: '4',
    sku: 'SKU004',
    title: '无线充电器 - 快充版',
    status: 'draft',
    price: 89.99,
    createdAt: '2024-01-12T14:45:00Z',
  },
  {
    id: '5',
    sku: 'SKU005',
    title: '蓝牙音箱 - 防水便携',
    status: 'active',
    price: 199.99,
    createdAt: '2024-01-11T11:30:00Z',
  },
]

export function ProductOverview() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>最新产品</CardTitle>
        <CardDescription>
          最近添加的产品列表
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recentProducts.map((product) => (
            <div
              key={product.id}
              className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors"
            >
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <p className="text-sm font-medium truncate">
                    {product.title}
                  </p>
                  <Badge 
                    variant="outline" 
                    className={getStatusColor(product.status)}
                  >
                    {getStatusText(product.status)}
                  </Badge>
                </div>
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <span>SKU: {product.sku}</span>
                  <span>¥{product.price}</span>
                  <span>{formatDate(product.createdAt)}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
