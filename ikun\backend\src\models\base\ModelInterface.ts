/**
 * Model Interface Definitions
 * 定义模型的接口和关联关系
 */

import { QueryOptions, PaginatedResult } from './BaseModel';

/**
 * 基础模型接口
 */
export interface IBaseModel<T> {
  findById(id: number | string): Promise<T | null>;
  findByIdOrFail(id: number | string): Promise<T>;
  findOne(where: Record<string, any>): Promise<T | null>;
  findMany(options?: QueryOptions): Promise<T[]>;
  paginate(options?: QueryOptions): Promise<PaginatedResult<T>>;
  create(data: Partial<T>): Promise<T>;
  update(id: number | string, data: Partial<T>): Promise<T>;
  delete(id: number | string): Promise<void>;
  exists(id: number | string): Promise<boolean>;
  count(where?: Record<string, any>): Promise<number>;
}

/**
 * 关联关系类型
 */
export enum RelationType {
  HAS_ONE = 'hasOne',
  HAS_MANY = 'hasMany',
  BELONGS_TO = 'belongsTo',
  BELONGS_TO_MANY = 'belongsToMany'
}

/**
 * 关联关系配置
 */
export interface RelationConfig {
  type: RelationType;
  model: string;
  foreignKey: string;
  localKey?: string;
  pivotTable?: string;
  pivotForeignKey?: string;
  pivotRelatedKey?: string;
}

/**
 * 关联查询选项
 */
export interface WithOptions {
  relations: string[];
  constraints?: Record<string, (query: any) => void>;
}

/**
 * 支持关联查询的模型接口
 */
export interface IRelationalModel<T> extends IBaseModel<T> {
  with(relations: string[]): Promise<T[]>;
  findWithRelations(id: number | string, relations: string[]): Promise<T | null>;
  getRelation(relationName: string): RelationConfig | undefined;
}

/**
 * 软删除模型接口
 */
export interface ISoftDeleteModel<T> extends IBaseModel<T> {
  softDelete(id: number | string): Promise<void>;
  restore(id: number | string): Promise<T>;
  forceDelete(id: number | string): Promise<void>;
  withTrashed(): this;
  onlyTrashed(): this;
}

/**
 * 树形结构模型接口
 */
export interface ITreeModel<T> extends IBaseModel<T> {
  getChildren(parentId: number | string): Promise<T[]>;
  getParent(id: number | string): Promise<T | null>;
  getAncestors(id: number | string): Promise<T[]>;
  getDescendants(id: number | string): Promise<T[]>;
  buildTree(items?: T[]): Promise<any[]>;
  getLevel(id: number | string): Promise<number>;
}

/**
 * 可排序模型接口
 */
export interface ISortableModel<T> extends IBaseModel<T> {
  reorder(items: Array<{ id: number | string; order: number }>): Promise<void>;
  moveUp(id: number | string): Promise<T>;
  moveDown(id: number | string): Promise<T>;
  moveTo(id: number | string, position: number): Promise<T>;
}

/**
 * 可搜索模型接口
 */
export interface ISearchableModel<T> extends IBaseModel<T> {
  search(query: string, options?: SearchOptions): Promise<PaginatedResult<T>>;
  getSearchableFields(): string[];
}

export interface SearchOptions extends QueryOptions {
  fields?: string[];
  exact?: boolean;
  fuzzy?: boolean;
}

/**
 * 缓存模型接口
 */
export interface ICacheableModel<T> extends IBaseModel<T> {
  remember(key: string, ttl: number, callback: () => Promise<T>): Promise<T>;
  forget(key: string): Promise<void>;
  flush(): Promise<void>;
}

/**
 * 审计模型接口
 */
export interface IAuditableModel<T> extends IBaseModel<T> {
  getAuditLog(id: number | string): Promise<AuditLog[]>;
  createAuditLog(id: number | string, action: string, changes: any): Promise<void>;
}

export interface AuditLog {
  id: number;
  model_type: string;
  model_id: number;
  action: string;
  changes: any;
  user_id?: number;
  created_at: Date;
}

/**
 * 批量操作接口
 */
export interface IBatchOperations<T> {
  batchCreate(items: Partial<T>[]): Promise<T[]>;
  batchUpdate(updates: Array<{ id: number | string; data: Partial<T> }>): Promise<T[]>;
  batchDelete(ids: Array<number | string>): Promise<void>;
}

/**
 * 数据验证接口
 */
export interface IValidatable<T> {
  validate(data: Partial<T>, rules?: ValidationRules): Promise<ValidationResult>;
  getValidationRules(): ValidationRules;
}

export interface ValidationRules {
  [field: string]: string | string[] | ValidationRule;
}

export interface ValidationRule {
  rule: string;
  message?: string;
  params?: any[];
}

export interface ValidationResult {
  valid: boolean;
  errors: ValidationError[];
}

export interface ValidationError {
  field: string;
  message: string;
  value?: any;
}

/**
 * 事件模型接口
 */
export interface IEventModel<T> extends IBaseModel<T> {
  on(event: string, callback: (data: T) => void): void;
  emit(event: string, data: T): void;
  beforeCreate(callback: (data: Partial<T>) => Partial<T>): void;
  afterCreate(callback: (data: T) => void): void;
  beforeUpdate(callback: (id: number | string, data: Partial<T>) => Partial<T>): void;
  afterUpdate(callback: (data: T) => void): void;
  beforeDelete(callback: (id: number | string) => void): void;
  afterDelete(callback: (id: number | string) => void): void;
}

// All interfaces and enums are already exported above
