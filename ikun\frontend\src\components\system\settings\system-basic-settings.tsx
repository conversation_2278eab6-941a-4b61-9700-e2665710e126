'use client'

import { useState, useEffect, useRef } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'

import { Loader2, Save, RotateCcw } from 'lucide-react'
import { useSystemSettings, SystemBasicConfig } from '@/hooks/useSystemSettings'
import { toast } from 'sonner'

// 表单验证模式
const systemBasicSchema = z.object({
  company_name: z.string().min(1, '公司名称不能为空').max(200, '公司名称不能超过200字符'),
  system_title: z.string().min(1, '系统标题不能为空').max(200, '系统标题不能超过200字符'),
  proxy_host: z.string().max(255, '代理地址不能超过255字符').optional(),
  proxy_port: z.string().max(10, '代理端口不能超过10字符').optional(),
  proxy_username: z.string().max(100, '代理账号不能超过100字符').optional(),
  proxy_password: z.string().max(100, '代理密码不能超过100字符').optional()
})

type SystemBasicFormData = z.infer<typeof systemBasicSchema>

export function SystemBasicSettings() {
  const { fetchSystemBasicConfig, updateSystemBasicConfig } = useSystemSettings()
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [hasLoaded, setHasLoaded] = useState(false)

  const form = useForm<SystemBasicFormData>({
    resolver: zodResolver(systemBasicSchema),
    defaultValues: {
      company_name: '',
      system_title: '',
      proxy_host: '',
      proxy_port: '',
      proxy_username: '',
      proxy_password: ''
    }
  })

  // 加载系统基础配置
  const loadConfig = async () => {
    if (hasLoaded || loading) {
      return
    }

    try {
      setLoading(true)
      const config = await fetchSystemBasicConfig()

      // 更新表单数据
      form.reset({
        company_name: config.company_name || '',
        system_title: config.system_title || '',
        proxy_host: config.proxy_host || '',
        proxy_port: config.proxy_port || '',
        proxy_username: config.proxy_username || '',
        proxy_password: config.proxy_password || ''
      })
      setHasLoaded(true)
    } catch (error) {
      console.error('加载系统配置失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 保存配置
  const onSubmit = async (data: SystemBasicFormData) => {
    try {
      setSaving(true)
      await updateSystemBasicConfig(data)
      toast.success('系统配置保存成功')
    } catch (error) {
      console.error('保存系统配置失败:', error)
    } finally {
      setSaving(false)
    }
  }

  // 重置表单
  const handleReset = () => {
    form.reset()
    toast.info('表单已重置')
  }

  // 初始化加载配置
  useEffect(() => {
    loadConfig()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>加载系统配置中...</span>
      </div>
    )
  }

  return (
    <div className="max-w-4xl mx-auto space-y-5">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
          {/* 基础信息 */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-lg">基础信息</CardTitle>
              <CardDescription className="text-sm">
                系统基本信息配置
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="company_name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">公司名称</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入公司名称" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="system_title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">系统标题</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入系统标题" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* 代理设置 */}
          <Card>
            <CardHeader className="pb-4">
              <CardTitle className="text-lg">代理设置</CardTitle>
              <CardDescription className="text-sm">
                网络代理服务器相关配置
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="proxy_host"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">代理地址</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入代理服务器地址" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="proxy_port"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">代理端口</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入代理端口" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="proxy_username"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">代理账号</FormLabel>
                      <FormControl>
                        <Input placeholder="请输入代理账号" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="proxy_password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium">代理密码</FormLabel>
                      <FormControl>
                        <Input type="password" placeholder="请输入代理密码" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </CardContent>
          </Card>

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-3 pt-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleReset}
              disabled={saving}
              size="sm"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              重置
            </Button>
            <Button type="submit" disabled={saving} size="sm">
              {saving ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              保存配置
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}
