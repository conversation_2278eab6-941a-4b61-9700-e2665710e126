/**
 * 翻译提供商基类
 */

import { logger } from '@/utils/logger';
import {
  ITranslationProvider,
  TranslationProviderConfig,
  TranslationRequest,
  TranslationResponse,
  BatchTranslationRequest,
  BatchTranslationResponse,
  LanguageCode
} from '../types';

export abstract class BaseTranslationProvider implements ITranslationProvider {
  public readonly name: string;
  public readonly config: TranslationProviderConfig;

  constructor(name: string, config: TranslationProviderConfig) {
    this.name = name;
    this.config = config;
  }

  /**
   * 抽象方法：单文本翻译
   */
  abstract translateText(request: TranslationRequest): Promise<TranslationResponse>;

  /**
   * 批量翻译（默认实现：逐个翻译）
   */
  async translateBatch(request: BatchTranslationRequest): Promise<BatchTranslationResponse> {
    const translations: Record<string, string> = {};
    const errors: Record<string, string> = {};
    let hasSuccess = false;

    // 并行翻译所有目标语言
    const promises = request.targetLangs.map(async (targetLang) => {
      try {
        const result = await this.translateText({
          text: request.text,
          sourceLang: request.sourceLang,
          targetLang,
          contentType: request.contentType
        });

        if (result.success && result.text) {
          translations[targetLang.toUpperCase()] = result.text;
          hasSuccess = true;
        } else {
          errors[targetLang.toUpperCase()] = result.error || '翻译失败';
        }
      } catch (error) {
        errors[targetLang.toUpperCase()] = error instanceof Error ? error.message : '翻译异常';
      }
    });

    await Promise.all(promises);

    return {
      success: hasSuccess,
      translations: Object.keys(translations).length > 0 ? translations : undefined,
      errors: Object.keys(errors).length > 0 ? errors : undefined
    };
  }

  /**
   * 健康检查（默认实现）
   */
  async healthCheck(): Promise<boolean> {
    try {
      // 简单的翻译测试
      const testRequest: TranslationRequest = {
        text: 'Hello',
        sourceLang: 'en',
        targetLang: 'zh',
        contentType: 'title'
      };

      const result = await this.translateText(testRequest);
      return result.success;
    } catch (error) {
      logger.error(`${this.name} health check failed:`, error);
      return false;
    }
  }

  /**
   * 重试机制
   */
  protected async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = this.config.retryCount
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        if (attempt === maxRetries) {
          break;
        }

        // 等待重试间隔
        await this.sleep(this.config.retryInterval * 1000);
        logger.warn(`${this.name} attempt ${attempt} failed, retrying...`, error);
      }
    }

    throw lastError!;
  }

  /**
   * 延迟函数
   */
  protected sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 验证语言代码
   */
  protected validateLanguageCode(lang: LanguageCode): boolean {
    const supportedLangs: LanguageCode[] = ['en', 'zh', 'lt', 'lv', 'et', 'fi', 'pt', 'es'];
    return supportedLangs.includes(lang);
  }

  /**
   * 标准化语言代码
   */
  protected normalizeLanguageCode(lang: LanguageCode): string {
    return lang.toLowerCase();
  }

  /**
   * 验证文本内容
   */
  protected validateText(text: string): boolean {
    return Boolean(text && text.trim().length > 0);
  }

  /**
   * 清理翻译结果
   */
  protected cleanTranslationResult(text: string): string {
    if (!text) return '';
    
    // 移除可能的异常标记
    if (text === '[object Object]' || text === 'undefined' || text === 'null') {
      return '';
    }

    return text.trim();
  }
}
