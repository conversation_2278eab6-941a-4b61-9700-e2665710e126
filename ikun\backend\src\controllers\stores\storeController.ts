/**
 * Store Controller
 * 店铺管理控制器
 */

import { Request, Response, NextFunction } from 'express';
import { storeService } from '@/services/stores/storeService';
import { logger } from '@/utils/logger';
import { getBeijingTimeISO } from '@/utils/time';
import { PaginationQuery } from '@/types';

class StoreController {
  // GET /api/v1/stores - Get stores list
  public async getStores(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const query = req.query as PaginationQuery & {
        platform_code?: string;
        status?: 'active' | 'failed';
        search?: string;
      };

      const result = await storeService.getStores(query);

      res.status(200).json({
        code: 200,
        message: 'Stores retrieved successfully',
        data: result,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // GET /api/v1/stores/:id - Get store details
  public async getStoreById(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const id = parseInt(req.params.id!);
      const store = await storeService.getStoreById(id);

      res.status(200).json({
        code: 200,
        message: 'Store retrieved successfully',
        data: store,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // POST /api/v1/stores - Create store
  public async createStore(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const storeData = req.body;
      const store = await storeService.createStore(storeData);

      logger.info('Store created:', { storeId: store.id, platformCode: store.platform_code });

      res.status(201).json({
        code: 201,
        message: 'Store created successfully',
        data: store,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // PUT /api/v1/stores/:id - Update store
  public async updateStore(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const id = parseInt(req.params.id!);
      const updateData = req.body;
      const store = await storeService.updateStore(id, updateData);

      logger.info('Store updated:', { storeId: id });

      res.status(200).json({
        code: 200,
        message: 'Store updated successfully',
        data: store,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // DELETE /api/v1/stores/:id - Delete store
  public async deleteStore(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const id = parseInt(req.params.id!);
      await storeService.deleteStore(id);

      logger.info('Store deleted:', { storeId: id });

      res.status(200).json({
        code: 200,
        message: 'Store deleted successfully',
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // PUT /api/v1/stores/:id/status - Update store status
  public async updateStoreStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const id = parseInt(req.params.id!);
      const { status } = req.body;
      const store = await storeService.updateStoreStatus(id, status);

      logger.info('Store status updated:', { storeId: id, status });

      res.status(200).json({
        code: 200,
        message: 'Store status updated successfully',
        data: store,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // POST /api/v1/stores/:id/sync - Sync store data
  public async syncStore(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const id = parseInt(req.params.id!);
      const result = await storeService.syncStore(id);

      logger.info('Store sync initiated:', { storeId: id });

      res.status(200).json({
        code: 200,
        message: 'Store sync initiated successfully',
        data: result,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }
}

export const storeController = new StoreController();
