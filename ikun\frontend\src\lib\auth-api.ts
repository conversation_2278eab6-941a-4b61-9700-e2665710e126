/**
 * 认证相关API
 */

import { apiClient } from './api';
import { LoginRequest, LoginResponse, RefreshTokenRequest, RefreshTokenResponse, User } from '@/types/auth';

export const authApi = {
  // 用户登录
  login: async (credentials: LoginRequest): Promise<LoginResponse> => {
    const response = await apiClient.post<LoginResponse>('/auth/login', credentials);
    return response;
  },

  // 用户登出
  logout: async (): Promise<void> => {
    await apiClient.post<null>('/auth/logout');
  },

  // 刷新token
  refreshToken: async (refreshTokenData: RefreshTokenRequest): Promise<RefreshTokenResponse> => {
    const response = await apiClient.post<RefreshTokenResponse>('/auth/refresh', refreshTokenData);
    return response;
  },

  // 获取用户信息
  getProfile: async (): Promise<User> => {
    const response = await apiClient.get<User>('/auth/profile');
    return response;
  }
};
