/**
 * Products Routes
 * Product management endpoints
 */

import { Router } from 'express';
import { productController } from '@/controllers/products/productController';
import { validateRequest } from '@/middleware/validation/validateRequest';
import { productValidation } from '@/validations/products/productValidation';

const router = Router();

// GET /api/v1/products - Get products list
router.get('/',
  validateRequest({ query: productValidation.getProducts }),
  productController.getProducts.bind(productController)
);

// POST /api/v1/products - Create product
router.post('/',
  validateRequest({ body: productValidation.createProduct }),
  productController.createProduct.bind(productController)
);

// GET /api/v1/products/template - Download import template (must be before /:id)
router.get('/template',
  productController.downloadTemplate.bind(productController)
);

// POST /api/v1/products/import - Import products from Excel
router.post('/import',
  productController.importFromExcel.bind(productController)
);

// POST /api/v1/products/batch-claim - Batch claim products
router.post('/batch-claim',
  validateRequest({ body: productValidation.batchClaim }),
  productController.batchClaimProducts.bind(productController)
);

// GET /api/v1/products/export - Export products to Excel
router.get('/export',
  validateRequest({ query: productValidation.exportProducts }),
  productController.exportToExcel.bind(productController)
);

// GET /api/v1/products/dropship - Get dropship products (alias for products)
router.get('/dropship',
  validateRequest({ query: productValidation.getProducts }),
  productController.getProducts.bind(productController)
);

// GET /api/v1/products/category/:categoryId - Get products by category
router.get('/category/:categoryId',
  validateRequest({
    params: productValidation.categoryId,
    query: productValidation.getProducts
  }),
  productController.getProductsByCategory.bind(productController)
);

// GET /api/v1/products/:id - Get product details (must be after specific routes)
router.get('/:id',
  validateRequest({ params: productValidation.productId }),
  productController.getProductById.bind(productController)
);

// PUT /api/v1/products/:id - Update product
router.put('/:id',
  validateRequest({
    params: productValidation.productId,
    body: productValidation.updateProduct
  }),
  productController.updateProduct.bind(productController)
);

// DELETE /api/v1/products/:id - Delete product
router.delete('/:id',
  validateRequest({ params: productValidation.productId }),
  productController.deleteProduct.bind(productController)
);

export default router;
