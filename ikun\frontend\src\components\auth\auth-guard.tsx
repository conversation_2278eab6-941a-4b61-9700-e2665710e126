'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'

interface AuthGuardProps {
  children: React.ReactNode
  requireAuth?: boolean
  requiredRole?: 'admin' | 'user' | 'viewer'
}

export function AuthGuard({ 
  children, 
  requireAuth = true, 
  requiredRole 
}: AuthGuardProps) {
  const { isAuthenticated, isLoading, user } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (isLoading) return

    // 需要认证但未登录
    if (requireAuth && !isAuthenticated) {
      router.push('/login')
      return
    }

    // 需要特定角色但权限不足
    if (requireAuth && isAuthenticated && requiredRole && user) {
      const roleHierarchy = {
        'viewer': 1,
        'user': 2,
        'admin': 3
      }
      
      const userLevel = roleHierarchy[user.role] || 0
      const requiredLevel = roleHierarchy[requiredRole] || 0
      
      if (userLevel < requiredLevel) {
        router.push('/unauthorized')
        return
      }
    }
  }, [isAuthenticated, isLoading, user, requireAuth, requiredRole, router])

  // 显示加载状态
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">加载中...</p>
        </div>
      </div>
    )
  }

  // 需要认证但未登录
  if (requireAuth && !isAuthenticated) {
    return null // 会被重定向到登录页
  }

  // 权限不足
  if (requireAuth && isAuthenticated && requiredRole && user) {
    const roleHierarchy = {
      'viewer': 1,
      'user': 2,
      'admin': 3
    }
    
    const userLevel = roleHierarchy[user.role] || 0
    const requiredLevel = roleHierarchy[requiredRole] || 0
    
    if (userLevel < requiredLevel) {
      return null // 会被重定向到未授权页面
    }
  }

  return <>{children}</>
}
