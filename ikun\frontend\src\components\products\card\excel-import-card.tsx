'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/hooks/use-toast'
import {
  Download,
  Upload,
  FileSpreadsheet,
  AlertCircle,
  CheckCircle,
  X,
  FileText
} from 'lucide-react'

interface ValidationResult {
  filename: string
  total: number
  valid: number
  errors: Array<{
    row: number
    column: string
    message: string
  }>
}

interface ExcelImportCardProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onImportComplete?: () => void
}

export function ExcelImportCard({
  open,
  onOpenChange,
  onImportComplete
}: ExcelImportCardProps) {
  const { toast } = useToast()
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [isValidating, setIsValidating] = useState(false)
  const [isImporting, setIsImporting] = useState(false)
  const [validationResults, setValidationResults] = useState<ValidationResult[]>([])
  const [showValidationDialog, setShowValidationDialog] = useState(false)

  // 格式化文件名显示
  const formatFileDisplayName = (filename: string, index: number): string => {
    // 如果是模板文件，显示为"铺货产品数据"
    if (filename.includes('template') || filename.includes('模板')) {
      return '铺货产品数据'
    }

    // 如果文件名包含中文，尝试提取有意义的部分
    if (/[\u4e00-\u9fa5]/.test(filename)) {
      // 移除时间戳和扩展名，保留中文部分
      const nameWithoutExt = filename.replace(/\.[^.]+$/, '')
      const cleanName = nameWithoutExt.replace(/_\d{4}-\d{2}-\d{2}.*$/, '')
      return cleanName || `文件 ${index + 1}`
    }

    // 默认显示为"文件 X"
    return `文件 ${index + 1}`
  }

  // 下载导入模板
  const handleDownloadTemplate = async () => {
    try {
      // 获取认证token
      const token = localStorage.getItem('auth_token')
      if (!token) {
        throw new Error('请先登录')
      }

      const response = await fetch('/api/v1/products/template', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('下载失败')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'dropship-products-template.xlsx'
      a.click()
      window.URL.revokeObjectURL(url)

      toast({
        title: "模板下载成功",
        variant: "default"
      })
    } catch (error) {
      toast({
        title: "模板下载失败",
        variant: "destructive"
      })
    }
  }

  // 处理文件选择
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || [])
    setSelectedFiles(files)
  }

  // 移除选中的文件
  const handleRemoveFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index))
  }

  // 验证文件数据
  const handleValidateFiles = async () => {
    if (selectedFiles.length === 0) {
      toast({
        title: "请选择文件",
        description: "请先选择要导入的Excel文件",
        variant: "destructive"
      })
      return
    }

    setIsValidating(true)
    try {
      const token = localStorage.getItem('auth_token')
      if (!token) {
        throw new Error('请先登录')
      }

      // 调用验证API（validate_only=true）
      const formData = new FormData()
      selectedFiles.forEach((file) => {
        formData.append(`files`, file)
      })
      formData.append('validate_only', 'true')

      const response = await fetch('/api/v1/products/import', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      })

      if (!response.ok) {
        throw new Error('验证失败')
      }

      const result = await response.json()
      const results = result.data || []

      // 转换为ValidationResult格式
      const validationResults: ValidationResult[] = results.map((r: any) => ({
        filename: r.filename,
        total: r.total,
        valid: r.total - r.errors.length,
        errors: r.errors.map((error: string, index: number) => ({
          row: index + 2, // 假设错误行号
          column: '未知',
          message: error
        }))
      }))

      setValidationResults(validationResults)
      setShowValidationDialog(true)
    } catch (error) {
      toast({
        title: "验证失败",
        description: "验证文件时发生错误，请重试",
        variant: "destructive"
      })
    } finally {
      setIsValidating(false)
    }
  }

  // 确认导入
  const handleConfirmImport = async () => {
    setIsImporting(true)
    setShowValidationDialog(false)

    try {
      const token = localStorage.getItem('auth_token')
      if (!token) {
        throw new Error('请先登录')
      }

      const formData = new FormData()
      selectedFiles.forEach((file) => {
        formData.append(`files`, file)
      })
      formData.append('validate_only', 'false')

      const response = await fetch('/api/v1/products/import', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      })

      if (!response.ok) {
        throw new Error('导入失败')
      }

      const result = await response.json()
      const results = result.data || []

      const successCount = results.filter((r: any) => r.success).length
      const totalFiles = results.length

      if (successCount === totalFiles) {
        toast({
          title: "导入成功",
          description: `已成功导入 ${totalFiles} 个文件`,
          variant: "default"
        })
      } else {
        toast({
          title: "部分导入成功",
          description: `${successCount}/${totalFiles} 个文件导入成功`,
          variant: "default"
        })
      }

      onImportComplete?.()
      handleClose()
    } catch (error) {
      toast({
        title: "导入失败",
        description: "导入文件时发生错误，请重试",
        variant: "destructive"
      })
    } finally {
      setIsImporting(false)
    }
  }

  // 导出产品数据
  const handleExport = async () => {
    try {
      // 获取认证token
      const token = localStorage.getItem('auth_token')
      if (!token) {
        throw new Error('请先登录')
      }

      const response = await fetch('/api/v1/products/export', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })

      if (!response.ok) {
        throw new Error('导出失败')
      }

      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `铺货产品数据_${new Date().toISOString().split('T')[0]}.xlsx`
      a.click()
      window.URL.revokeObjectURL(url)

      toast({
        title: "导出成功",
        description: "产品数据已导出到本地",
        variant: "default"
      })
    } catch (error) {
      toast({
        title: "导出失败",
        description: "导出数据时发生错误，请重试",
        variant: "destructive"
      })
    }
  }

  // 重置状态
  const handleClose = () => {
    setSelectedFiles([])
    setValidationResults([])
    setShowValidationDialog(false)
    onOpenChange(false)
  }

  return (
    <>
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileSpreadsheet className="w-5 h-5" />
              Excel 导入/导出
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* 操作按钮区域 */}
            <div className="flex gap-3">
              <Button onClick={handleDownloadTemplate} variant="outline" className="flex-1">
                <Download className="w-4 h-4 mr-2" />
                下载模板
              </Button>
              <Button onClick={handleExport} variant="outline" className="flex-1">
                <FileText className="w-4 h-4 mr-2" />
                导出产品
              </Button>
            </div>

            {/* 文件上传区域 */}
            <div className="space-y-4">
              <Label htmlFor="file-upload" className="text-base font-medium">
                选择文件上传
              </Label>

              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                <Input
                  id="file-upload"
                  type="file"
                  multiple
                  accept=".xlsx,.xls"
                  onChange={handleFileSelect}
                  className="hidden"
                />
                <Label
                  htmlFor="file-upload"
                  className="cursor-pointer flex flex-col items-center gap-2"
                >
                  <Upload className="w-8 h-8 text-gray-400" />
                  <span className="text-sm text-gray-600">
                    点击选择文件或拖拽文件到此处
                  </span>
                </Label>
              </div>

              {/* 已选择的文件列表 */}
              {selectedFiles.length > 0 && (
                <div className="space-y-2">
                  <Label className="text-sm font-medium">已选择的文件：</Label>
                  {selectedFiles.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center gap-2">
                        <FileSpreadsheet className="w-4 h-4 text-green-600" />
                        <span className="text-sm">{file.name}</span>
                        <span className="text-xs text-gray-500">
                          ({(file.size / 1024).toFixed(1)} KB)
                        </span>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveFile(index)}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}

              <Button
                onClick={handleValidateFiles}
                disabled={selectedFiles.length === 0 || isValidating}
                className="w-full"
              >
                {isValidating ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                    验证中...
                  </>
                ) : (
                  <>
                    <Upload className="w-4 h-4 mr-2" />
                    开始导入
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 验证结果对话框 */}
      <Dialog open={showValidationDialog} onOpenChange={setShowValidationDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-amber-500" />
              数据验证结果
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {validationResults.map((result, index) => (
              <div key={index} className="space-y-2">
                <div className="font-medium">{formatFileDisplayName(result.filename, index)}</div>
                <div className="text-sm text-muted-foreground">
                  总计 {result.total} 条数据，可导入 {result.valid} 条
                </div>
                {result.errors.length > 0 && (
                  <div className="text-sm text-red-600">
                    错误 {result.errors.length} 条：
                    <ul className="mt-1 list-disc list-inside">
                      {result.errors.slice(0, 3).map((error, i) => (
                        <li key={i}>第{error.row}行 {error.column}: {error.message}</li>
                      ))}
                      {result.errors.length > 3 && (
                        <li>还有 {result.errors.length - 3} 个错误...</li>
                      )}
                    </ul>
                  </div>
                )}
              </div>
            ))}
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => setShowValidationDialog(false)}
              className="flex-1"
            >
              取消
            </Button>
            <Button
              onClick={handleConfirmImport}
              disabled={isImporting}
              className="flex-1"
            >
              {isImporting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  导入中...
                </>
              ) : (
                '确定导入'
              )}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
