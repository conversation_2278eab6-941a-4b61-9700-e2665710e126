'use client'

import { useState, useEffect } from 'react'
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON>alogHeader,
  <PERSON>alogTitle,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { 
  Languages, 
  X, 
  Play, 
  CheckCircle,
  AlertCircle 
} from 'lucide-react'
import { toast } from 'sonner'
import { apiClient } from '@/lib/api'
import { 
  LanguageCode, 
  getLanguageName, 
  getTargetLanguagesText 
} from './TranslationButton'
import { 
  TranslationProgress, 
  LanguageTranslationStatus,
  TranslationTaskStatus 
} from './TranslationProgress'

// 翻译模态框属性
interface TranslationModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  title?: string
  initialText?: string
  sourceLang?: LanguageCode
  targetLangs?: LanguageCode[]
  contentType?: 'title' | 'description' | 'selling_point'
  platform: string // 必需的平台参数
  source?: string // 翻译来源场景
  onTranslationComplete?: (translations: Record<string, string>) => void
  onTranslationError?: (errors: Record<string, string>) => void
}

// 翻译API响应类型
interface TranslationResponse {
  success: boolean
  translations?: Record<string, string>
  errors?: Record<string, string>
}

export function TranslationModal({
  open,
  onOpenChange,
  title = '批量翻译',
  initialText = '',
  sourceLang = 'en',
  targetLangs = ['lt', 'lv', 'et', 'fi'],
  contentType = 'title',
  platform,
  source,
  onTranslationComplete,
  onTranslationError
}: TranslationModalProps) {
  const [text, setText] = useState(initialText)
  const [isTranslating, setIsTranslating] = useState(false)
  const [languageStatuses, setLanguageStatuses] = useState<LanguageTranslationStatus[]>([])
  const [translationResults, setTranslationResults] = useState<Record<string, string>>({})
  const [translationErrors, setTranslationErrors] = useState<Record<string, string>>({})

  // 重置状态
  const resetState = () => {
    setText(initialText)
    setIsTranslating(false)
    setLanguageStatuses([])
    setTranslationResults({})
    setTranslationErrors({})
  }

  // 当模态框打开时重置状态
  useEffect(() => {
    if (open) {
      resetState()
      setText(initialText)
    }
  }, [open, initialText])

  // 初始化语言状态
  useEffect(() => {
    if (targetLangs.length > 0) {
      const initialStatuses: LanguageTranslationStatus[] = targetLangs.map(lang => ({
        language: lang,
        status: 'pending' as TranslationTaskStatus
      }))
      setLanguageStatuses(initialStatuses)
    }
  }, [targetLangs])

  // 开始翻译
  const handleStartTranslation = async () => {
    if (!text.trim()) {
      toast.error('请输入要翻译的内容')
      return
    }

    setIsTranslating(true)
    setTranslationResults({})
    setTranslationErrors({})

    // 更新所有语言状态为运行中
    setLanguageStatuses(prev => 
      prev.map(status => ({ ...status, status: 'running' as TranslationTaskStatus }))
    )

    try {
      // 调用批量翻译API
      const response = await apiClient.post<TranslationResponse>('/translation/batch/forediting', {
        text: text.trim(),
        sourceLang,
        targetLangs,
        contentType,
        platform,
        source
      })

      if (response.success) {
        // 处理成功的翻译结果
        if (response.translations) {
          setTranslationResults(response.translations)
          
          // 更新语言状态
          setLanguageStatuses(prev => 
            prev.map(status => {
              const langKey = status.language.toUpperCase()
              if (response.translations && response.translations[langKey]) {
                return {
                  ...status,
                  status: 'completed' as TranslationTaskStatus,
                  result: response.translations[langKey]
                }
              }
              return status
            })
          )
        }

        // 处理失败的翻译
        if (response.errors) {
          setTranslationErrors(response.errors)
          
          // 更新失败的语言状态
          setLanguageStatuses(prev => 
            prev.map(status => {
              const langKey = status.language.toUpperCase()
              if (response.errors && response.errors[langKey]) {
                return {
                  ...status,
                  status: 'failed' as TranslationTaskStatus,
                  error: response.errors[langKey]
                }
              }
              return status
            })
          )
        }

        const successCount = response.translations ? Object.keys(response.translations).length : 0
        const errorCount = response.errors ? Object.keys(response.errors).length : 0

        if (successCount > 0) {
          toast.success(`翻译完成！成功 ${successCount} 个，失败 ${errorCount} 个`)
        } else {
          toast.error('翻译失败，请检查网络连接或稍后重试')
        }

      } else {
        // 全部失败
        setLanguageStatuses(prev => 
          prev.map(status => ({
            ...status,
            status: 'failed' as TranslationTaskStatus,
            error: '翻译服务异常'
          }))
        )
        
        toast.error('翻译失败，请稍后重试')
      }

    } catch (error) {
      console.error('Translation error:', error)
      
      // 更新所有语言状态为失败
      setLanguageStatuses(prev => 
        prev.map(status => ({
          ...status,
          status: 'failed' as TranslationTaskStatus,
          error: error instanceof Error ? error.message : '翻译服务异常'
        }))
      )
      
      toast.error('翻译服务异常，请稍后重试')
    } finally {
      setIsTranslating(false)
    }
  }

  // 应用翻译结果
  const handleApplyResults = () => {
    if (Object.keys(translationResults).length > 0) {
      onTranslationComplete?.(translationResults)
      toast.success('翻译结果已应用')
      onOpenChange(false)
    }
    
    if (Object.keys(translationErrors).length > 0) {
      onTranslationError?.(translationErrors)
    }
  }

  // 关闭模态框
  const handleClose = () => {
    if (!isTranslating) {
      onOpenChange(false)
    }
  }

  const hasResults = Object.keys(translationResults).length > 0
  const hasErrors = Object.keys(translationErrors).length > 0
  const isCompleted = languageStatuses.every(status => 
    status.status === 'completed' || status.status === 'failed'
  )

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Languages className="w-5 h-5" />
            {title}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 翻译配置 */}
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">源语言:</span> {getLanguageName(sourceLang)}
              </div>
              <div>
                <span className="font-medium">目标语言:</span> {getTargetLanguagesText(targetLangs)}
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="translation-text">翻译内容</Label>
              <Textarea
                id="translation-text"
                value={text}
                onChange={(e) => setText(e.target.value)}
                placeholder={`请输入要翻译的${contentType === 'title' ? '标题' : '描述'}...`}
                rows={4}
                disabled={isTranslating}
              />
            </div>
          </div>

          <Separator />

          {/* 翻译进度 */}
          {languageStatuses.length > 0 && (
            <TranslationProgress
              title="翻译进度"
              totalLanguages={targetLangs.length}
              languageStatuses={languageStatuses}
              onComplete={(results) => setTranslationResults(results)}
              onError={(errors) => setTranslationErrors(errors)}
            />
          )}

          {/* 翻译结果预览 */}
          {hasResults && (
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm font-medium text-green-700">
                <CheckCircle className="w-4 h-4" />
                翻译结果预览
              </div>
              <div className="space-y-2 max-h-40 overflow-y-auto">
                {Object.entries(translationResults).map(([langCode, translation]) => (
                  <div key={langCode} className="p-3 bg-green-50 border border-green-200 rounded">
                    <div className="text-xs font-medium text-green-700 mb-1">
                      {getLanguageName(langCode.toLowerCase() as LanguageCode)}
                    </div>
                    <div className="text-sm text-gray-900">{translation}</div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* 错误信息 */}
          {hasErrors && (
            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm font-medium text-red-700">
                <AlertCircle className="w-4 h-4" />
                翻译错误
              </div>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {Object.entries(translationErrors).map(([langCode, error]) => (
                  <div key={langCode} className="p-3 bg-red-50 border border-red-200 rounded">
                    <div className="text-xs font-medium text-red-700 mb-1">
                      {getLanguageName(langCode.toLowerCase() as LanguageCode)}
                    </div>
                    <div className="text-sm text-red-600">{error}</div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isTranslating}
          >
            {isCompleted ? '关闭' : '取消'}
          </Button>
          
          {!isTranslating && !isCompleted && (
            <Button
              onClick={handleStartTranslation}
              disabled={!text.trim()}
            >
              <Play className="w-4 h-4 mr-2" />
              开始翻译
            </Button>
          )}
          
          {isCompleted && hasResults && (
            <Button onClick={handleApplyResults}>
              <CheckCircle className="w-4 h-4 mr-2" />
              应用结果
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
