/**
 * 翻译API验证规则
 */

import Joi from 'joi';

// 支持的语言代码
const languageCodes = ['en', 'zh', 'lt', 'lv', 'et', 'fi', 'pt', 'es'];

// 支持的内容类型
const contentTypes = ['title', 'description', 'selling_point'];

// 支持的翻译来源场景
const translationSources = ['form_editing', 'form_batch', 'form_task'];

export const translationValidation = {
  /**
   * 单文本翻译验证
   */
  translateText: Joi.object({
    text: Joi.string()
      .required()
      .min(1)
      .max(10000)
      .messages({
        'string.empty': '翻译文本不能为空',
        'string.min': '翻译文本不能为空',
        'string.max': '翻译文本长度不能超过10000字符',
        'any.required': '翻译文本是必需的'
      }),

    sourceLang: Joi.string()
      .valid(...languageCodes)
      .required()
      .messages({
        'any.only': `源语言必须是以下之一: ${languageCodes.join(', ')}`,
        'any.required': '源语言是必需的'
      }),

    targetLang: Joi.string()
      .valid(...languageCodes)
      .required()
      .messages({
        'any.only': `目标语言必须是以下之一: ${languageCodes.join(', ')}`,
        'any.required': '目标语言是必需的'
      }),

    contentType: Joi.string()
      .valid(...contentTypes)
      .optional()
      .default('title')
      .messages({
        'any.only': `内容类型必须是以下之一: ${contentTypes.join(', ')}`
      }),

    provider: Joi.string()
      .optional()
      .messages({
        'string.base': '提供商名称必须是字符串'
      }),

    platform: Joi.string()
      .optional()
      .messages({
        'string.base': '平台代码必须是字符串'
      }),

    source: Joi.string()
      .valid(...translationSources)
      .optional()
      .messages({
        'any.only': `翻译来源必须是以下之一: ${translationSources.join(', ')}`,
        'string.base': '翻译来源必须是字符串'
      })
  }),

  /**
   * 批量翻译验证
   */
  translateBatch: Joi.object({
    text: Joi.string()
      .required()
      .min(1)
      .max(10000)
      .messages({
        'string.empty': '翻译文本不能为空',
        'string.min': '翻译文本不能为空',
        'string.max': '翻译文本长度不能超过10000字符',
        'any.required': '翻译文本是必需的'
      }),

    sourceLang: Joi.string()
      .valid(...languageCodes)
      .required()
      .messages({
        'any.only': `源语言必须是以下之一: ${languageCodes.join(', ')}`,
        'any.required': '源语言是必需的'
      }),

    targetLangs: Joi.array()
      .items(Joi.string().valid(...languageCodes))
      .min(1)
      .max(10)
      .required()
      .messages({
        'array.min': '至少需要一个目标语言',
        'array.max': '目标语言不能超过10个',
        'any.required': '目标语言列表是必需的',
        'array.includesRequiredUnknowns': `目标语言必须是以下之一: ${languageCodes.join(', ')}`
      }),

    contentType: Joi.string()
      .valid(...contentTypes)
      .optional()
      .default('title')
      .messages({
        'any.only': `内容类型必须是以下之一: ${contentTypes.join(', ')}`
      }),

    provider: Joi.string()
      .optional()
      .messages({
        'string.base': '提供商名称必须是字符串'
      }),

    platform: Joi.string()
      .optional()
      .messages({
        'string.base': '平台代码必须是字符串'
      }),

    source: Joi.string()
      .valid(...translationSources)
      .optional()
      .messages({
        'any.only': `翻译来源必须是以下之一: ${translationSources.join(', ')}`,
        'string.base': '翻译来源必须是字符串'
      })
  }),

  /**
   * 产品翻译验证
   */
  translateProduct: Joi.object({
    title: Joi.string()
      .required()
      .min(1)
      .max(500)
      .messages({
        'string.empty': '产品标题不能为空',
        'string.min': '产品标题不能为空',
        'string.max': '产品标题长度不能超过500字符',
        'any.required': '产品标题是必需的'
      }),

    description: Joi.string()
      .required()
      .min(1)
      .max(5000)
      .messages({
        'string.empty': '产品描述不能为空',
        'string.min': '产品描述不能为空',
        'string.max': '产品描述长度不能超过5000字符',
        'any.required': '产品描述是必需的'
      }),

    sellingPoints: Joi.array()
      .items(Joi.string().max(200))
      .max(10)
      .optional()
      .messages({
        'array.max': '卖点不能超过10个',
        'string.max': '单个卖点长度不能超过200字符'
      }),

    sourceLang: Joi.string()
      .valid(...languageCodes)
      .required()
      .messages({
        'any.only': `源语言必须是以下之一: ${languageCodes.join(', ')}`,
        'any.required': '源语言是必需的'
      }),

    targetLangs: Joi.array()
      .items(Joi.string().valid(...languageCodes))
      .min(1)
      .max(10)
      .required()
      .messages({
        'array.min': '至少需要一个目标语言',
        'array.max': '目标语言不能超过10个',
        'any.required': '目标语言列表是必需的',
        'array.includesRequiredUnknowns': `目标语言必须是以下之一: ${languageCodes.join(', ')}`
      }),

    provider: Joi.string()
      .optional()
      .messages({
        'string.base': '提供商名称必须是字符串'
      })
  }),

  /**
   * 产品批量翻译验证
   */
  translateProductBatch: Joi.object({
    platform: Joi.string()
      .required()
      .messages({
        'string.empty': '平台不能为空',
        'any.required': '平台是必需的'
      }),

    source: Joi.string()
      .valid(...translationSources)
      .required()
      .messages({
        'any.only': `翻译来源必须是以下之一: ${translationSources.join(', ')}`,
        'any.required': '翻译来源是必需的'
      }),

    sourceLang: Joi.string()
      .valid(...languageCodes)
      .required()
      .messages({
        'any.only': `源语言必须是以下之一: ${languageCodes.join(', ')}`,
        'any.required': '源语言是必需的'
      }),

    targetLangs: Joi.array()
      .items(Joi.string().valid(...languageCodes))
      .min(1)
      .max(10)
      .required()
      .messages({
        'array.min': '至少需要一个目标语言',
        'array.max': '目标语言不能超过10个',
        'any.required': '目标语言列表是必需的',
        'array.includesRequiredUnknowns': `目标语言必须是以下之一: ${languageCodes.join(', ')}`
      }),

    productids: Joi.string()
      .required()
      .pattern(/^\d+(,\d+)*$/)
      .messages({
        'string.empty': '产品ID列表不能为空',
        'string.pattern.base': '产品ID列表格式不正确，应为逗号分隔的数字',
        'any.required': '产品ID列表是必需的'
      }),

    provider: Joi.string()
      .optional()
      .messages({
        'string.base': '提供商名称必须是字符串'
      })
  })
};
