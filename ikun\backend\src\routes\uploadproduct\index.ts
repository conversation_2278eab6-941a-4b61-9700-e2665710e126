/**
 * Upload Product Routes
 * Product listing management endpoints
 */

import { Router } from 'express';
import listingsRoutes from './listings';
import categoriesRoutes from './categories';
import attributesRoutes from './attributes';

const router = Router();

// Mount sub-routes
router.use('/listings', listingsRoutes);
router.use('/categories', categoriesRoutes);
router.use('/attributes', attributesRoutes);

export default router;
