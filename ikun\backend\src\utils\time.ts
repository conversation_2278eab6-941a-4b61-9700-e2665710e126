/**
 * 时间工具函数
 * 统一使用北京时间（+08:00）
 */

/**
 * 获取北京时间的Date对象
 */
export function getBeijingTime(): Date {
  const now = new Date();
  // 获取UTC时间戳
  const utcTime = now.getTime() + (now.getTimezoneOffset() * 60000);
  // 加上北京时间偏移（+8小时）
  const beijingTime = new Date(utcTime + (8 * 3600000));
  return beijingTime;
}

/**
 * 获取北京时间的ISO字符串
 * 格式：2025-06-20T16:37:17.109+08:00
 */
export function getBeijingTimeISO(): string {
  const beijingTime = getBeijingTime();
  // 手动构建带时区的ISO字符串
  const year = beijingTime.getFullYear();
  const month = String(beijingTime.getMonth() + 1).padStart(2, '0');
  const day = String(beijingTime.getDate()).padStart(2, '0');
  const hours = String(beijingTime.getHours()).padStart(2, '0');
  const minutes = String(beijingTime.getMinutes()).padStart(2, '0');
  const seconds = String(beijingTime.getSeconds()).padStart(2, '0');
  const milliseconds = String(beijingTime.getMilliseconds()).padStart(3, '0');
  
  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.${milliseconds}+08:00`;
}

/**
 * 获取北京时间的格式化字符串
 * 格式：2025-06-20 16:37:17
 */
export function getBeijingTimeFormatted(): string {
  const beijingTime = getBeijingTime();
  const year = beijingTime.getFullYear();
  const month = String(beijingTime.getMonth() + 1).padStart(2, '0');
  const day = String(beijingTime.getDate()).padStart(2, '0');
  const hours = String(beijingTime.getHours()).padStart(2, '0');
  const minutes = String(beijingTime.getMinutes()).padStart(2, '0');
  const seconds = String(beijingTime.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}

/**
 * 将UTC时间转换为北京时间
 */
export function utcToBeijingTime(utcDate: Date): Date {
  const utcTime = utcDate.getTime();
  return new Date(utcTime + (8 * 3600000));
}

/**
 * 将北京时间转换为UTC时间
 */
export function beijingToUtcTime(beijingDate: Date): Date {
  const beijingTime = beijingDate.getTime();
  return new Date(beijingTime - (8 * 3600000));
}

/**
 * 格式化任意时间为北京时间字符串
 */
export function formatToBeijingTime(date: Date | string): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  const beijingTime = utcToBeijingTime(dateObj);
  return getBeijingTimeFormatted();
}
