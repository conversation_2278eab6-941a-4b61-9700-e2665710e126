# 跨境电商ERP系统开发文档

## 1. 项目概述

### 1.1 项目名称
跨境电商ERP管理系统

### 1.2 项目目标
开发一个基于云端部署的Web ERP系统，专门服务于跨境电商运营，提供产品管理、采集、上架、订单管理等核心功能。

### 1.3 目标用户
跨境电商运营人员、店铺管理者

## 📚 相关文档索引

### 核心技术文档
- **[IKUN_ERP_API文档.md](./IKUN_ERP_API文档.md)** - 完整的API接口文档（包含快速导航）
- **[产品上架功能技术文档.md](./产品上架功能技术文档.md)** - 产品上架功能完整技术文档
- **[全局时间规范.md](./全局时间规范.md)** - 全局时间处理规范

### 前端开发文档
- **[前端-开发进度文档.md](./frontend/前端-开发进度文档.md)** - 前端开发进度和架构
- **[前端-UI规范.md](./frontend/前端-UI规范.md)** - UI设计规范
- **[前端-组件开发规范.md](./frontend/前端-组件开发规范.md)** - 组件开发规范

### 后端开发文档
- **[后端-开发进度文档.md](./backend/后端-开发进度文档.md)** - 后端开发进度和架构
- **[后端-开发规范.md](./backend/后端-开发规范.md)** - 后端开发规范
- **[数据库自动管理说明.md](./backend/数据库自动管理说明.md)** - 数据库自动管理功能

## 2. 技术架构

### 2.1 技术栈选择
- **前端框架**: Next.js (React + TypeScript)
- **UI组件库**: Shadcn/ui (已确定使用)
- **样式方案**: Tailwind CSS
- **状态管理**: Zustand (轻量) / Redux Toolkit (复杂状态)
- **后端**: Node.js + Express + TypeScript
- **主数据库**: MySQL (本地开发: localhost:3306, root/123456)
- **缓存层**: Redis (云端部署时启用，开发阶段暂不使用)
- **容器化**: Docker (可选--留到最后期在考虑)
- **API版本**: V1 (便于后期升级)

### 2.2 项目结构
```
IKUN-erp/
├── frontend/                   # Next.js 前端项目 (前端部分参考我另外一个项目web2.0/templates里面的页面)
│   ├── src/
│   │   ├── app/               # Next.js App Router
│   │   ├── components/        # React组件
│   │   ├── lib/              # 工具函数和配置
│   │   ├── store/            # 状态管理
│   │   └── types/            # TypeScript类型定义
│   ├── public/               # 静态资源
│   ├── next.config.js
│   └── package.json
├── backend/                   # Node.js 后端项目
│   ├── src/
│   │   ├── api/              # API路由 (V1)
│   │   │   ├── v1/
│   │   │   │   ├── products/ # 产品相关API
│   │   │   │   ├── orders/   # 订单相关API
│   │   │   │   ├── platforms/# 平台相关API
│   │   │   │   └── scraping/ # 采集相关API
│   │   ├── services/         # 业务逻辑服务
│   │   ├── models/           # 数据模型
│   │   ├── middleware/       # 中间件
│   │   ├── utils/            # 工具函数
│   │   └── database/         # 数据库配置
│   ├── scripts/              # Python采集脚本
│   │   ├── amazon/
│   │   ├── ebay/
│   │   ├── shopify/
│   │   └── common/
│   ├── platforms/            # 平台集成代码
│   │   ├── amazon/
│   │   ├── ebay/
│   │   ├── shopify/
│   │   ├── walmart/
│   │   └── common/
│   ├── translation/          # 翻译模块 (预留)
│   ├── Dockerfile
│   └── package.json
├── shared/                   # 共享类型和工具
│   └── types/
└── docker-compose.yml        # 本地开发环境
```

### 2.3 云端部署架构
```
云端部署架构:
├── 前端 (Vercel)
│   ├── Next.js 应用
│   ├── 静态资源 CDN
│   └── 自动部署 (Git集成)
├── 后端 (Railway/Render)
│   ├── Node.js API 服务
│   ├── Docker 容器
│   └── 环境变量管理
├── 数据库 (云端)
│   ├── MySQL 主库 (云端部署)
│   ├── Redis 缓存 (云端部署时启用)
│   └── 数据备份策略
└── 第三方服务
    ├── 文件存储 (AWS S3/Cloudinary)
    ├── 邮件服务 (SendGrid)
    └── 监控日志 (LogRocket/Sentry)
```

## 3. 核心功能模块

### 3.1 产品库管理
**功能描述**: 存储和管理产品SKU信息

**主要功能**:
- 产品信息录入/编辑
- SKU管理
- 产品分类
- 库存管理
- 产品图片管理
- 批量导入/导出

**数据结构**:
```typescript
interface Product {
  id: string;
  sku: string;
  title: string;
  description: string;
  category: string;
  price: number;
  cost: number;
  weight: number;
  dimensions: {
    length: number;
    width: number;
    height: number;
  };
  images: string[];
  tags: string[];
  status: 'active' | 'inactive' | 'draft';
  createdAt: Date;
  updatedAt: Date;
}
```

### 3.2 产品采集模块
**功能描述**: 通过Python脚本采集各平台产品信息

**主要功能**:
- 多平台产品采集
- 采集任务管理
- 采集结果预览
- 数据清洗和格式化
- 采集历史记录

**支持平台**:
- Amazon
- eBay
- Shopify
- AliExpress
- 其他平台 (可扩展)

**技术实现**:
- Python脚本执行
- 数据解析和清洗
- 反爬虫策略
- 代理IP管理

### 3.3 产品上架模块
**功能描述**: 将产品上架到各个销售平台

**主要功能**:
- 多平台产品上架
- 平台API集成
- 批量上架
- 上架状态监控
- 平台规则适配

**支持平台**:
- Amazon Seller Central
- eBay
- Shopify
- Walmart
- 其他平台 (可扩展)

### 3.4 订单管理模块
**功能描述**: 统一管理各平台订单

**主要功能**:
- 订单同步
- 订单状态跟踪
- 发货管理
- 退款处理
- 订单报表

**数据结构**:
```typescript
interface Order {
  id: string;
  platformOrderId: string;
  platform: string;
  customerId: string;
  items: OrderItem[];
  totalAmount: number;
  status: 'pending' | 'paid' | 'shipped' | 'delivered' | 'cancelled';
  shippingAddress: Address;
  createdAt: Date;
  updatedAt: Date;
}
```

### 3.5 产品翻译模块 (预留)
**功能描述**: 基于大模型的多语言翻译

**主要功能**:
- 产品标题翻译
- 产品描述翻译
- 关键词翻译
- 翻译质量评估
- 翻译历史管理

**基准语言**: 英语
**目标语言**: 根据平台需求配置

## 4. API设计 (V1)

### 4.1 API基础规范
- **基础路径**: `/api/v1`
- **认证方式**: JWT Token
- **数据格式**: JSON
- **错误处理**: 统一错误码和消息

### 4.2 主要API端点

#### 产品目录相关API
```
GET    /api/v1/categories        # 获取产品目录列表（支持树状结构）
POST   /api/v1/categories        # 创建产品目录
GET    /api/v1/categories/:id    # 获取产品目录详情
PUT    /api/v1/categories/:id    # 更新产品目录
DELETE /api/v1/categories/:id    # 删除产品目录
GET    /api/v1/categories/tree   # 获取完整的目录树结构
GET    /api/v1/categories/level/:level # 获取指定层级的目录
POST   /api/v1/categories/batch  # 批量创建目录
PUT    /api/v1/categories/:id/status # 更新目录状态
GET    /api/v1/categories/:id/children # 获取子目录
```

#### 产品相关API
```
GET    /api/v1/products          # 获取产品列表
POST   /api/v1/products          # 创建产品
GET    /api/v1/products/:id      # 获取产品详情
PUT    /api/v1/products/:id      # 更新产品
DELETE /api/v1/products/:id      # 删除产品
POST   /api/v1/products/import   # 批量导入产品
GET    /api/v1/products/category/:categoryId # 获取指定目录下的产品
```

#### 采集相关API
```
POST   /api/v1/scraping/start    # 开始采集任务
GET    /api/v1/scraping/tasks    # 获取采集任务列表
GET    /api/v1/scraping/tasks/:id # 获取采集任务详情
DELETE /api/v1/scraping/tasks/:id # 删除采集任务
```

#### 订单相关API
```
GET    /api/v1/orders            # 获取订单列表
GET    /api/v1/orders/:id        # 获取订单详情
PUT    /api/v1/orders/:id/status # 更新订单状态
POST   /api/v1/orders/sync       # 同步平台订单
```

### 6.2 产品目录API详细定义

#### 获取产品目录列表
```http
GET /api/v1/categories
Query Parameters:
- level: 类目层级 (1/2/3)
- parent_id: 父级类目ID
- status: 状态 (enabled/disabled)
- page: 页码
- limit: 每页数量

Response:
{
  "code": 200,
  "message": "success",
  "data": {
    "categories": [
      {
        "id": 1,
        "category_code": "CLO",
        "chinese_name": "服装",
        "english_name": "Clothing",
        "status": "enabled",
        "auto_sku": "enabled",
        "category_level": 1,
        "parent_id": null,
        "category_description": "服装类产品",
        "attribute_tags": ["color", "size"],
        "sort_order": 1,
        "category_path": "clothing"
      }
    ],
    "total": 100,
    "page": 1,
    "limit": 20
  }
}
```

#### 获取目录树结构
```http
GET /api/v1/categories/tree
Response:
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "label": "服装 (Clothing)",
      "value": "clothing",
      "level": 1,
      "children": [
        {
          "id": 4,
          "label": "男装 (Mens)",
          "value": "clothing/mens",
          "level": 2,
          "children": [
            {
              "id": 9,
              "label": "T恤 (Tshirts)",
              "value": "clothing/mens/tshirts",
              "level": 3,
              "children": []
            }
          ]
        }
      ]
    }
  ]
}
```

#### 创建产品目录
```http
POST /api/v1/categories
Request Body:
{
  "category_code": "CLO-M-T",
  "chinese_name": "T恤",
  "english_name": "Tshirts",
  "status": "enabled",
  "auto_sku": "enabled",
  "category_level": 3,
  "parent_id": 4,
  "category_description": "男士T恤衫",
  "attribute_tags": ["color", "size"],
  "sort_order": 1
}

Response:
{
  "code": 201,
  "message": "Category created successfully",
  "data": {
    "id": 15,
    "category_path": "clothing/mens/tshirts"
  }
}
```

#### 平台相关API
```
GET    /api/v1/platforms         # 获取支持的平台列表
POST   /api/v1/platforms/:platform/auth # 平台授权
POST   /api/v1/platforms/:platform/upload # 上架产品到平台
```

## 5. 数据库设计

### 5.1 主要数据表

#### product_categories (产品目录表) - 产品类目管理核心表
```sql
CREATE TABLE product_categories (
  -- 基础标识字段
  id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 类目ID，自增主键
  category_code VARCHAR(50) UNIQUE NOT NULL, -- 分类编码，用于SKU前缀，唯一

  -- 类目名称（必填字段标*）
  chinese_name VARCHAR(200) NOT NULL, -- *中文名称（必填）
  english_name VARCHAR(200) NOT NULL, -- *英文名称（必填）

  -- 类目状态和配置
  status ENUM('enabled', 'disabled') DEFAULT 'enabled', -- 状态：启用/关闭
  auto_sku ENUM('enabled', 'disabled') DEFAULT 'disabled', -- 自动SKU：启用/关闭

  -- 类目层级信息
  category_level TINYINT NOT NULL, -- 类目属性：1=一级，2=二级，3=三级
  parent_id BIGINT NULL, -- 上级目录ID（一级类目为NULL）

  -- 类目描述和属性
  category_description TEXT, -- 类目描述（中文描述记录信息）
  attribute_tags JSON, -- 属性标签（color/model/size等变体属性）

  -- 排序和路径
  sort_order INT DEFAULT 0, -- 排序权重
  category_path VARCHAR(500), -- 类目路径（如：clothing/mens/tshirts）

  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 修改时间

  -- 索引
  INDEX idx_category_code (category_code),
  INDEX idx_status (status),
  INDEX idx_category_level (category_level),
  INDEX idx_parent_id (parent_id),
  INDEX idx_category_path (category_path),
  INDEX idx_sort_order (sort_order),

  -- 外键约束
  FOREIGN KEY (parent_id) REFERENCES product_categories(id) ON DELETE SET NULL,

  -- 检查约束
  CONSTRAINT chk_category_level CHECK (category_level IN (1, 2, 3)),
  CONSTRAINT chk_parent_level CHECK (
    (category_level = 1 AND parent_id IS NULL) OR
    (category_level > 1 AND parent_id IS NOT NULL)
  )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### product_dropship (铺货产品表) - 产品库核心表
```sql
CREATE TABLE product_dropship (
  -- 基础标识字段
  id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 全局ID，从10000001开始，唯一且不可修改
  source VARCHAR(50) NOT NULL, -- 来源：excel导入/手动添加/采集自什么平台
  sku VARCHAR(100) UNIQUE NOT NULL, -- *产品SKU，唯一，创建后不可修改（必填）
  ean VARCHAR(20) UNIQUE NOT NULL, -- *产品EAN，国际产品代码（必填）

  -- 产品基本信息（必填字段标*）
  category_id BIGINT NOT NULL, -- *产品类目ID，关联product_categories表（必填）
  category VARCHAR(200) NOT NULL, -- *产品类目路径（英文，如：clothing/mens/tshirts）（必填）
  english_title VARCHAR(500) NOT NULL, -- *英文标题（必填）
  english_description TEXT NOT NULL, -- *英文描述（必填）

  -- 产品图片（图片1为主图，必须）
  image1 VARCHAR(500) NOT NULL, -- *图片1（主图，必须属性）
  image2 VARCHAR(500), -- 图片2
  image3 VARCHAR(500), -- 图片3
  image4 VARCHAR(500), -- 图片4
  image5 VARCHAR(500), -- 图片5

  -- 价格和物理属性
  cost_price DECIMAL(10,2), -- 成本价（￥符号，人民币单位）
  package_weight INT, -- 包装重量（g为单位）
  package_length DECIMAL(8,2), -- 包装长度（cm单位）
  package_width DECIMAL(8,2), -- 包装宽度（cm单位）
  package_height DECIMAL(8,2), -- 包装高度（cm单位）

  -- 采购和业务信息
  purchase_link TEXT, -- 采购链接（用于采购商品）

  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 修改时间

  -- 认领记录（两个字段）
  claim_time TIMESTAMP NULL, -- 认领时间
  claim_platform VARCHAR(100), -- 认领平台

  -- 业务统计
  listing_count INT DEFAULT 0, -- 刊登次数（产品被刊登到其他平台的次数）

  -- 备注和状态
  remarks TEXT, -- 备注（用于记录产品额外信息）
  status VARCHAR(20) DEFAULT 'draft', -- 状态：draft/active/inactive (三种状态：草稿/活跃/禁用)

  -- 索引
  INDEX idx_sku (sku),
  INDEX idx_ean (ean),
  INDEX idx_status (status),
  INDEX idx_category_id (category_id),
  INDEX idx_category (category),
  INDEX idx_source (source),
  INDEX idx_claim_platform (claim_platform),
  INDEX idx_created_at (created_at),

  -- 外键约束
  FOREIGN KEY (category_id) REFERENCES product_categories(id) ON DELETE RESTRICT,

  -- 全文搜索索引
  FULLTEXT INDEX idx_search (english_title, english_description, sku, ean, category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 设置ID自增起始值从10000001开始
ALTER TABLE product_dropship AUTO_INCREMENT = 10000001;

-- 添加约束确保数据完整性
ALTER TABLE product_dropship
  ADD CONSTRAINT chk_status CHECK (status IN ('draft', 'active', 'inactive')),
  ADD CONSTRAINT chk_cost_price CHECK (cost_price >= 0),
  ADD CONSTRAINT chk_package_weight CHECK (package_weight >= 0),
  ADD CONSTRAINT chk_listing_count CHECK (listing_count >= 0);
```

#### orders_updata (订单表) - 完整订单管理核心表
```sql
CREATE TABLE orders_updata (
  -- 基础标识字段
  id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 全局ID，从10001开始，唯一且不可修改
  order_number VARCHAR(100) UNIQUE NOT NULL, -- *订单编号，唯一不可修改（必填）
  transaction_id VARCHAR(100) UNIQUE NOT NULL, -- *交易编号，唯一不可修改（必填）

  -- 店铺和平台信息
  store_name VARCHAR(200) NOT NULL, -- *店铺名（店铺别名）（必填）
  platform VARCHAR(50) NOT NULL, -- *所属平台（必填）

  -- 时间信息
  payment_time TIMESTAMP NULL, -- 付款时间（北京时间）
  platform_order_status VARCHAR(50), -- 平台订单状态（从平台同步或手动创建）
  order_remarks TEXT, -- 订单备注（从平台同步或手动创建）
  ioss_number VARCHAR(50), -- IOSS编号（从平台同步或手动创建）

  -- 客户信息
  customer_id VARCHAR(100) NOT NULL, -- *客户ID，唯一且不可修改（必填）
  customer_name VARCHAR(200) NOT NULL, -- *客户姓名（必填）
  phone1 VARCHAR(50), -- 电话1
  phone2 VARCHAR(50), -- 电话2
  country VARCHAR(100), -- 国家（中文）
  country_en VARCHAR(100), -- 国家（英文）
  state_province VARCHAR(100), -- 所属地区（省/州）
  city VARCHAR(100), -- 所属城市
  postal_code VARCHAR(20), -- 邮政编码
  shipping_address TEXT, -- 包裹邮寄地址
  email VARCHAR(200), -- 联系邮箱

  -- 商品信息
  product_name_en VARCHAR(500), -- 商品英文名称
  product_sku VARCHAR(100), -- 商品SKU
  product_quantity INT, -- 商品SKU数量
  product_unit_price DECIMAL(10,2), -- 商品销售单价
  product_total_price DECIMAL(10,2), -- 商品销售总价
  shipping_fee DECIMAL(10,2), -- 运费收入
  product_image_url VARCHAR(500), -- 商品图片链接

  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间（订单创建或从平台拉取时间，北京时间）
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 更新时间

  -- 索引
  INDEX idx_order_number (order_number),
  INDEX idx_transaction_id (transaction_id),
  INDEX idx_platform (platform),
  INDEX idx_customer_id (customer_id),
  INDEX idx_store_name (store_name),
  INDEX idx_payment_time (payment_time),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 设置ID自增起始值从10001开始
ALTER TABLE orders_updata AUTO_INCREMENT = 10001;
```

#### stores_platforms (平台配置表) - 独立平台管理核心表
```sql
CREATE TABLE stores_platforms (
  -- 基础标识字段
  id INT AUTO_INCREMENT PRIMARY KEY,
  platform_code VARCHAR(50) UNIQUE NOT NULL, -- 平台代码，如amazon、ebay、phh

  -- 平台基本信息
  platform_name VARCHAR(100) NOT NULL, -- 平台显示名称（中文）
  platform_name_en VARCHAR(100) NOT NULL, -- 平台英文名称
  logo_url VARCHAR(500), -- 平台Logo图片链接

  -- 平台状态和配置
  status ENUM('active', 'inactive') DEFAULT 'active', -- 平台状态
  sort_order INT DEFAULT 0, -- 排序顺序
  config_fields JSON, -- 平台特定配置字段定义
  description TEXT, -- 平台描述

  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  -- 索引
  INDEX idx_platform_code (platform_code),
  INDEX idx_status (status),
  INDEX idx_sort_order (sort_order)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### stores_updata (统一店铺表) - 多平台店铺管理核心表
```sql
CREATE TABLE stores_updata (
  -- 基础标识字段
  id BIGINT AUTO_INCREMENT PRIMARY KEY, -- 全局ID，从100开始
  platform_code VARCHAR(50) NOT NULL, -- 平台代码，关联platforms表

  -- 店铺基本信息
  store_name VARCHAR(200) NOT NULL, -- 店铺名称，可修改
  status ENUM('active', 'failed') DEFAULT 'active', -- 绑定状态：活跃-正常，失败-绑定失败

  -- API配置信息
  api_key VARCHAR(500), -- API密钥
  token VARCHAR(500), -- 短期token，30天有效
  token_expires_at TIMESTAMP, -- token过期时间
  site VARCHAR(50), -- 平台销售站点

  -- 平台特定配置（JSON存储）
  platform_config JSON, -- 平台特定配置信息

  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, -- 创建时间（北京时间）
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP, -- 修改时间（北京时间）

  -- 索引
  INDEX idx_platform_code (platform_code),
  INDEX idx_status (status),
  INDEX idx_store_name (store_name),

  -- 外键约束
  FOREIGN KEY (platform_code) REFERENCES stores_platforms(platform_code) ON UPDATE CASCADE ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 设置ID自增起始值从100开始
ALTER TABLE stores_updata AUTO_INCREMENT = 100;

### 5.2 数据库特性

#### 自动迁移系统
- ✅ **启动时自动创建表** - 系统启动时检查并创建缺失的数据表
- ✅ **字段自动更新** - 检测模型变更，自动添加新字段
- ✅ **索引自动创建** - 根据定义自动创建索引和外键
- ✅ **数据完整性** - 外键约束保证数据关联完整性

#### 时区配置
- ✅ **统一北京时间** - 所有时间字段使用+08:00时区
- ✅ **自动时间戳** - created_at和updated_at自动管理

#### 初始化数据
- ✅ **平台数据自动初始化** - Amazon、eBay、PHH平台基础数据
- ✅ **配置字段定义** - 各平台特定字段要求自动配置

#### ID管理策略
- ✅ **订单ID** - 从10001开始自增
- ✅ **店铺ID** - 从100开始自增
- ✅ **产品ID** - 从10000001开始自增

#### scraping_tasks (采集任务表)
```sql
CREATE TABLE scraping_tasks (
  id VARCHAR(36) PRIMARY KEY,
  platform VARCHAR(50) NOT NULL,
  url TEXT NOT NULL,
  status VARCHAR(20) DEFAULT 'pending',
  result JSON, -- JSON格式存储采集结果
  error_message TEXT,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  completed_at TIMESTAMP NULL,

  INDEX idx_platform (platform),
  INDEX idx_status (status),
  INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

## 6. 开发计划

### 6.1 开发阶段

#### 第一阶段: 基础框架搭建 (1-2周)
- Next.js + Node.js 项目初始化
- 基础UI框架搭建 (Ant Design)
- 云端数据库设计和初始化
- 基础API框架和认证系统

#### 第二阶段: 产品库管理 (2-3周)
- 产品CRUD功能
- 产品分类管理
- 图片上传和管理
- 批量导入导出

#### 第三阶段: 产品采集模块 (3-4周)
- Python脚本集成
- 采集任务管理
- 多平台采集适配
- 数据清洗和格式化

#### 第四阶段: 产品上架模块 (3-4周)
- 平台API集成
- 上架流程设计
- 批量上架功能
- 状态监控

#### 第五阶段: 订单管理模块 (2-3周)
- 订单同步功能
- 订单状态管理
- 发货和退款处理

#### 第六阶段: 系统优化和测试 (2-3周)
- 性能优化
- 错误处理完善
- 用户体验优化
- 全面测试

### 6.2 里程碑
- **MVP版本**: 完成产品库管理和基础采集功能
- **Beta版本**: 完成所有核心功能
- **正式版本**: 完成测试和优化

## 7. 技术难点和解决方案

### 7.1 Python脚本集成
**难点**: Node.js调用Python脚本
**解决方案**: 使用child_process模块执行Python脚本，通过JSON进行数据交换

### 7.2 多平台API集成
**难点**: 不同平台API差异较大
**解决方案**: 设计统一的适配器模式，每个平台独立实现

### 7.3 反爬虫处理
**难点**: 采集时遇到反爬虫机制
**解决方案**: 代理IP轮换、请求频率控制、User-Agent轮换

### 7.4 数据同步
**难点**: 多平台数据同步的一致性
**解决方案**: 设计数据同步队列，支持重试和错误恢复

### 7.5 云端部署和扩展性
**难点**: 云端环境的性能和可扩展性
**解决方案**:
- 前端使用Vercel进行自动部署和CDN加速
- 后端使用Railway或Render进行容器化部署
- 数据库使用云端PostgreSQL，支持读写分离
- 使用Redis进行缓存和会话管理

## 8. 后端开发进展

### 8.1 已完成功能 (75%整体进度)

#### 基础架构 (100%)
- ✅ **项目结构** - 完整的分层架构设计
- ✅ **TypeScript配置** - 严格类型检查和路径别名
- ✅ **数据库连接** - MySQL连接池和事务支持
- ✅ **配置管理** - 环境变量和配置文件管理
- ✅ **日志系统** - 结构化日志记录
- ✅ **错误处理** - 统一错误处理机制

#### 数据库设计 (95%)
- ✅ **自动迁移系统** - 启动时自动创建/更新表结构
- ✅ **核心数据表** - 产品、订单、店铺、平台表完整设计
- ✅ **关系管理** - 外键约束和数据完整性
- ✅ **索引优化** - 查询性能优化
- ✅ **时区配置** - 统一北京时间支持
- ✅ **初始化数据** - 平台基础数据自动初始化

#### 数据模型层 (100%)
- ✅ **BaseModel** - 通用CRUD操作和事务支持
- ✅ **产品模型** - CategoryModel、ProductModel完整实现
- ✅ **订单模型** - OrderModel完整字段定义和业务逻辑
- ✅ **店铺模型** - StoreModel统一多平台管理
- ✅ **平台模型** - PlatformModel独立平台配置
- ✅ **关联关系** - 模型间关联查询和数据验证

#### API路由架构 (90%)
- ✅ **路由分层** - 按功能模块组织路由
- ✅ **版本控制** - /api/v1路径规范
- ✅ **中间件** - 请求验证和错误处理
- ✅ **统一响应** - 标准化API响应格式

### 8.2 最新完成的功能 (2025年6月)

#### 订单管理系统 (80%)
- ✅ **完整字段定义** - 包含订单、客户、商品完整信息
- ✅ **唯一性约束** - 订单编号、交易编号唯一性保证
- ✅ **业务逻辑** - 订单创建、更新、状态管理
- ✅ **高级搜索** - 多条件筛选和分页查询
- ✅ **批量操作** - 批量状态更新和数据同步

#### 店铺管理系统 (90%)
- ✅ **统一架构** - 多平台店铺统一管理
- ✅ **平台配置** - 独立平台配置表设计
- ✅ **动态字段** - 平台特定配置JSON存储
- ✅ **关联关系** - 店铺与平台外键关联
- ✅ **扩展性** - 便于添加新平台支持

#### 平台管理系统 (95%)
- ✅ **独立管理** - 平台信息集中管理
- ✅ **配置定义** - 平台特定字段要求定义
- ✅ **自动初始化** - Amazon、eBay、PHH平台数据
- ✅ **API支持** - 前端下拉菜单数据源

## 9. 前端开发进展

### 9.1 已完成功能 (70%整体进度)
- ✅ **基础架构**: Next.js + Shadcn/ui + TypeScript
- ✅ **UI组件库**: 100%完成，包含所有核心组件
- ✅ **布局系统**: 响应式布局，优化右侧占比(w-56侧边栏)
- ✅ **仪表板**: 统计卡片、概览、快速操作、最近任务
- ✅ **产品管理**: 完整的产品管理系统
  - ✅ 下拉菜单导航 (产品目录/铺货产品/POD产品)
  - ✅ 产品目录页面 (树状结构、左右分栏、三级目录)
  - ✅ 铺货产品完整页面 (表格、筛选、搜索、分页)
  - ✅ POD产品占位页面
- ✅ **UI规范**: 制定完整的UI设计规范文档
- ✅ **重点UI规范实施**:
  - ✅ 所有页面移除顶部大标题
  - ✅ 底部信息栏固定且紧凑设计
  - ✅ 搜索功能右侧布局优化

### 8.2 最新完成的功能 (2024年1月最新)
- ✅ **铺货产品页面优化**:
  - 完整的产品表格 (9列布局)
  - 状态筛选按钮组
  - 搜索和重置功能
  - 批量操作下拉菜单
  - 固定底部分页
- ✅ **UI规范严格执行**:
  - 移除所有页面顶部标题大字符
  - 底部分页固定到最底部并缩小高度
  - 搜索重置状态栏移到右侧与筛选按钮同行
- ✅ **组件优化**:
  - 修复所有图标导入问题
  - 清理未使用的变量和导入
  - 优化组件性能和可维护性
- ✅ **卡片式产品表单系统** (重大功能更新):
  - 添加产品卡片组件 (AddProductCard)
  - 编辑产品卡片组件 (EditProductCard)
  - 分标签页表单设计 (基本信息/图片/包装/其他/元数据)
  - 完整的表单验证和错误处理
  - 卡片固定尺寸优化，防止布局跳动
- ✅ **产品交互功能优化**:
  - 筛选器固定宽度，防止选择后变化
  - 双击产品直接编辑功能
  - 单击产品选中/取消选中功能
- ✅ **产品状态规范化**:
  - 移除"已认领"状态，统一为三种状态
  - 状态定义：活跃(active)、草稿(draft)、禁用(inactive)
  - 更新所有相关组件和数据库约束

### 8.3 当前开发重点
- **店铺管理页面**: 基于原web2.0模板开发
- **任务中心页面**: 任务监控和管理功能
- **产品管理功能增强**: 表单功能、模态框、批量操作

### 8.4 技术特色
- **严格UI规范**: 所有页面遵循统一设计规范，无冗余信息
- **响应式设计**: 支持桌面端和移动端
- **模块化架构**: 组件可复用，易于维护
- **类型安全**: 完整的TypeScript类型定义
- **性能优化**: 组件懒加载、代码分割

### 8.5 重要UI规范 ⚠️ **严格执行**
- **⚠️ 页面标题**: 所有页面顶部不显示页面标题大字符
- **⚠️ 底部信息栏**: 固定到最底部并缩小高度 (py-2)
- **⚠️ 搜索布局**: 搜索重置状态栏移到右侧与筛选按钮同行
- **布局**: 侧边栏224px(w-56)，主内容区域最大化
- **组件库**: 统一使用Shadcn/ui组件
- **颜色规范**: 主色Blue-500，状态色标准化
- **响应式**: 移动端友好的设计

## 9. 详细开发进度记录

### 9.1 前端开发时间线

#### 2024年1月 - 第一周
- ✅ **项目初始化**: Next.js + TypeScript + Tailwind CSS
- ✅ **Shadcn/ui集成**: 安装和配置UI组件库
- ✅ **基础布局**: DashboardLayout + Sidebar + Header
- ✅ **路由结构**: App Router配置

#### 2024年1月 - 第二周
- ✅ **仪表板开发**:
  - DashboardStats (统计卡片)
  - ProductOverview (产品概览)
  - QuickActions (快速操作)
  - RecentTasks (最近任务)
- ✅ **侧边栏优化**: 导航菜单和响应式设计

#### 2024年1月 - 第三周
- ✅ **产品管理基础**:
  - 产品管理页面路由
  - 下拉菜单导航 (铺货产品/POD产品)
  - 基础页面结构
- ✅ **UI规范制定**: 完整的UI设计规范文档

#### 2024年1月 - 第四周 (最新)
- ✅ **铺货产品页面完整开发**:
  - 完整的产品表格 (9列布局)
  - 工具栏 (添加、导入、编辑、批量操作)
  - 状态筛选按钮组 (5个主要状态)
  - 搜索和重置功能
  - 批量操作下拉菜单
  - 固定底部分页
- ✅ **产品目录页面开发**:
  - 树状结构展示 (支持三级目录)
  - 左右分栏布局 (目录树 + 详情区域)
  - 可展开收缩的目录节点
  - 目录详情查看和编辑模式
  - 搜索功能和导航优化
- ✅ **铺货产品数据库设计完善**:
  - 完整的字段定义 (ID、来源、SKU、EAN、类目等)
  - 必填字段标识 (SKU、EAN、类目、英文标题、英文描述、图片1)
  - 图片字段 (5张图片，图片1为主图)
  - 价格重量尺寸字段 (成本价、包装信息)
  - 认领和刊登记录字段
  - 数据库约束和索引优化
- ✅ **重点UI规范实施**:
  - 移除所有页面顶部大标题字符
  - 底部信息栏固定到最底部并缩小高度
  - 搜索重置状态栏移到右侧与筛选按钮同行
- ✅ **技术优化**:
  - 修复所有Lucide React图标导入问题
  - 清理未使用的变量和导入
  - 组件性能优化

### 9.2 文件结构现状
```
ikun/frontend/src/
├── app/
│   ├── page.tsx                    # ✅ 仪表板主页
│   ├── products/
│   │   ├── page.tsx               # ✅ 产品管理主页 (下拉菜单)
│   │   ├── dropship/
│   │   │   └── page.tsx           # ✅ 铺货产品页面
│   │   └── pod/
│   │       └── page.tsx           # ✅ POD产品页面
│   ├── layout.tsx                 # ✅ 根布局
│   └── globals.css                # ✅ 全局样式
├── components/
│   ├── ui/                        # ✅ Shadcn/ui组件
│   ├── layout/
│   │   ├── dashboard-layout.tsx   # ✅ 仪表板布局
│   │   ├── sidebar.tsx            # ✅ 侧边栏
│   │   └── header.tsx             # ✅ 顶部栏
│   ├── dashboard/
│   │   ├── dashboard-stats.tsx    # ✅ 统计卡片
│   │   ├── product-overview.tsx   # ✅ 产品概览
│   │   ├── quick-actions.tsx      # ✅ 快速操作
│   │   └── recent-tasks.tsx       # ✅ 最近任务
│   └── products/
│       ├── dropship-products-page-full.tsx  # ✅ 铺货产品完整页面
│       └── pod-products-page.tsx            # ✅ POD产品页面
├── lib/
│   └── utils.ts                   # ✅ 工具函数
└── 配置文件                        # ✅ 完整配置
```

### 9.3 组件完成度统计
- **布局组件**: 100% (3/3)
- **仪表板组件**: 100% (4/4)
- **产品管理组件**: 80% (2/3) - 缺少产品表单组件
- **UI基础组件**: 100% (Shadcn/ui完整集成)

### 9.4 页面完成度统计
- **仪表板**: 100% ✅
- **产品管理**: 85% ✅ (铺货产品完整，POD产品占位)
- **店铺管理**: 0% ⏳ (待开发)
- **任务中心**: 0% ⏳ (待开发)
- **系统设置**: 0% ⏳ (待开发)

### 9.5 下一步开发计划
1. **店铺管理页面** (预计1周)
   - 参考web2.0/templates页面结构
   - 店铺列表、添加、编辑功能
   - 平台集成状态显示

2. **任务中心页面** (预计1周)
   - 任务列表和状态监控
   - 任务创建和管理
   - 进度跟踪

3. **产品管理增强** (预计1周)
   - 产品添加/编辑表单
   - 批量操作功能实现
   - 图片上传功能

## 10. 后续扩展计划

### 9.1 功能扩展
- 财务管理模块
- 库存预警系统
- 数据分析和报表
- 移动端支持

### 10.2 技术升级
- API V2版本
- 微服务架构
- 容器化部署优化
- 实时数据同步
- CDN加速
- 负载均衡

---

## 📋 文档更新记录

### V1.5 - 2025年7月14日
- ✅ 认证系统安全漏洞修复 (Token验证增强)
- ✅ Worten产品上架表单关闭按钮修复
- ✅ 产品上架功能完整实现 (Worten/PHH平台)
- ✅ 多语言支持系统完善
- ✅ 前端认证状态自动验证和清理
- ✅ API客户端401错误自动处理
- ✅ 数据库自动管理系统优化

### V1.4 - 2025年6月19日
- ✅ 更新后端开发进度至75%
- ✅ 完善订单管理数据库设计和业务逻辑
- ✅ 完成店铺管理统一架构设计
- ✅ 实现平台管理独立配置系统
- ✅ 添加自动数据库迁移系统说明
- ✅ 更新数据库特性和时区配置
- ✅ 完善API设计和数据模型层

### V1.3 - 2024年1月第四周
- ✅ 更新前端开发进度至70%
- ✅ 添加详细开发进度记录章节
- ✅ 记录重点UI规范实施情况
- ✅ 更新组件和页面完成度统计
- ✅ 制定下一步开发计划

### V1.2 - 2024年1月第三周
- ✅ 添加UI规范章节
- ✅ 更新产品管理开发进展
- ✅ 完善技术架构说明

### V1.1 - 2024年1月第二周
- ✅ 初始版本创建
- ✅ 基础架构和功能模块定义
- ✅ API设计和数据库设计

---

**文档版本**: V1.5
**创建日期**: 2024年1月
**最后更新**: 2025年7月14日 - 认证系统安全增强，产品上架功能完善
