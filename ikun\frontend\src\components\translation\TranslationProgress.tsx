'use client'

import { useState, useEffect } from 'react'
import { Progress } from '@/components/ui/progress'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  CheckCircle, 
  AlertCircle, 
  Clock, 
  Languages,
  Loader2 
} from 'lucide-react'
import { LanguageCode, getLanguageName } from './TranslationButton'

// 翻译任务状态
export type TranslationTaskStatus = 'pending' | 'running' | 'completed' | 'failed'

// 单个语言翻译状态
export interface LanguageTranslationStatus {
  language: LanguageCode
  status: TranslationTaskStatus
  result?: string
  error?: string
}

// 翻译进度属性
interface TranslationProgressProps {
  title?: string
  totalLanguages: number
  languageStatuses: LanguageTranslationStatus[]
  onComplete?: (results: Record<string, string>) => void
  onError?: (errors: Record<string, string>) => void
  className?: string
}

export function TranslationProgress({
  title = '翻译进度',
  totalLanguages,
  languageStatuses,
  onComplete,
  onError,
  className = ''
}: TranslationProgressProps) {
  const [progress, setProgress] = useState(0)

  // 计算进度
  useEffect(() => {
    const completedCount = languageStatuses.filter(
      status => status.status === 'completed' || status.status === 'failed'
    ).length
    
    const newProgress = totalLanguages > 0 ? (completedCount / totalLanguages) * 100 : 0
    setProgress(newProgress)

    // 检查是否全部完成
    if (completedCount === totalLanguages && totalLanguages > 0) {
      const results: Record<string, string> = {}
      const errors: Record<string, string> = {}

      languageStatuses.forEach(status => {
        if (status.status === 'completed' && status.result) {
          results[status.language.toUpperCase()] = status.result
        } else if (status.status === 'failed' && status.error) {
          errors[status.language.toUpperCase()] = status.error
        }
      })

      // 触发回调
      if (Object.keys(results).length > 0 && onComplete) {
        onComplete(results)
      }
      if (Object.keys(errors).length > 0 && onError) {
        onError(errors)
      }
    }
  }, [languageStatuses, totalLanguages, onComplete, onError])

  // 获取状态统计
  const getStatusCounts = () => {
    const counts = {
      pending: 0,
      running: 0,
      completed: 0,
      failed: 0
    }

    languageStatuses.forEach(status => {
      counts[status.status]++
    })

    return counts
  }

  const statusCounts = getStatusCounts()

  // 获取状态图标
  const getStatusIcon = (status: TranslationTaskStatus) => {
    switch (status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-gray-500" />
      case 'running':
        return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />
      case 'failed':
        return <AlertCircle className="w-4 h-4 text-red-500" />
    }
  }

  // 获取状态颜色
  const getStatusColor = (status: TranslationTaskStatus) => {
    switch (status) {
      case 'pending':
        return 'secondary'
      case 'running':
        return 'default'
      case 'completed':
        return 'default'
      case 'failed':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-base">
          <Languages className="w-4 h-4" />
          {title}
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 总体进度条 */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>总体进度</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* 状态统计 */}
        <div className="flex gap-2 flex-wrap">
          {statusCounts.completed > 0 && (
            <Badge variant="default" className="bg-green-100 text-green-800 hover:bg-green-100">
              <CheckCircle className="w-3 h-3 mr-1" />
              完成 {statusCounts.completed}
            </Badge>
          )}
          {statusCounts.running > 0 && (
            <Badge variant="default" className="bg-blue-100 text-blue-800 hover:bg-blue-100">
              <Loader2 className="w-3 h-3 mr-1 animate-spin" />
              进行中 {statusCounts.running}
            </Badge>
          )}
          {statusCounts.failed > 0 && (
            <Badge variant="destructive">
              <AlertCircle className="w-3 h-3 mr-1" />
              失败 {statusCounts.failed}
            </Badge>
          )}
          {statusCounts.pending > 0 && (
            <Badge variant="secondary">
              <Clock className="w-3 h-3 mr-1" />
              等待 {statusCounts.pending}
            </Badge>
          )}
        </div>

        {/* 详细语言状态 */}
        {languageStatuses.length > 0 && (
          <div className="space-y-2">
            <div className="text-sm font-medium text-gray-700">语言详情</div>
            <div className="space-y-1 max-h-32 overflow-y-auto">
              {languageStatuses.map((langStatus) => (
                <div
                  key={langStatus.language}
                  className="flex items-center justify-between p-2 bg-gray-50 rounded text-sm"
                >
                  <div className="flex items-center gap-2">
                    {getStatusIcon(langStatus.status)}
                    <span className="font-medium">
                      {getLanguageName(langStatus.language)}
                    </span>
                  </div>
                  
                  <Badge 
                    variant={getStatusColor(langStatus.status)}
                    className="text-xs"
                  >
                    {langStatus.status === 'pending' && '等待中'}
                    {langStatus.status === 'running' && '翻译中'}
                    {langStatus.status === 'completed' && '已完成'}
                    {langStatus.status === 'failed' && '失败'}
                  </Badge>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* 错误信息 */}
        {statusCounts.failed > 0 && (
          <div className="space-y-2">
            <div className="text-sm font-medium text-red-700">错误详情</div>
            <div className="space-y-1 max-h-24 overflow-y-auto">
              {languageStatuses
                .filter(status => status.status === 'failed' && status.error)
                .map((langStatus) => (
                  <div
                    key={langStatus.language}
                    className="p-2 bg-red-50 border border-red-200 rounded text-xs text-red-700"
                  >
                    <span className="font-medium">
                      {getLanguageName(langStatus.language)}:
                    </span>{' '}
                    {langStatus.error}
                  </div>
                ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
