/**
 * Authentication Routes
 * User login, logout, registration, and token management
 */

import { Router } from 'express';
import { authController } from '@/controllers/auth/authController';
import { validateRequest } from '@/middleware/validation/validateRequest';
import { authValidation } from '@/validations/auth/authValidation';
import { authMiddleware } from '@/middleware/auth/authMiddleware';

const router = Router();

// POST /api/v1/auth/login - User login
router.post('/login',
  validateRequest({ body: authValidation.login }),
  authController.login.bind(authController)
);

// POST /api/v1/auth/logout - User logout
router.post('/logout', authController.logout.bind(authController));

// POST /api/v1/auth/refresh - Refresh token
router.post('/refresh',
  validateRequest({ body: authValidation.refresh }),
  authController.refreshToken.bind(authController)
);

// GET /api/v1/auth/profile - Get user profile (requires authentication)
router.get('/profile', authMiddleware, authController.getProfile.bind(authController));

// POST /api/v1/auth/register - User registration (admin only)
router.post('/register',
  validateRequest({ body: authValidation.register }),
  authController.register.bind(authController)
);

export default router;
