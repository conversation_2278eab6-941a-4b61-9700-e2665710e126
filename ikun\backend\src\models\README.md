# Models 数据模型层说明

## 说明 ##
🎯 推荐方案：关联数据表模式
优势分析：
1. 数据结构清晰
产品库表：存储核心产品信息（SKU、基本标题、描述、价格等）
平台刊登表：存储平台特定信息（多语言标题、描述、平台规则适配等）
职责分离，逻辑清晰
2. 扩展性强
新增平台时只需要在刊登表中添加记录
不同平台的特殊字段不会污染核心产品表
支持一个产品刊登到多个平台
3. 性能优化
产品库查询不会被平台信息拖累
可以根据需要选择性加载平台信息
索引优化更精准
4. 业务灵活性
支持你提到的多种导出方式
可以独立管理产品库和刊登信息
便于批量操作和数据分析

-- 产品库表（核心信息--示例不是真实的）
products (
  id, sku, title, description, category, 
  price, cost, weight, images, status, 
  created_at, updated_at
)

-- 平台刊登表（扩展信息--示例不是真实的）
product_listings (
  id, product_id, platform, 
  localized_title, localized_description,
  platform_specific_fields, listing_status,
  created_at, updated_at
)


🚀 实施建议：
采用当前方案，在你当前的铺货产品页面中：
默认显示产品库信息
可以通过筛选查看已刊登/未刊登状态
支持查看特定平台的刊登信息
导出时提供多种选项（基本信息/平台信息/组合信息）
这样既保持了数据的规范性，又为未来的功能扩展留下了充足的空间。

## 📋 概述

Models层是数据模型定义层，主要用于：

1. **数据表结构定义** - 定义每个数据表的字段、类型、约束
2. **数据访问对象 (DAO)** - 封装数据库操作的基础方法
3. **数据验证和转换** - 数据的格式化、验证、类型转换
4. **关系映射** - 定义表与表之间的关联关系

## 🏗️ 与其他层的关系

### Models vs Database
- **Database层** (`/database/`): 
  - 负责数据库连接管理
  - 提供基础的查询工具函数
  - 包含数据库初始化脚本 (`init.sql`)
  - 数据库连接配置 (`connection.ts`)

- **Models层** (`/models/`):
  - 定义具体的数据表模型
  - 封装表特定的CRUD操作
  - 数据验证和业务规则
  - 关联查询和复杂操作

### Models vs Services
- **Models层**: 专注于数据访问和基础操作
- **Services层**: 处理业务逻辑，调用Models层进行数据操作

## 📁 目录结构

```
models/
├── base/                    # 基础模型类
│   ├── BaseModel.ts        # 基础模型抽象类
│   └── ModelInterface.ts   # 模型接口定义
├── products/               # 产品相关模型
│   ├── CategoryModel.ts    # 产品目录模型
│   └── ProductModel.ts     # 产品模型
├── stores/                 # 店铺相关模型
│   ├── StoreModel.ts       # 店铺基础模型
│   ├── amazon/             # Amazon平台模型
│   └── ebay/               # eBay平台模型
├── orders/                 # 订单相关模型
│   └── OrderModel.ts       # 订单模型
├── users/                  # 用户相关模型
│   └── UserModel.ts        # 用户模型
└── README.md               # 本说明文件
```

## 🔧 Models层的作用

### 1. 数据表结构定义
```typescript
// 示例：CategoryModel.ts
export class CategoryModel extends BaseModel {
  protected tableName = 'product_categories';
  
  // 定义表字段
  protected fields = {
    id: 'BIGINT AUTO_INCREMENT PRIMARY KEY',
    category_code: 'VARCHAR(50) UNIQUE NOT NULL',
    chinese_name: 'VARCHAR(200) NOT NULL',
    english_name: 'VARCHAR(200) NOT NULL',
    // ... 其他字段
  };
}
```

### 2. 封装CRUD操作
```typescript
// 基础CRUD方法
public async create(data: CreateCategoryRequest): Promise<ProductCategory>
public async findById(id: number): Promise<ProductCategory | null>
public async update(id: number, data: UpdateCategoryRequest): Promise<ProductCategory>
public async delete(id: number): Promise<void>
public async findAll(options?: QueryOptions): Promise<ProductCategory[]>
```

### 3. 关联查询
```typescript
// 示例：获取分类及其子分类
public async findWithChildren(id: number): Promise<CategoryWithChildren>
public async findByParent(parentId: number): Promise<ProductCategory[]>
```

### 4. 数据验证
```typescript
// 数据验证和转换
protected validateData(data: any): ValidationResult
protected formatData(data: any): FormattedData
```

## 🆚 Models vs init.sql 的区别

### init.sql (Database层)
- **用途**: 数据库初始化脚本
- **内容**: 
  - CREATE TABLE 语句
  - 索引定义
  - 约束定义
  - 初始数据插入
- **执行时机**: 数据库初始化时一次性执行
- **维护**: 手动维护SQL语句

### Models (Models层)
- **用途**: 应用程序中的数据模型
- **内容**:
  - TypeScript类定义
  - 数据访问方法
  - 业务规则验证
  - 关联关系处理
- **执行时机**: 应用运行时动态调用
- **维护**: 代码级别的维护和版本控制

## 🔗 connection.ts 的作用

`/database/connection.ts` 是数据库连接管理器：

### 主要功能
1. **连接池管理** - 管理MySQL连接池
2. **基础查询方法** - 提供 `query()`, `queryOne()`, `execute()` 等方法
3. **事务支持** - 提供事务处理能力
4. **错误处理** - 统一的数据库错误处理

### 与Models的关系
- Models层通过 `connection.ts` 访问数据库
- Models层不直接操作数据库连接
- 保持了数据访问的一致性和安全性

## 🚀 开发计划

### 当前状态
- ✅ Database连接层已完成
- ✅ 基础架构已搭建
- 🔄 Models层待开发

### 开发优先级
1. **BaseModel** - 基础模型抽象类
2. **CategoryModel** - 产品目录模型 (配合已有的CategoryService)
3. **ProductModel** - 产品模型
4. **UserModel** - 用户模型 (认证系统需要)
5. **StoreModel** - 店铺模型 (各平台店铺管理)

### 开发原则
- 遵循单一职责原则
- 保持与Service层的清晰分离
- 统一的错误处理和日志记录
- 完整的类型定义和文档

## 📝 使用示例

```typescript
// Service层调用Models层
import { CategoryModel } from '@/models/products/CategoryModel';

class CategoryService {
  private categoryModel = new CategoryModel();
  
  public async createCategory(data: CreateCategoryRequest): Promise<ProductCategory> {
    // 业务逻辑验证
    await this.validateCategoryData(data);
    
    // 调用Model层进行数据操作
    const category = await this.categoryModel.create(data);
    
    // 业务逻辑处理
    await this.updateCategoryPath(category);
    
    return category;
  }
}
```

---

**注意**: Models层是可选的架构层。当前项目可以直接在Service层使用Database连接进行数据操作。Models层的引入主要是为了：
- 更好的代码组织
- 数据访问的标准化
- 复杂查询的封装
- 未来的扩展性

如果项目规模较小，可以暂时跳过Models层的开发，直接在Service层操作数据库。
