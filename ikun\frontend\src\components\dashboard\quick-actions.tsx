'use client'

import Link from 'next/link'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Plus, Download, Upload, Settings } from 'lucide-react'

const quickActions = [
  {
    title: '添加产品',
    description: '手动添加新产品',
    href: '/products/new',
    icon: Plus,
    variant: 'default' as const,
  },
  {
    title: '产品采集',
    description: '从平台采集产品',
    href: '/scraping',
    icon: Download,
    variant: 'secondary' as const,
  },
  {
    title: '批量上架',
    description: '发布产品到平台',
    href: '/publishing',
    icon: Upload,
    variant: 'secondary' as const,
  },
  {
    title: '系统设置',
    description: '配置系统参数',
    href: '/system/settings',
    icon: Settings,
    variant: 'outline' as const,
  },
]

export function QuickActions() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>快速操作</CardTitle>
        <CardDescription>
          常用功能快捷入口
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-3">
          {quickActions.map((action) => (
            <Button
              key={action.title}
              variant={action.variant}
              className="h-auto p-4 justify-start"
              asChild
            >
              <Link href={action.href}>
                <div className="flex items-center gap-3">
                  <action.icon className="h-5 w-5" />
                  <div className="text-left">
                    <div className="font-medium">{action.title}</div>
                    <div className="text-xs text-muted-foreground">
                      {action.description}
                    </div>
                  </div>
                </div>
              </Link>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
