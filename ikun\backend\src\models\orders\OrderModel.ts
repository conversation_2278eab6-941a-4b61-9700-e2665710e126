/**
 * Order Model
 * 订单数据模型 - 支持多平台订单管理
 */

import { BaseModel, QueryOptions, PaginatedResult } from '@/models/base/BaseModel';
import { IRelationalModel, RelationType, RelationConfig } from '@/models/base/ModelInterface';
import { Order } from '@/types';
import { database } from '@/database/connection';
import { logger } from '@/utils/logger';
import { BadRequestError } from '@/middleware/error/errorHandler';

export class OrderModel extends BaseModel<Order> implements IRelationalModel<Order> {
  protected tableName = 'orders_updata';
  protected primaryKey = 'id';
  protected timestamps = true;
  
  protected fillable = [
    'order_number',
    'transaction_id',
    'store_name',
    'platform',
    'payment_time',
    'platform_order_status',
    'order_remarks',
    'ioss_number',
    'customer_id',
    'customer_name',
    'phone1',
    'phone2',
    'country',
    'country_en',
    'state_province',
    'city',
    'postal_code',
    'shipping_address',
    'email',
    'product_name_en',
    'product_sku',
    'product_quantity',
    'product_unit_price',
    'product_total_price',
    'shipping_fee',
    'currency',
    'product_image_url'
  ];

  protected hidden: string[] = [];

  // 定义关联关系
  protected relations: Record<string, RelationConfig> = {
    // 可以添加与用户、产品等的关联关系
  };

  /**
   * 获取关联关系配置
   */
  public getRelation(relationName: string): RelationConfig | undefined {
    return this.relations[relationName];
  }

  /**
   * 根据订单编号查找
   */
  public async findByOrderNumber(orderNumber: string): Promise<Order | null> {
    return await this.findOne({ order_number: orderNumber });
  }

  /**
   * 根据交易编号查找
   */
  public async findByTransactionId(transactionId: string): Promise<Order | null> {
    return await this.findOne({ transaction_id: transactionId });
  }

  /**
   * 根据平台获取订单
   */
  public async findByPlatform(platform: string, options: QueryOptions = {}): Promise<PaginatedResult<Order>> {
    const whereCondition = { platform, ...options.where };
    return await this.paginate({ ...options, where: whereCondition });
  }

  /**
   * 根据平台订单状态获取订单
   */
  public async findByPlatformStatus(status: string, options: QueryOptions = {}): Promise<PaginatedResult<Order>> {
    const whereCondition = { platform_order_status: status, ...options.where };
    return await this.paginate({ ...options, where: whereCondition });
  }

  /**
   * 根据客户ID获取订单
   */
  public async findByCustomer(customerId: string, options: QueryOptions = {}): Promise<PaginatedResult<Order>> {
    const whereCondition = { customer_id: customerId, ...options.where };
    return await this.paginate({ ...options, where: whereCondition });
  }

  /**
   * 检查订单编号是否存在
   */
  public async orderNumberExists(orderNumber: string, excludeId?: number): Promise<boolean> {
    if (excludeId) {
      const sql = `SELECT COUNT(*) as count FROM ${this.tableName} WHERE order_number = ? AND id != ?`;
      const result = await database.queryOne<{ count: number }>(sql, [orderNumber, excludeId]);
      return (result?.count || 0) > 0;
    }

    const result = await this.findOne({ order_number: orderNumber });
    return result !== null;
  }

  /**
   * 检查交易编号是否存在
   */
  public async transactionIdExists(transactionId: string, excludeId?: number): Promise<boolean> {
    if (excludeId) {
      const sql = `SELECT COUNT(*) as count FROM ${this.tableName} WHERE transaction_id = ? AND id != ?`;
      const result = await database.queryOne<{ count: number }>(sql, [transactionId, excludeId]);
      return (result?.count || 0) > 0;
    }

    const result = await this.findOne({ transaction_id: transactionId });
    return result !== null;
  }

  /**
   * 更新平台订单状态
   */
  public async updatePlatformStatus(id: number | string, status: string): Promise<Order> {
    const order = await this.update(id, { platform_order_status: status } as Partial<Order>);

    logger.info('Order platform status updated:', { orderId: id, status });
    return order;
  }

  /**
   * 批量更新平台订单状态
   */
  public async batchUpdatePlatformStatus(orderIds: Array<number | string>, status: string): Promise<void> {
    await this.transaction(async (connection) => {
      const placeholders = orderIds.map(() => '?').join(', ');
      const sql = `
        UPDATE ${this.tableName}
        SET platform_order_status = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id IN (${placeholders})
      `;

      await connection.execute(sql, [status, ...orderIds]);
    });

    logger.info('Orders platform status batch updated:', { count: orderIds.length, status });
  }

  /**
   * 搜索订单
   */
  public async searchOrders(query: string, options: QueryOptions = {}): Promise<PaginatedResult<Order>> {
    const { page = 1, limit = 20 } = options;
    const offset = (page - 1) * limit;

    const searchConditions = `
      (order_number LIKE ? OR transaction_id LIKE ? OR customer_id LIKE ? OR customer_name LIKE ? OR platform LIKE ? OR store_name LIKE ?)
    `;

    const searchParams = Array(6).fill(`%${query}%`);
    
    // 添加额外的WHERE条件
    let whereClause = `WHERE ${searchConditions}`;
    let params = [...searchParams];

    if (options.where) {
      const { whereClause: additionalWhere, params: additionalParams } = this.buildWhereClause(options.where);
      if (additionalWhere) {
        whereClause += ` AND ${additionalWhere.replace('WHERE ', '')}`;
        params.push(...additionalParams);
      }
    }

    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM ${this.tableName} ${whereClause}`;
    const countResult = await database.queryOne<{ total: number }>(countSql, params);
    const total = countResult?.total || 0;

    // 获取数据
    const orderBy = options.orderBy || 'created_at';
    const orderDirection = options.orderDirection || 'DESC';
    
    const sql = `
      SELECT * FROM ${this.tableName} 
      ${whereClause}
      ORDER BY ${orderBy} ${orderDirection}
      LIMIT ? OFFSET ?
    `;

    const items = await database.query<Order>(sql, [...params, limit, offset]);

    return {
      items: items.map(item => this.formatOutput(item)),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * 高级搜索订单
   */
  public async advancedSearch(filters: {
    query?: string;
    platform?: string;
    platform_order_status?: string;
    customer_id?: string;
    store_name?: string;
    country?: string;
    payment_start_date?: string;
    payment_end_date?: string;
    start_date?: string;
    end_date?: string;
  }, options: QueryOptions = {}): Promise<PaginatedResult<Order>> {
    const { page = 1, limit = 20 } = options;
    const offset = (page - 1) * limit;

    let whereConditions: string[] = [];
    let params: any[] = [];

    // 文本搜索
    if (filters.query) {
      whereConditions.push('(order_number LIKE ? OR transaction_id LIKE ? OR customer_id LIKE ? OR customer_name LIKE ?)');
      params.push(`%${filters.query}%`, `%${filters.query}%`, `%${filters.query}%`, `%${filters.query}%`);
    }

    // 平台筛选
    if (filters.platform) {
      whereConditions.push('platform = ?');
      params.push(filters.platform);
    }

    // 平台订单状态筛选
    if (filters.platform_order_status) {
      whereConditions.push('platform_order_status = ?');
      params.push(filters.platform_order_status);
    }

    // 客户筛选
    if (filters.customer_id) {
      whereConditions.push('customer_id = ?');
      params.push(filters.customer_id);
    }

    // 店铺筛选
    if (filters.store_name) {
      whereConditions.push('store_name = ?');
      params.push(filters.store_name);
    }

    // 国家筛选
    if (filters.country) {
      whereConditions.push('(country = ? OR country_en = ?)');
      params.push(filters.country, filters.country);
    }

    // 付款时间范围筛选
    if (filters.payment_start_date) {
      whereConditions.push('payment_time >= ?');
      params.push(filters.payment_start_date);
    }

    if (filters.payment_end_date) {
      whereConditions.push('payment_time <= ?');
      params.push(filters.payment_end_date);
    }

    // 创建时间范围筛选
    if (filters.start_date) {
      whereConditions.push('created_at >= ?');
      params.push(filters.start_date);
    }

    if (filters.end_date) {
      whereConditions.push('created_at <= ?');
      params.push(filters.end_date);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM ${this.tableName} ${whereClause}`;
    const countResult = await database.queryOne<{ total: number }>(countSql, params);
    const total = countResult?.total || 0;

    // 获取数据
    const orderBy = options.orderBy || 'created_at';
    const orderDirection = options.orderDirection || 'DESC';
    
    const sql = `
      SELECT * FROM ${this.tableName} 
      ${whereClause}
      ORDER BY ${orderBy} ${orderDirection}
      LIMIT ? OFFSET ?
    `;

    const items = await database.query<Order>(sql, [...params, limit, offset]);

    return {
      items: items.map(item => this.formatOutput(item)),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    };
  }

  /**
   * 获取订单统计信息
   */
  public async getOrderStatistics(filters: {
    platform?: string;
    start_date?: string;
    end_date?: string;
  } = {}): Promise<{
    total: number;
    by_status: Record<string, number>;
    by_platform: Record<string, number>;
    total_revenue: number;
    average_order_value: number;
  }> {
    let whereConditions: string[] = [];
    let params: any[] = [];

    if (filters.platform) {
      whereConditions.push('platform = ?');
      params.push(filters.platform);
    }

    if (filters.start_date) {
      whereConditions.push('created_at >= ?');
      params.push(filters.start_date);
    }

    if (filters.end_date) {
      whereConditions.push('created_at <= ?');
      params.push(filters.end_date);
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : '';

    // 总订单数和总收入
    const totalResult = await database.queryOne<{ total: number; revenue: number; avg_value: number }>(
      `SELECT COUNT(*) as total, COALESCE(SUM(total_amount), 0) as revenue, COALESCE(AVG(total_amount), 0) as avg_value FROM ${this.tableName} ${whereClause}`,
      params
    );

    // 按状态统计
    const statusStats = await database.query<{ status: string; count: number }>(
      `SELECT status, COUNT(*) as count FROM ${this.tableName} ${whereClause} GROUP BY status`,
      params
    );
    const by_status: Record<string, number> = {};
    statusStats.forEach(stat => {
      by_status[stat.status] = stat.count;
    });

    // 按平台统计
    const platformStats = await database.query<{ platform: string; count: number }>(
      `SELECT platform, COUNT(*) as count FROM ${this.tableName} ${whereClause} GROUP BY platform`,
      params
    );
    const by_platform: Record<string, number> = {};
    platformStats.forEach(stat => {
      by_platform[stat.platform] = stat.count;
    });

    return {
      total: totalResult?.total || 0,
      by_status,
      by_platform,
      total_revenue: totalResult?.revenue || 0,
      average_order_value: totalResult?.avg_value || 0
    };
  }

  /**
   * 同步平台订单
   */
  public async syncPlatformOrders(platform: string, orders: Partial<Order>[]): Promise<{
    created: number;
    updated: number;
    errors: Array<{ order: Partial<Order>; error: string }>;
  }> {
    let created = 0;
    let updated = 0;
    const errors: Array<{ order: Partial<Order>; error: string }> = [];

    await this.transaction(async (connection) => {
      for (const orderData of orders) {
        try {
          if (!(orderData as any).order_number) {
            throw new BadRequestError('Order number is required');
          }

          // 检查订单是否已存在
          const existingOrder = await this.findByOrderNumber((orderData as any).order_number);

          if (existingOrder) {
            // 更新现有订单
            await this.update(existingOrder.id, orderData);
            updated++;
          } else {
            // 创建新订单
            await this.create({ ...orderData, platform });
            created++;
          }
        } catch (error) {
          errors.push({
            order: orderData,
            error: (error as Error).message
          });
          
          logger.warn('Failed to sync order:', {
            orderNumber: (orderData as any).order_number,
            platform,
            error: (error as Error).message
          });
        }
      }
    });

    logger.info('Platform orders synced:', { platform, created, updated, errors: errors.length });

    return { created, updated, errors };
  }

  /**
   * 关联查询实现
   */
  public async with(relations: string[]): Promise<Order[]> {
    // TODO: 实现关联查询逻辑
    throw new Error('Relation loading not implemented yet');
  }

  public async findWithRelations(id: number | string, relations: string[]): Promise<Order | null> {
    // TODO: 实现单个记录的关联查询
    throw new Error('Relation loading not implemented yet');
  }

  /**
   * 格式化输出数据
   */
  protected formatOutput(data: Order): Order {
    const formatted = super.formatOutput(data);
    
    // shipping_address 现在是TEXT类型，不需要JSON解析
    // 如果将来需要JSON解析，可以在这里添加
    
    return formatted;
  }

  /**
   * 准备插入数据
   */
  protected prepareForInsert(data: Partial<Order>): Record<string, any> {
    const prepared = super.prepareForInsert(data);

    // shipping_address 现在是TEXT类型，直接存储字符串
    // 如果需要存储JSON对象，可以在这里序列化

    return prepared;
  }

  /**
   * 准备更新数据
   */
  protected prepareForUpdate(data: Partial<Order>): Record<string, any> {
    const prepared = super.prepareForUpdate(data);

    // shipping_address 现在是TEXT类型，直接存储字符串
    // 如果需要存储JSON对象，可以在这里序列化

    return prepared;
  }

  /**
   * 重写创建方法，添加验证
   */
  public async create(data: Partial<Order>): Promise<Order> {
    // 验证订单编号唯一性
    if ((data as any).order_number && await this.orderNumberExists((data as any).order_number)) {
      throw new BadRequestError(`Order number '${(data as any).order_number}' already exists`);
    }

    // 验证交易编号唯一性
    if ((data as any).transaction_id && await this.transactionIdExists((data as any).transaction_id)) {
      throw new BadRequestError(`Transaction ID '${(data as any).transaction_id}' already exists`);
    }

    return await super.create(data);
  }

  /**
   * 重写更新方法，添加验证
   */
  public async update(id: number | string, data: Partial<Order>): Promise<Order> {
    const current = await this.findByIdOrFail(id);

    // 订单编号唯一性验证（排除自身）
    if ((data as any).order_number && (data as any).order_number !== (current as any).order_number &&
        await this.orderNumberExists((data as any).order_number, Number(id))) {
      throw new BadRequestError(`Order number '${(data as any).order_number}' already exists`);
    }

    // 交易编号唯一性验证（排除自身）
    if ((data as any).transaction_id && (data as any).transaction_id !== (current as any).transaction_id &&
        await this.transactionIdExists((data as any).transaction_id, Number(id))) {
      throw new BadRequestError(`Transaction ID '${(data as any).transaction_id}' already exists`);
    }

    return await super.update(id, data);
  }
}

export default OrderModel;
