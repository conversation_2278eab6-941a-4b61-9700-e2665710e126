/**
 * Product Service
 * Business logic for product management
 */

import { database } from '@/database/connection';
import { Models, ProductModel } from '@/models';
import { uploadproductService } from '@/services/uploadproduct/uploadproductService';
import { storeService } from '@/services/stores/storeService';
import {
  DropshipProduct,
  CreateProductRequest,
  UpdateProductRequest,
  SearchQuery,
  PaginatedResponse
} from '@/types';
import { NotFoundError, ConflictError, BadRequestError } from '@/middleware/error/errorHandler';
import { logger } from '@/utils/logger';

class ProductService {
  private productModel: ProductModel = Models.Product() as ProductModel;

  // Get products with pagination, search and filters
  public async getProducts(query: SearchQuery & {
    status?: 'draft' | 'active' | 'inactive';
    category_id?: number;
    source?: string;
    start_date?: string;
    end_date?: string;
  }): Promise<PaginatedResponse<DropshipProduct>> {
    try {
      const {
        page = 1,
        limit = 20,
        search,
        status,
        category_id,
        source,
        start_date,
        end_date
      } = query;

      // 构建搜索参数
      const searchParams: any = {
        page: Number(page),
        limit: Number(limit)
      };

      if (search) {
        searchParams.search = search;
      }

      if (status) {
        searchParams.status = status;
      }

      if (category_id) {
        searchParams.category_id = Number(category_id);
      }

      if (source) {
        searchParams.source = source;
      }

      if (start_date) {
        searchParams.start_date = start_date;
      }

      if (end_date) {
        searchParams.end_date = end_date;
      }

      const result = await this.productModel.advancedSearch({
        query: search,
        status,
        category_id,
        source,
        start_date,
        end_date
      }, { page: Number(page), limit: Number(limit) });

      return result;
    } catch (error) {
      throw error;
    }
  }

  // Create new product
  public async createProduct(data: CreateProductRequest): Promise<DropshipProduct> {
    try {
      // 验证必填字段
      if (!data.sku || !data.ean || !data.english_title) {
        throw new BadRequestError('SKU, EAN, and English title are required');
      }

      // 检查SKU和EAN唯一性
      if (await this.productModel.skuExists(data.sku)) {
        throw new ConflictError(`SKU '${data.sku}' already exists`);
      }

      if (await this.productModel.eanExists(data.ean)) {
        throw new ConflictError(`EAN '${data.ean}' already exists`);
      }

      const product = await this.productModel.create(data);

      logger.info('Product created:', {
        productId: product.id,
        sku: product.sku,
        title: product.english_title
      });

      return product;
    } catch (error) {
      logger.error('Error creating product:', error);
      throw error;
    }
  }

  // Import multiple products
  public async importProducts(products: CreateProductRequest[]): Promise<DropshipProduct[]> {
    try {
      const result = await this.productModel.batchImport(products);

      logger.info('Products imported:', {
        total: products.length,
        success: result.length
      });

      return result;
    } catch (error) {
      logger.error('Error importing products:', error);
      throw error;
    }
  }

  // Get products by category
  public async getProductsByCategory(categoryId: number, query: SearchQuery): Promise<PaginatedResponse<DropshipProduct>> {
    try {
      const { page = 1, limit = 20 } = query;

      const result = await this.productModel.findByCategory(categoryId, {
        page: Number(page),
        limit: Number(limit)
      });

      logger.info('Products by category retrieved:', {
        categoryId,
        total: result.total,
        page: result.page
      });

      return result;
    } catch (error) {
      logger.error('Error getting products by category:', error);
      throw error;
    }
  }

  // Get product by ID
  public async getProductById(id: number): Promise<DropshipProduct> {
    try {
      const product = await this.productModel.findByIdOrFail(id);

      logger.info('Product retrieved:', { productId: id });

      return product;
    } catch (error) {
      logger.error('Error getting product by ID:', error);
      throw error;
    }
  }

  // Get product by SKU
  public async getProductBySku(sku: string): Promise<DropshipProduct | null> {
    try {
      const product = await this.productModel.findBySku(sku);

      if (product) {
        logger.info('Product retrieved by SKU:', { sku });
      }

      return product;
    } catch (error) {
      logger.error('Error getting product by SKU:', error);
      throw error;
    }
  }

  // Update product
  public async updateProduct(id: number, data: UpdateProductRequest): Promise<DropshipProduct> {
    try {
      // 检查产品是否存在
      await this.productModel.findByIdOrFail(id);

      // 如果更新SKU或EAN，检查唯一性
      if (data.sku && await this.productModel.skuExists(data.sku, id)) {
        throw new ConflictError(`SKU '${data.sku}' already exists`);
      }

      if (data.ean && await this.productModel.eanExists(data.ean, id)) {
        throw new ConflictError(`EAN '${data.ean}' already exists`);
      }

      const product = await this.productModel.update(id, data);

      logger.info('Product updated:', { productId: id });

      return product;
    } catch (error) {
      logger.error('Error updating product:', error);
      throw error;
    }
  }

  // Delete product
  public async deleteProduct(id: number): Promise<{
    success: boolean;
    message: string;
    error?: string;
    claimedInfo?: any[];
  }> {
    try {
      // 检查产品是否存在
      await this.productModel.findByIdOrFail(id);

      await this.productModel.delete(id);

      logger.info('Product deleted:', { productId: id });

      return {
        success: true,
        message: 'Product deleted successfully'
      };
    } catch (error: any) {
      // 检查是否是外键约束错误（产品被认领）
      if (error.code === 'ER_ROW_IS_REFERENCED_2') {
        // 查询认领信息
        let claimedInfo: any[] = [];
        try {
          claimedInfo = await this.getProductClaimedInfo(id);
        } catch (queryError) {
          logger.error('Error querying claimed info:', queryError);
          // 查询失败时使用空数组
        }

        logger.info('Product deletion blocked - claimed by stores:', { productId: id, claimedInfo });

        return {
          success: false,
          message: 'Cannot delete product that is claimed by stores',
          error: 'PRODUCT_CLAIMED',
          claimedInfo
        };
      }

      logger.error('Error deleting product:', error);
      throw error;
    }
  }

  // 获取产品认领信息
  private async getProductClaimedInfo(productId: number): Promise<any[]> {
    try {
      logger.info('Querying claimed info for product:', { productId });

      const query = `
        SELECT
          l.platform_code,
          COALESCE(s.store_name, 'Unknown Store') as store_name,
          COUNT(*) as listing_count
        FROM uploadproduct_listings l
        LEFT JOIN stores_updata s ON l.store_id = s.id
        WHERE l.dropship_product_id = ?
        GROUP BY l.platform_code, s.store_name
        ORDER BY l.platform_code, s.store_name
      `;

      const result = await database.query(query, [productId]);
      logger.info('Claimed info query result:', { productId, result });

      return result;
    } catch (error) {
      logger.error('Error getting product claimed info:', error);
      return [];
    }
  }

  // Batch claim products to multiple stores
  public async batchClaimProducts(productIds: number[], storeIds: number[]): Promise<{
    success_count: number;
    failed_count: number;
    details: Array<{ product_id: number; store_id: number; success: boolean; error?: string }>;
  }> {
    try {
      const results: Array<{ product_id: number; store_id: number; success: boolean; error?: string }> = [];
      let successCount = 0;
      let failedCount = 0;

      // 预先获取所有产品和店铺信息
      const products = new Map<number, DropshipProduct>();
      const stores = new Map<number, any>();

      // 验证并获取所有产品
      for (const productId of productIds) {
        try {
          const product = await this.productModel.findByIdOrFail(productId);
          products.set(productId, product);
        } catch (error) {
          // 如果产品不存在，为所有店铺记录失败
          for (const storeId of storeIds) {
            results.push({
              product_id: productId,
              store_id: storeId,
              success: false,
              error: `Product ${productId} not found`
            });
            failedCount++;
          }
        }
      }

      // 验证并获取所有店铺
      for (const storeId of storeIds) {
        try {
          const store = await storeService.getStoreById(storeId);
          stores.set(storeId, store);
        } catch (error) {
          // 如果店铺不存在，为所有产品记录失败
          for (const productId of productIds) {
            if (products.has(productId)) {
              results.push({
                product_id: productId,
                store_id: storeId,
                success: false,
                error: `Store ${storeId} not found`
              });
              failedCount++;
            }
          }
        }
      }

      // 执行批量认领
      for (const [productId, product] of products) {
        for (const [storeId, store] of stores) {
          try {
            // 检查是否已经存在相同的认领记录
            const existingListing = await this.checkExistingListing(productId, storeId, store.platform_code);
            if (existingListing) {
              results.push({
                product_id: productId,
                store_id: storeId,
                success: false,
                error: `Product already claimed to this store`
              });
              failedCount++;
              continue;
            }

            // 1. 更新产品的认领信息
            await this.productModel.claimProduct(productId, store.platform_code);

            // 2. 增加产品的刊登次数
            await this.productModel.incrementListingCount(productId);

            // 3. 在 uploadproduct_listings 表中创建认领记录
            const listingData = {
              dropship_product_id: productId,
              platform_code: store.platform_code,
              store_id: storeId,
              sku: product.sku,
              ean: product.ean,
              english_title: product.english_title,
              image1: product.image1,
              status: 'draft',
              translation_status: 'pending'
            };

            await uploadproductService.createListing(listingData);

            results.push({
              product_id: productId,
              store_id: storeId,
              success: true
            });
            successCount++;

          } catch (error) {
            results.push({
              product_id: productId,
              store_id: storeId,
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error'
            });
            failedCount++;
          }
        }
      }

      logger.info('Batch claim completed:', {
        totalOperations: results.length,
        successCount,
        failedCount
      });

      return {
        success_count: successCount,
        failed_count: failedCount,
        details: results
      };
    } catch (error) {
      logger.error('Error in batch claim products:', error);
      throw error;
    }
  }

  // 检查是否已存在认领记录
  private async checkExistingListing(productId: number, storeId: number, platformCode: string): Promise<boolean> {
    try {
      const sql = `
        SELECT COUNT(*) as count
        FROM uploadproduct_listings
        WHERE dropship_product_id = ? AND store_id = ? AND platform_code = ?
      `;
      const result = await database.queryOne<{ count: number }>(sql, [productId, storeId, platformCode]);
      return (result?.count || 0) > 0;
    } catch (error) {
      logger.error('Error checking existing listing:', error);
      return false;
    }
  }
}

export const productService = new ProductService();
