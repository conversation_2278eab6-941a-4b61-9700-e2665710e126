/** @type {import('next').NextConfig} */
const nextConfig = {
  // 禁用开发模式下的 React.StrictMode 以避免重复请求  非常重要
  //reactStrictMode: false,

  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: process.env.NODE_ENV === 'development'
          ? 'http://localhost:3001/api/:path*' // 开发环境后端地址
          : process.env.BACKEND_URL + '/api/:path*' // 生产环境后端地址
      }
    ]
  },
  images: {
    domains: [
      'localhost',
      's3.amazonaws.com',
      'cloudinary.com',
      'res.cloudinary.com'
    ]
  }
}

module.exports = nextConfig
