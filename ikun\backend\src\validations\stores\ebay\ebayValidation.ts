/**
 * eBay Store Validation Schemas
 * Joi validation rules for eBay store-related requests
 */

import <PERSON><PERSON> from 'joi';

export const ebayValidation = {
  // GET /api/v1/stores/ebay
  getStores: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    status: Joi.string().valid('active', 'inactive', 'suspended').optional(),
    site: Joi.string().trim().max(10).optional() // US, UK, DE, etc.
  }),

  // POST /api/v1/stores/ebay
  createStore: Joi.object({
    store_name: Joi.string().trim().min(1).max(200).required(),
    site: Joi.string().trim().min(2).max(10).required(), // US, UK, DE, etc.
    user_token: Joi.string().trim().min(1).max(500).required(),
    app_id: Joi.string().trim().min(1).max(100).required(),
    dev_id: Joi.string().trim().min(1).max(100).required(),
    cert_id: Joi.string().trim().min(1).max(100).required(),
    status: Joi.string().valid('active', 'inactive').default('active'),
    description: Joi.string().trim().max(1000).optional().allow('')
  }),

  // PUT /api/v1/stores/ebay/:id
  updateStore: Joi.object({
    store_name: Joi.string().trim().min(1).max(200).optional(),
    user_token: Joi.string().trim().min(1).max(500).optional(),
    app_id: Joi.string().trim().min(1).max(100).optional(),
    dev_id: Joi.string().trim().min(1).max(100).optional(),
    cert_id: Joi.string().trim().min(1).max(100).optional(),
    status: Joi.string().valid('active', 'inactive', 'suspended').optional(),
    description: Joi.string().trim().max(1000).optional().allow('')
  }),

  // GET /api/v1/stores/ebay/:id/listings
  getStoreListings: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    status: Joi.string().valid('active', 'ended', 'sold').optional(),
    search: Joi.string().trim().max(255).optional()
  }),

  // POST /api/v1/stores/ebay/:id/listings/create
  createListings: Joi.object({
    product_ids: Joi.array().items(
      Joi.number().integer().positive().required()
    ).min(1).max(50).required(),
    listing_options: Joi.object({
      listing_type: Joi.string().valid('FixedPriceItem', 'Auction').default('FixedPriceItem'),
      duration: Joi.string().valid('Days_1', 'Days_3', 'Days_5', 'Days_7', 'Days_10', 'Days_30', 'GTC').default('GTC'),
      category_id: Joi.string().trim().max(20).required(),
      condition: Joi.string().valid('New', 'Used', 'Refurbished').default('New'),
      shipping_policy: Joi.string().trim().max(100).optional(),
      return_policy: Joi.string().trim().max(100).optional(),
      markup_percentage: Joi.number().min(0).max(1000).optional()
    }).required()
  }),

  // Common parameter validation
  storeId: Joi.object({
    id: Joi.number().integer().positive().required()
  })
};
