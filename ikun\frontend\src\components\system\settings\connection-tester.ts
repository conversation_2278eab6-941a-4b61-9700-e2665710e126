/**
 * OpenAI兼容API连接测试工具
 * 支持DeepSeek火山引擎和其他OpenAI兼容服务的连接测试
 */

import { apiClient } from '@/lib/api'

export interface ConnectionTestParams {
  baseUrl: string
  apiKey: string
  modelId: string
  timeout?: number
}

export interface ConnectionTestResult {
  success: boolean
  message: string
  responseTime?: number
  error?: string
  details?: any
  tokenUsage?: {
    inputTokens: number
    outputTokens: number
    totalTokens: number
  }
}

/**
 * 测试OpenAI兼容API连接
 */
export async function testOpenAICompatibleConnection(params: ConnectionTestParams): Promise<ConnectionTestResult> {
  const startTime = Date.now()
  
  try {
    // 验证参数
    if (!params.baseUrl || !params.apiKey || !params.modelId) {
      return {
        success: false,
        message: '缺少必要参数：baseUrl、apiKey 或 modelId'
      }
    }

    // 构建请求URL
    const url = params.baseUrl.endsWith('/') 
      ? `${params.baseUrl}chat/completions`
      : `${params.baseUrl}/chat/completions`

    // 构建请求体
    const requestBody = {
      model: params.modelId,
      messages: [
        {
          role: "system",
          content: "You are a helpful assistant"
        },
        {
          role: "user", 
          content: "Hello, this is a connection test. Please respond with 'Connection successful'."
        }
      ],
      max_tokens: 50,
      temperature: 0.1,
      stream: false
    }

    // 发送测试请求
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${params.apiKey}`
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(params.timeout || 30000) // 30秒超时
    })

    const responseTime = Date.now() - startTime

    if (!response.ok) {
      const errorText = await response.text()
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`
      
      try {
        const errorData = JSON.parse(errorText)
        if (errorData.error?.message) {
          errorMessage = errorData.error.message
        }
      } catch {
        // 如果不是JSON格式，使用原始错误文本
        if (errorText) {
          errorMessage = errorText
        }
      }

      return {
        success: false,
        message: `连接失败: ${errorMessage}`,
        responseTime,
        error: errorMessage
      }
    }

    const data = await response.json()

    // 检查响应格式
    if (!data.choices || !Array.isArray(data.choices) || data.choices.length === 0) {
      return {
        success: false,
        message: '连接成功但响应格式异常：缺少choices字段',
        responseTime,
        details: data
      }
    }

    const assistantMessage = data.choices[0]?.message?.content

    // 提取Token使用信息
    const tokenUsage = data.usage ? {
      inputTokens: data.usage.prompt_tokens || 0,
      outputTokens: data.usage.completion_tokens || 0,
      totalTokens: data.usage.total_tokens || 0
    } : undefined

    return {
      success: true,
      message: `连接成功！响应时间: ${responseTime}ms${tokenUsage ? ` | Token使用: ${tokenUsage.totalTokens}` : ''}`,
      responseTime,
      tokenUsage,
      details: {
        model: data.model,
        usage: data.usage,
        response: assistantMessage
      }
    }

  } catch (error: any) {
    const responseTime = Date.now() - startTime
    
    if (error.name === 'AbortError' || error.name === 'TimeoutError') {
      return {
        success: false,
        message: `连接超时 (${responseTime}ms)`,
        responseTime,
        error: 'Timeout'
      }
    }

    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      return {
        success: false,
        message: '网络连接失败，请检查URL是否正确',
        responseTime,
        error: 'Network Error'
      }
    }

    return {
      success: false,
      message: `连接失败: ${error.message}`,
      responseTime,
      error: error.message
    }
  }
}

/**
 * 测试DeepSeek火山引擎连接
 */
export async function testDeepSeekConnection(params: ConnectionTestParams): Promise<ConnectionTestResult> {
  // DeepSeek火山引擎使用OpenAI兼容格式，直接调用通用测试函数
  return testOpenAICompatibleConnection(params)
}

/**
 * 将Token使用情况发送到后端进行统计
 */
async function recordTokenUsage(
  providerType: 'deepseek_huoshan' | 'with_openai',
  modelName: string,
  tokenUsage: { inputTokens: number; outputTokens: number; totalTokens: number }
): Promise<void> {
  try {
    const provider = providerType === 'deepseek_huoshan' ? 'deepseek_huoshan' : 'openai_compatible'

    await apiClient.post('/system/settings/token-usage/increment', {
      provider,
      modelName,
      inputTokens: tokenUsage.inputTokens,
      outputTokens: tokenUsage.outputTokens
    })
  } catch (error) {
    console.error('Failed to record token usage:', error)
    // 不抛出错误，避免影响连接测试的主要功能
  }
}

/**
 * 根据提供商类型选择合适的测试函数
 */
export async function testProviderConnection(
  providerType: 'deepseek_huoshan' | 'with_openai',
  params: ConnectionTestParams,
  modelName?: string
): Promise<ConnectionTestResult> {
  let result: ConnectionTestResult

  switch (providerType) {
    case 'deepseek_huoshan':
      result = await testDeepSeekConnection(params)
      break
    case 'with_openai':
      result = await testOpenAICompatibleConnection(params)
      break
    default:
      return {
        success: false,
        message: `不支持的提供商类型: ${providerType}`
      }
  }

  // 如果连接成功且有Token使用信息，记录到后端
  if (result.success && result.tokenUsage && modelName) {
    await recordTokenUsage(providerType, modelName, result.tokenUsage)
  }

  return result
}
