# IKUN ERP 全局时间规范

## 📋 概述

本文档规定了IKUN ERP系统中所有时间处理的统一标准，确保前端、后端、数据库在时间处理上的一致性。

## 🌍 时区标准

### 统一时区
- **全局时区**: 北京时间 (UTC+08:00)
- **数据库时区**: +08:00
- **API响应时区**: +08:00
- **前端显示时区**: 北京时间

### 禁止使用
- ❌ UTC时间 (除非特殊说明)
- ❌ 本地时间 (避免时区混乱)
- ❌ 不带时区信息的时间戳

## 🔧 技术实现

### 后端时间处理

#### 1. 时间工具函数 (`src/utils/time.ts`)
```typescript
// 获取北京时间的Date对象
export function getBeijingTime(): Date

// 获取北京时间的ISO字符串 (带+08:00时区标识)
export function getBeijingTimeISO(): string

// 获取格式化的北京时间字符串
export function getBeijingTimeFormatted(): string

// UTC时间转北京时间
export function utcToBeijingTime(utcDate: Date): Date

// 北京时间转UTC时间
export function beijingToUtcTime(beijingDate: Date): Date
```

#### 2. API响应格式
```json
{
  "code": 200,
  "message": "Success",
  "data": {...},
  "timestamp": "2025-06-20T16:37:17.109+08:00"
}
```

#### 3. 数据库配置
```typescript
database: {
  timezone: '+08:00'  // 北京时间
}
```

#### 4. 模型时间戳
```typescript
// BaseModel.ts
protected prepareForInsert(data: Partial<T>): Record<string, any> {
  const prepared = { ...data };
  
  if (this.timestamps) {
    const beijingTime = getBeijingTime();
    (prepared as any).created_at = beijingTime;
    (prepared as any).updated_at = beijingTime;
  }
  
  return prepared as Record<string, any>;
}
```

### 前端时间处理

#### 1. 时间显示工具 (`src/lib/utils.ts`)
```typescript
export function formatDate(date: string | Date) {
  return new Intl.DateTimeFormat("zh-CN", {
    year: "numeric",
    month: "2-digit", 
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    timeZone: "Asia/Shanghai"  // 明确指定北京时间
  }).format(new Date(date))
}
```

#### 2. API请求时间参数
```typescript
// 发送时间参数时，确保使用北京时间
const params = {
  start_date: new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' }),
  end_date: new Date().toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })
}
```

## 📝 开发规范

### 强制规则

#### 1. API响应时间戳
- ✅ **必须使用**: `getBeijingTimeISO()` 生成时间戳
- ❌ **禁止使用**: `new Date().toISOString()`

```typescript
// ✅ 正确
res.json({
  code: 200,
  message: 'Success',
  data: result,
  timestamp: getBeijingTimeISO()
});

// ❌ 错误
res.json({
  code: 200,
  message: 'Success', 
  data: result,
  timestamp: new Date().toISOString()  // 这是UTC时间
});
```

#### 2. 数据库时间字段
- ✅ **必须使用**: `getBeijingTime()` 生成时间
- ❌ **禁止使用**: `new Date()`

```typescript
// ✅ 正确
const product = await this.update(id, {
  claim_time: getBeijingTime(),
  claim_platform: platform
});

// ❌ 错误
const product = await this.update(id, {
  claim_time: new Date(),  // 可能是本地时间
  claim_platform: platform
});
```

#### 3. 前端时间显示
- ✅ **必须指定**: `timeZone: "Asia/Shanghai"`
- ❌ **禁止使用**: 不指定时区的格式化

```typescript
// ✅ 正确
const formatTime = (date: string) => {
  return new Intl.DateTimeFormat("zh-CN", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit", 
    hour: "2-digit",
    minute: "2-digit",
    timeZone: "Asia/Shanghai"
  }).format(new Date(date));
};

// ❌ 错误
const formatTime = (date: string) => {
  return new Date(date).toLocaleString();  // 使用本地时区
};
```

## 🔍 测试验证

### 时间一致性检查
1. **API响应检查**: 确保所有API返回的timestamp包含`+08:00`
2. **数据库检查**: 确保created_at和updated_at字段使用北京时间
3. **前端显示检查**: 确保页面显示的时间与服务器时间一致

### 测试用例
```bash
# 1. 检查API响应时间格式
curl http://localhost:3001/api/v1/auth/login
# 期望: "timestamp": "2025-06-20T16:37:17.109+08:00"

# 2. 检查数据库时区设置
SELECT NOW(), UTC_TIMESTAMP();
# 期望: NOW()比UTC_TIMESTAMP()快8小时

# 3. 检查前端时间显示
# 期望: 页面显示时间与北京时间一致
```

## 📚 常见问题

### Q1: 为什么不使用UTC时间？
A: 虽然UTC是国际标准，但考虑到：
- 用户主要在中国，使用北京时间更直观
- 避免前端时区转换的复杂性
- 减少时区相关的bug

### Q2: 如何处理不同时区的用户？
A: 当前版本统一使用北京时间，未来如需支持多时区：
- 在用户表中添加timezone字段
- API响应根据用户时区返回
- 前端根据用户设置显示时间

### Q3: 数据库迁移时如何处理时间？
A: 
- 确保目标数据库时区设置为+08:00
- 迁移脚本中明确指定时区
- 迁移后验证时间数据的正确性

## 🔄 更新记录

- **2025-06-20**: 初始版本，统一全局时间规范
- **2025-06-20**: 修复SQL查询和时间戳问题，添加预防措施

---

**重要提醒**: 所有开发人员必须严格遵守此规范，任何时间相关的代码修改都必须符合本文档的要求。
