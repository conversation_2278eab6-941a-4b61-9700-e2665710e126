'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Loader2, <PERSON>freshCw, BarChart3, TrendingUp, Activity } from 'lucide-react'
import { useSystemSettings, TokenStatistics, TokenModelStats } from '@/hooks/useSystemSettings'
import { toast } from 'sonner'

// 格式化数字显示
const formatNumber = (num: number): string => {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

// 模型统计卡片组件
interface ModelStatsCardProps {
  modelName: string
  stats: TokenModelStats
  provider: string
}

function ModelStatsCard({ modelName, stats, provider }: ModelStatsCardProps) {
  return (
    <Card className="h-full">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">{modelName}</CardTitle>
          <Badge variant="outline" className="text-xs">
            {provider === 'deepseek_huoshan' ? 'DeepSeek' : 'OpenAI兼容'}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="grid grid-cols-2 gap-3">
          <div className="space-y-1">
            <p className="text-xs text-muted-foreground">今日输入</p>
            <p className="text-sm font-medium">{formatNumber(stats.today_input_tokens)}</p>
          </div>
          <div className="space-y-1">
            <p className="text-xs text-muted-foreground">今日输出</p>
            <p className="text-sm font-medium">{formatNumber(stats.today_output_tokens)}</p>
          </div>
          <div className="space-y-1">
            <p className="text-xs text-muted-foreground">昨日输入</p>
            <p className="text-sm font-medium text-muted-foreground">{formatNumber(stats.yesterday_input_tokens)}</p>
          </div>
          <div className="space-y-1">
            <p className="text-xs text-muted-foreground">昨日输出</p>
            <p className="text-sm font-medium text-muted-foreground">{formatNumber(stats.yesterday_output_tokens)}</p>
          </div>
        </div>
        <div className="pt-2 border-t">
          <div className="grid grid-cols-2 gap-3">
            <div className="space-y-1">
              <p className="text-xs text-muted-foreground">总输入</p>
              <p className="text-sm font-semibold">{formatNumber(stats.total_input_tokens)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-xs text-muted-foreground">总输出</p>
              <p className="text-sm font-semibold">{formatNumber(stats.total_output_tokens)}</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function SystemTokenStatistics() {
  const { fetchTokenStatistics, resetDailyTokenStats } = useSystemSettings()
  const [statistics, setStatistics] = useState<TokenStatistics | null>(null)
  const [loading, setLoading] = useState(false)
  const [resetting, setResetting] = useState(false)

  // 加载Token统计数据
  const loadStatistics = async () => {
    try {
      setLoading(true)
      const data = await fetchTokenStatistics()
      setStatistics(data)
    } catch (error) {
      console.error('加载Token统计失败:', error)
    } finally {
      setLoading(false)
    }
  }

  // 重置今日统计
  const handleResetDaily = async () => {
    try {
      setResetting(true)
      await resetDailyTokenStats()
      await loadStatistics() // 重新加载数据
    } catch (error) {
      console.error('重置今日统计失败:', error)
    } finally {
      setResetting(false)
    }
  }

  // 初始化加载数据
  useEffect(() => {
    loadStatistics()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>加载Token统计中...</span>
      </div>
    )
  }

  if (!statistics) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center">
          <p className="text-muted-foreground mb-4">暂无Token统计数据</p>
          <Button onClick={loadStatistics}>
            <RefreshCw className="h-4 w-4 mr-2" />
            重新加载
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* 全局统计 */}
      <Card>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                全局统计
              </CardTitle>
              <CardDescription className="text-sm">
                所有翻译服务的总体使用情况
              </CardDescription>
            </div>
            <div className="flex space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={loadStatistics}
                disabled={loading}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                刷新
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleResetDaily}
                disabled={resetting}
              >
                {resetting ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <Activity className="h-4 w-4 mr-2" />
                )}
                重置今日
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {formatNumber(statistics.global_statistics.total_input_tokens)}
              </div>
              <p className="text-sm text-muted-foreground">总输入Token</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {formatNumber(statistics.global_statistics.total_output_tokens)}
              </div>
              <p className="text-sm text-muted-foreground">总输出Token</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {formatNumber(statistics.global_statistics.total_api_calls)}
              </div>
              <p className="text-sm text-muted-foreground">总API调用次数</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* DeepSeek火山引擎模型统计 */}
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="text-lg flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            DeepSeek火山引擎
          </CardTitle>
        </CardHeader>
        <CardContent>
          {Object.keys(statistics.deepseek_huoshan.models).length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Object.entries(statistics.deepseek_huoshan.models).map(([modelName, stats]) => (
                <ModelStatsCard
                  key={modelName}
                  modelName={modelName}
                  stats={stats}
                  provider="deepseek_huoshan"
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              暂无DeepSeek模型使用记录
            </div>
          )}
        </CardContent>
      </Card>

      {/* OpenAI兼容模型统计 */}
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="text-lg flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            OpenAI兼容模型
          </CardTitle>
        </CardHeader>
        <CardContent>
          {Object.keys(statistics.openai_compatible.models).length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Object.entries(statistics.openai_compatible.models).map(([modelName, stats]) => (
                <ModelStatsCard
                  key={modelName}
                  modelName={modelName}
                  stats={stats}
                  provider="openai_compatible"
                />
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-muted-foreground">
              暂无OpenAI兼容模型使用记录
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
