/**
 * Models Index
 * 统一导出所有数据模型
 */

// Base Models
export { BaseModel } from './base/BaseModel';
export type { QueryOptions, PaginatedResult } from './base/BaseModel';
export * from './base/ModelInterface';

// Product Models
import CategoryModel from './products/CategoryModel';
import ProductModel from './products/ProductModel';
export { CategoryModel, ProductModel };

// User Models
import UserModel from './users/UserModel';
export { UserModel };

// Store Models
export { StoreModel } from './stores/StoreModel';
export type { BaseStore } from './stores/StoreModel';
import EbayStoreModel from './stores/ebay/EbayStoreModel';
export type { EbayStore } from './stores/ebay/EbayStoreModel';

// Order Models
import OrderModel from './orders/OrderModel';
export { OrderModel };

// Model Factory - 用于动态创建模型实例
export class ModelFactory {
  private static instances: Map<string, any> = new Map();

  /**
   * 获取模型实例（单例模式）
   */
  public static getInstance<T>(modelClass: new () => T): T {
    const className = modelClass.name;
    
    if (!this.instances.has(className)) {
      this.instances.set(className, new modelClass());
    }
    
    return this.instances.get(className);
  }

  /**
   * 清除所有实例缓存
   */
  public static clearInstances(): void {
    this.instances.clear();
  }
}

// 便捷的模型实例获取器
export const Models = {
  Category: () => ModelFactory.getInstance(CategoryModel),
  Product: () => ModelFactory.getInstance(ProductModel),
  User: () => ModelFactory.getInstance(UserModel),
  EbayStore: () => ModelFactory.getInstance(EbayStoreModel),
  Order: () => ModelFactory.getInstance(OrderModel)
};

export default Models;
