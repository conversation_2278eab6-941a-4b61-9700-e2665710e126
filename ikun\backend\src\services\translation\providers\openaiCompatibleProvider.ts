/**
 * OpenAI 兼容翻译服务提供商
 * 支持 DeepSeek火山引擎 和 其他OpenAI兼容服务
 */

import axios, { AxiosInstance } from 'axios';
import { logger } from '@/utils/logger';
import {
  TranslationProviderConfig,
  LanguageCode
} from '../types';

// OpenAI 兼容 API 请求接口
interface OpenAIMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

interface OpenAIRequest {
  model: string;
  messages: OpenAIMessage[];
  max_tokens?: number;
  temperature?: number;
  stream?: boolean;
}

interface OpenAIResponse {
  id: string;
  object: string;
  created: number;
  model: string;
  choices: Array<{
    index: number;
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

// 模型配置接口
interface ModelConfig {
  baseUrl: string;
  apiKey: string;
  modelId: string;
  modelName: string; // 用于日志和统计
}

export class OpenAICompatibleProvider {
  public readonly name: string;
  public readonly config: TranslationProviderConfig;
  private httpClient: AxiosInstance;
  private modelConfig: ModelConfig;

  constructor(
    config: TranslationProviderConfig,
    private providerType: 'deepseek_huoshan' | 'with_openai',
    private subService: string,
    private dbConfig: any // 完整的数据库配置
  ) {
    this.name = `OpenAI兼容-${subService}`;
    this.config = config;

    // 根据配置类型和子服务获取模型配置
    this.modelConfig = this.extractModelConfig();

    // 创建 HTTP 客户端
    this.httpClient = axios.create({
      baseURL: this.modelConfig.baseUrl,
      timeout: config.timeout || 60000,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.modelConfig.apiKey}`
      },
      maxRedirects: 5
    });
  }

  /**
   * 从数据库配置中提取模型配置
   */
  private extractModelConfig(): ModelConfig {
    if (this.providerType === 'deepseek_huoshan') {
      // 从 one_model 或 mult_models 中查找指定的模型
      const allModels = [
        ...(this.dbConfig.one_model || []),
        ...(this.dbConfig.mult_models || [])
      ];
      
      const model = allModels.find(m => m.models_name === this.subService);
      if (!model) {
        throw new Error(`DeepSeek模型 ${this.subService} 未找到`);
      }
      
      return {
        baseUrl: this.dbConfig.baseUrl,
        apiKey: model.apikey,
        modelId: model.models_id,
        modelName: model.models_name
      };
    } else if (this.providerType === 'with_openai') {
      // 从 providers 中查找指定的提供商
      const provider = this.dbConfig.providers?.find((p: any) => p.models_name === this.subService);
      if (!provider) {
        throw new Error(`OpenAI兼容提供商 ${this.subService} 未找到`);
      }
      
      return {
        baseUrl: provider.baseUrl,
        apiKey: provider.apikey,
        modelId: provider.models_id,
        modelName: provider.models_name
      };
    }
    
    throw new Error(`不支持的提供商类型: ${this.providerType}`);
  }

  /**
   * 验证文本是否有效
   */
  private validateText(text: string): boolean {
    return Boolean(text && text.trim().length > 0);
  }

  /**
   * 验证语言代码是否支持
   */
  private validateLanguageCode(lang: LanguageCode): boolean {
    const supportedLanguages: LanguageCode[] = ['en', 'zh', 'lt', 'lv', 'et', 'fi', 'pt', 'es'];
    return supportedLanguages.includes(lang);
  }

  /**
   * 清理翻译结果
   */
  private cleanTranslationResult(text: string): string {
    return text.trim();
  }

  /**
   * 重试机制
   */
  private async withRetry<T>(operation: () => Promise<T>, maxRetries: number = 3): Promise<T> {
    let lastError: any;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error;
        if (attempt < maxRetries) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 10000);
          logger.warn(`${this.name} 第${attempt}次尝试失败，${delay}ms后重试:`, error);
          await this.sleep(delay);
        }
      }
    }

    throw lastError;
  }

  /**
   * 延迟函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 翻译文本 - 用于表单翻译（标题/描述/卖点）
   * 支持一次性翻译成多种语言
   */
  async translateText(
    text: string,
    sourceLang: LanguageCode,
    targetLangs: LanguageCode[],
    contentType: 'title' | 'description' | 'selling_point'
  ): Promise<{
    success: boolean;
    translations?: Record<string, string>;
    error?: string;
    tokenUsage?: {
      inputTokens: number;
      outputTokens: number;
      totalTokens: number;
    };
  }> {
    try {
      // 验证输入
      if (!this.validateText(text)) {
        return {
          success: false,
          error: '翻译文本不能为空'
        };
      }

      if (!this.validateLanguageCode(sourceLang)) {
        return {
          success: false,
          error: '不支持的源语言代码'
        };
      }

      for (const targetLang of targetLangs) {
        if (!this.validateLanguageCode(targetLang)) {
          return {
            success: false,
            error: `不支持的目标语言代码: ${targetLang}`
          };
        }
      }

      // 构建多语言翻译提示词
      const promptData = this.buildTranslationPrompt_text(
        text,
        sourceLang,
        targetLangs,
        contentType
      );

      // 调用 OpenAI 兼容 API
      const result = await this.callOpenAIAPI(promptData.systemPrompt, promptData.userInput);

      if (!result.success) {
        return {
          success: false,
          error: result.error,
          tokenUsage: result.tokenUsage
        };
      }

      // 解析多语言翻译结果
      const translations = this.parseMultiLanguageResult(result.text!, targetLangs);

      if (!translations) {
        return {
          success: false,
          error: '翻译结果解析失败',
          tokenUsage: result.tokenUsage
        };
      }

      // 记录 Token 使用统计
      if (result.tokenUsage) {
        await this.recordTokenUsage(result.tokenUsage);
      }

      return {
        success: true,
        translations,
        tokenUsage: result.tokenUsage
      };

    } catch (error) {
      logger.error(`${this.name} translation failed:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '翻译服务异常'
      };
    }
  }



  /**
   * 构建多语言翻译提示词 - 用于表单翻译
   */
  private buildTranslationPrompt_text(
    text: string,
    sourceLang: LanguageCode,
    targetLangs: LanguageCode[],
    contentType: 'title' | 'description' | 'selling_point'
  ): {
    systemPrompt: string;
    userInput: string;
  } {
    const languageNames = {
      'en': 'English',
      'zh': 'Chinese',
      'lt': 'Lithuanian',
      'lv': 'Latvian',
      'et': 'Estonian',
      'fi': 'Finnish',
      'pt': 'Portuguese',
      'es': 'Spanish'
    };

    const contentTypeNames = {
      'title': '产品标题',
      'description': '产品描述',
      'selling_point': '产品卖点'
    };

    const sourceLanguage = languageNames[sourceLang] || sourceLang;
    const targetLanguageList = targetLangs.map(lang => languageNames[lang] || lang).join('、');
    const contentTypeName = contentTypeNames[contentType] || contentType;

    // 构建示例JSON格式
    const exampleJson = targetLangs.reduce((acc, lang) => {
      acc[lang.toUpperCase()] = `翻译后的${contentTypeName}`;
      return acc;
    }, {} as Record<string, string>);

    const systemPrompt = `你是优秀的电商翻译专家。请将输入的${sourceLanguage} ${contentTypeName}翻译成${targetLanguageList}语言，并以JSON格式输出。

示例输出格式：${JSON.stringify(exampleJson, null, 2)}

请确保输出的是可解析的JSON格式，不要添加任何其他说明文字。`;

    const userInput = text;

    return {
      systemPrompt,
      userInput
    };
  }



  /**
   * 解析产品翻译结果
   */
  private parseProductTranslationResult(text: string): {
    title: string;
    description: string;
    sellingPoints: string[];
  } | null {
    try {
      // 清理可能的markdown代码块标记
      let cleanText = text.trim();
      if (cleanText.startsWith('```json')) {
        cleanText = cleanText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanText.startsWith('```')) {
        cleanText = cleanText.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      const parsed = JSON.parse(cleanText);

      // 验证必需字段
      if (!parsed.title || !parsed.description || !Array.isArray(parsed.sellingPoints)) {
        logger.error('产品翻译结果格式不正确:', parsed);
        return null;
      }

      return {
        title: String(parsed.title).trim(),
        description: String(parsed.description).trim(),
        sellingPoints: parsed.sellingPoints.map((point: any) => String(point).trim())
      };
    } catch (error) {
      logger.error('解析产品翻译结果失败:', { text, error });
      return null;
    }
  }

  /**
   * 解析多语言翻译结果
   */
  private parseMultiLanguageResult(text: string, targetLangs: LanguageCode[]): Record<string, string> | null {
    try {
      // 清理可能的markdown代码块标记
      let cleanText = text.trim();
      if (cleanText.startsWith('```json')) {
        cleanText = cleanText.replace(/^```json\s*/, '').replace(/\s*```$/, '');
      } else if (cleanText.startsWith('```')) {
        cleanText = cleanText.replace(/^```\s*/, '').replace(/\s*```$/, '');
      }

      const parsed = JSON.parse(cleanText);

      // 验证返回的语言是否匹配
      const result: Record<string, string> = {};
      for (const lang of targetLangs) {
        const langKey = lang.toUpperCase();
        if (parsed[langKey]) {
          result[langKey] = String(parsed[langKey]).trim();
        } else {
          logger.warn(`缺少语言 ${langKey} 的翻译结果`);
          return null;
        }
      }

      return result;
    } catch (error) {
      logger.error('解析多语言翻译结果失败:', { text, error });
      return null;
    }
  }

  /**
   * 调用 OpenAI 兼容 API
   */
  private async callOpenAIAPI(systemPrompt: string, userInput: string): Promise<{
    success: boolean;
    text?: string;
    error?: string;
    tokenUsage?: {
      inputTokens: number;
      outputTokens: number;
      totalTokens: number;
    };
  }> {
    return await this.withRetry(async () => {
      const requestData: OpenAIRequest = {
        model: this.modelConfig.modelId,
        messages: [
          {
            role: 'system',
            content: systemPrompt
          },
          {
            role: 'user',
            content: userInput
          }
        ],
        max_tokens: 2000,
        temperature: 0.1,
        stream: false
      };

      // 打印详细的请求信息
      logger.info(`${this.name} 📤 API请求详情:`, {
        url: this.modelConfig.baseUrl + '/chat/completions',
        model: this.modelConfig.modelId,
        providerType: this.providerType,
        subService: this.modelConfig.modelName,
        systemPromptLength: systemPrompt.length,
        userInputLength: userInput.length,
        requestData: {
          model: requestData.model,
          messages: requestData.messages,
          max_tokens: requestData.max_tokens,
          temperature: requestData.temperature,
          stream: requestData.stream
        }
      });

      // 打印完整的提示词内容
      logger.info(`${this.name} 📝 提示词内容:`, {
        systemPrompt: systemPrompt,
        userInput: userInput
      });

      const response = await this.httpClient.post('/chat/completions', requestData);

      if (response.status !== 200) {
        throw new Error(`API请求失败: HTTP ${response.status}`);
      }

      const data: OpenAIResponse = response.data;

      // 打印完整的API响应
      logger.info(`${this.name} 📥 API响应详情:`, {
        status: response.status,
        statusText: response.statusText,
        responseData: {
          id: data.id,
          object: data.object,
          created: data.created,
          model: data.model,
          choices: data.choices,
          usage: data.usage
        }
      });

      // 验证响应格式
      if (!data.choices || !Array.isArray(data.choices) || data.choices.length === 0) {
        logger.error(`${this.name} ❌ API响应格式异常:`, {
          choices: data.choices,
          responseData: data
        });
        throw new Error('API响应格式异常：缺少choices字段');
      }

      const translatedText = data.choices[0]?.message?.content;
      if (!translatedText) {
        logger.error(`${this.name} ❌ 翻译结果为空:`, {
          choice: data.choices[0],
          message: data.choices[0]?.message
        });
        throw new Error('翻译结果为空');
      }

      // 打印翻译结果内容
      logger.info(`${this.name} 📄 翻译结果内容:`, {
        translatedText: translatedText,
        textLength: translatedText.length,
        finishReason: data.choices[0]?.finish_reason
      });

      // 提取 Token 使用信息
      const tokenUsage = data.usage ? {
        inputTokens: data.usage.prompt_tokens || 0,
        outputTokens: data.usage.completion_tokens || 0,
        totalTokens: data.usage.total_tokens || 0
      } : undefined;

      logger.info(`${this.name} 📊 Token使用统计:`, {
        model: data.model,
        tokenUsage,
        providerType: this.providerType,
        modelName: this.modelConfig.modelName
      });

      return {
        success: true,
        text: this.cleanTranslationResult(translatedText),
        tokenUsage
      };
    });
  }

  /**
   * 记录 Token 使用统计
   */
  private async recordTokenUsage(tokenUsage: {
    inputTokens: number;
    outputTokens: number;
    totalTokens: number;
  }): Promise<void> {
    try {
      // 导入系统设置服务
      const { SystemSettingService } = await import('@/services/system/systemSettingService');
      const systemSettingService = new SystemSettingService();

      // 确定提供商类型
      const provider = this.providerType === 'deepseek_huoshan' ? 'deepseek_huoshan' : 'openai_compatible';

      // 调用系统设置服务记录 Token 统计
      await systemSettingService.incrementTokenUsage(
        provider,
        this.modelConfig.modelName,
        tokenUsage.inputTokens,
        tokenUsage.outputTokens
      );

      logger.info(`${this.name} Token使用统计已记录:`, {
        provider,
        model: this.modelConfig.modelName,
        inputTokens: tokenUsage.inputTokens,
        outputTokens: tokenUsage.outputTokens,
        totalTokens: tokenUsage.totalTokens
      });

    } catch (error) {
      logger.error('记录Token使用统计失败:', error);
      // 不抛出错误，避免影响翻译的主要功能
    }
  }



  /**
   * 产品翻译 - 一次性翻译标题+描述+卖点
   * 利用大模型能力一次性处理所有内容，适用于表单翻译和批量翻译
   */
  async translateProduct(
    productData: {
      title: string;
      description: string;
      sellingPoints: string[];
    },
    sourceLang: LanguageCode,
    targetLang: LanguageCode
  ): Promise<{
    success: boolean;
    result?: {
      title: string;
      description: string;
      sellingPoints: string[];
    };
    error?: string;
    tokenUsage?: {
      inputTokens: number;
      outputTokens: number;
      totalTokens: number;
    };
  }> {
    try {
      // 验证输入
      if (!this.validateText(productData.title) || !this.validateText(productData.description)) {
        return {
          success: false,
          error: '产品标题和描述不能为空'
        };
      }

      if (!this.validateLanguageCode(sourceLang) || !this.validateLanguageCode(targetLang)) {
        return {
          success: false,
          error: '不支持的语言代码'
        };
      }

      // TODO: 从系统设置中获取产品翻译提示词模板
      // 暂时使用简单的提示词进行测试
      const systemPrompt = `You are a professional e-commerce translator. Translate the given product information accurately and return the result in JSON format with keys: title, description, sellingPoints (array).`;

      const userInput = `Translate the following product information from ${sourceLang} to ${targetLang}:
Title: ${productData.title}
Description: ${productData.description}
Selling Points: ${productData.sellingPoints.join(', ')}`;

      // 调用 OpenAI 兼容 API
      const result = await this.callOpenAIAPI(systemPrompt, userInput);

      if (!result.success) {
        return {
          success: false,
          error: result.error,
          tokenUsage: result.tokenUsage
        };
      }

      // 解析翻译结果
      const parsedResult = this.parseProductTranslationResult(result.text!);

      if (!parsedResult) {
        return {
          success: false,
          error: '翻译结果解析失败',
          tokenUsage: result.tokenUsage
        };
      }

      // 记录 Token 使用统计
      if (result.tokenUsage) {
        await this.recordTokenUsage(result.tokenUsage);
      }

      return {
        success: true,
        result: parsedResult,
        tokenUsage: result.tokenUsage
      };

    } catch (error) {
      logger.error(`${this.name} 产品翻译失败:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : '产品翻译服务异常'
      };
    }
  }

  /**
   * SKU批量翻译 - 支持多个SKU的产品翻译
   * 前端传入SKU数组，后端获取产品信息并翻译
   */
  async translateSKUBatch(
    skus: string[],
    sourceLang: LanguageCode,
    targetLang: LanguageCode,
    options: {
      concurrency?: number;      // 并发数，默认3
      progressCallback?: (progress: {
        total: number;
        completed: number;
        failed: number;
        current?: string;
        percentage: number;
      }) => void;
    } = {}
  ): Promise<{
    success: boolean;
    results: Array<{
      sku: string;
      success: boolean;
      result?: {
        title: string;
        description: string;
        sellingPoints: string[];
      };
      error?: string;
      tokenUsage?: {
        inputTokens: number;
        outputTokens: number;
        totalTokens: number;
      };
    }>;
    summary: {
      total: number;
      successful: number;
      failed: number;
      duration: number;
      totalTokenUsage: {
        inputTokens: number;
        outputTokens: number;
        totalTokens: number;
      };
    };
  }> {
    const startTime = Date.now();
    const { concurrency = 3, progressCallback } = options;

    logger.info(`${this.name} 开始SKU批量翻译:`, {
      totalSKUs: skus.length,
      sourceLang,
      targetLang,
      concurrency
    });

    const results: any[] = [];
    let completed = 0;
    let failed = 0;
    let totalTokenUsage = {
      inputTokens: 0,
      outputTokens: 0,
      totalTokens: 0
    };

    // 并发处理SKU翻译
    const processSKU = async (sku: string): Promise<any> => {
      try {
        progressCallback?.({
          total: skus.length,
          completed,
          failed,
          current: `正在翻译 SKU: ${sku}`,
          percentage: Math.round((completed / skus.length) * 100)
        });

        // TODO: 这里需要调用产品服务获取SKU对应的产品信息
        // const productData = await productService.getProductBySKU(sku);

        // 暂时使用模拟数据，实际使用时需要替换
        const productData = {
          title: `Product Title for ${sku}`,
          description: `Product Description for ${sku}`,
          sellingPoints: [`Selling Point 1 for ${sku}`, `Selling Point 2 for ${sku}`]
        };

        const result = await this.translateProduct(productData, sourceLang, targetLang);

        if (result.success) {
          completed++;
          if (result.tokenUsage) {
            totalTokenUsage.inputTokens += result.tokenUsage.inputTokens;
            totalTokenUsage.outputTokens += result.tokenUsage.outputTokens;
            totalTokenUsage.totalTokens += result.tokenUsage.totalTokens;
          }
        } else {
          failed++;
        }

        return {
          sku,
          success: result.success,
          result: result.result,
          error: result.error,
          tokenUsage: result.tokenUsage
        };

      } catch (error) {
        failed++;
        logger.error(`SKU ${sku} 翻译失败:`, error);
        return {
          sku,
          success: false,
          error: error instanceof Error ? error.message : '翻译异常'
        };
      }
    };

    // 分批并发处理
    const chunks = [];
    for (let i = 0; i < skus.length; i += concurrency) {
      chunks.push(skus.slice(i, i + concurrency));
    }

    for (const chunk of chunks) {
      const chunkPromises = chunk.map(processSKU);
      const chunkResults = await Promise.all(chunkPromises);
      results.push(...chunkResults);

      // 批次间短暂休息
      if (chunk !== chunks[chunks.length - 1]) {
        await this.sleep(1000);
      }
    }

    const endTime = Date.now();
    const duration = endTime - startTime;

    const summary = {
      total: skus.length,
      successful: completed,
      failed: failed,
      duration,
      totalTokenUsage
    };

    logger.info(`${this.name} SKU批量翻译完成:`, summary);

    return {
      success: failed === 0,
      results,
      summary
    };
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.callOpenAIAPI(
        'You are a helpful assistant.',
        'Hello, this is a connection test. Please respond with "Connection successful".'
      );
      return result.success;
    } catch (error) {
      logger.error(`${this.name} health check failed:`, error);
      return false;
    }
  }
}
