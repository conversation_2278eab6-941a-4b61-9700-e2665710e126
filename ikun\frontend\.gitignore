# ===================================
# Dependencies
# ===================================
node_modules/
/.pnp
.pnp.js
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz

# ===================================
# Next.js
# ===================================
/.next/
/out/
next-env.d.ts

# ===================================
# Production Build
# ===================================
/build
/dist

# ===================================
# Environment Variables
# ===================================
.env
.env.*
!.env.example
.env.local
.env.development.local
.env.test.local
.env.production.local

# ===================================
# Logs
# ===================================
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# ===================================
# Runtime Data
# ===================================
pids
*.pid
*.seed
*.pid.lock

# ===================================
# Coverage & Testing
# ===================================
coverage/
*.lcov
.nyc_output
test-results/
playwright-report/

# ===================================
# Cache
# ===================================
.eslintcache
.cache
.parcel-cache
.turbo/

# ===================================
# TypeScript
# ===================================
*.tsbuildinfo

# ===================================
# IDE & Editors
# ===================================
.vscode/
.idea/
*.swp
*.swo
*~
.project
.classpath
.c9/
*.launch
.settings/
.vscode-test

# ===================================
# OS Generated Files
# ===================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
Desktop.ini

# ===================================
# Package Managers
# ===================================
.npm
.pnpm-store/

# ===================================
# Vercel
# ===================================
.vercel

# ===================================
# Application Specific
# ===================================
# Temporary files
tmp/
temp/

# User uploads
public/uploads/
public/temp/

# Generated files
public/sitemap.xml
public/robots.txt

# ===================================
# Storybook
# ===================================
storybook-static/

# ===================================
# Miscellaneous
# ===================================
*.tgz
.yarn-integrity
.tern-port
