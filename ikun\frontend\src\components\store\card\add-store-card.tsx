'use client'

import { useState, useEffect } from 'react'
import { Store, Platform } from '@/types'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { X, Plus } from 'lucide-react'

interface AddStoreCardProps {
  platforms: Platform[]
  onSubmit: (storeData: Partial<Store>) => Promise<void>
  onCancel: () => void
}

export function AddStoreCard({ platforms, onSubmit, onCancel }: AddStoreCardProps) {
  const [formData, setFormData] = useState({
    platform_code: '',
    store_name: '',
    status: 'active' as 'active' | 'failed',
    api_key: '',
    token: '',
    site: '',
    platform_config: {} as any
  })

  const [selectedPlatform, setSelectedPlatform] = useState<Platform | null>(null)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  // 当选择平台时，更新平台配置字段
  useEffect(() => {
    if (formData.platform_code) {
      const platform = platforms.find(p => p.platform_code === formData.platform_code)
      setSelectedPlatform(platform || null)
      
      // 重置平台特定配置
      setFormData(prev => ({
        ...prev,
        platform_config: {}
      }))
    }
  }, [formData.platform_code, platforms])

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // 清除对应字段的错误
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const handlePlatformConfigChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      platform_config: {
        ...prev.platform_config,
        [field]: value
      }
    }))
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.platform_code) {
      newErrors.platform_code = '请选择平台'
    }

    if (!formData.store_name.trim()) {
      newErrors.store_name = '请输入店铺名称'
    }

    // 验证平台特定的必填字段
    if (selectedPlatform?.config_fields) {
      const configFields = selectedPlatform.config_fields
      if (configFields.required_fields) {
        configFields.required_fields.forEach((field: string) => {
          if (!formData.platform_config[field]) {
            newErrors[`config_${field}`] = `${field} 是必填项`
          }
        })
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async () => {
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)
    try {
      await onSubmit(formData)
    } catch (error) {
      console.error('提交失败:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  // 渲染平台特定配置字段
  const renderPlatformConfigFields = () => {
    if (!selectedPlatform?.config_fields) {
      return null
    }

    const configFields = selectedPlatform.config_fields
    const requiredFields = configFields.required_fields || []
    const optionalFields = configFields.optional_fields || []
    const allFields = [...requiredFields, ...optionalFields]

    return (
      <div className="space-y-4">
        <div className="border-t pt-4">
          <h4 className="text-sm font-medium mb-3">{selectedPlatform.platform_name} 平台配置</h4>
          <div className="grid grid-cols-1 gap-4">
            {allFields.map((field: string) => {
              const isRequired = requiredFields.includes(field)
              const fieldKey = `config_${field}`
              
              return (
                <div key={field}>
                  <Label htmlFor={fieldKey}>
                    {field} {isRequired && <span className="text-red-500">*</span>}
                  </Label>
                  {field.toLowerCase().includes('password') ? (
                    <Input
                      id={fieldKey}
                      type="password"
                      value={formData.platform_config[field] || ''}
                      onChange={(e) => handlePlatformConfigChange(field, e.target.value)}
                      className={errors[fieldKey] ? 'border-red-500' : ''}
                    />
                  ) : (
                    <Input
                      id={fieldKey}
                      value={formData.platform_config[field] || ''}
                      onChange={(e) => handlePlatformConfigChange(field, e.target.value)}
                      className={errors[fieldKey] ? 'border-red-500' : ''}
                    />
                  )}
                  {errors[fieldKey] && (
                    <p className="text-red-500 text-xs mt-1">{errors[fieldKey]}</p>
                  )}
                </div>
              )
            })}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
          <CardTitle className="text-lg font-semibold">新增店铺</CardTitle>
          <Button variant="ghost" size="sm" onClick={onCancel}>
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 基本信息 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="platform">
                平台 <span className="text-red-500">*</span>
              </Label>
              <Select 
                value={formData.platform_code} 
                onValueChange={(value) => handleInputChange('platform_code', value)}
              >
                <SelectTrigger className={errors.platform_code ? 'border-red-500' : ''}>
                  <SelectValue placeholder="选择平台" />
                </SelectTrigger>
                <SelectContent>
                  {platforms.map((platform) => (
                    <SelectItem key={platform.platform_code} value={platform.platform_code}>
                      {platform.platform_name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.platform_code && (
                <p className="text-red-500 text-xs mt-1">{errors.platform_code}</p>
              )}
            </div>

            <div>
              <Label htmlFor="store_name">
                店铺名称 <span className="text-red-500">*</span>
              </Label>
              <Input
                id="store_name"
                value={formData.store_name}
                onChange={(e) => handleInputChange('store_name', e.target.value)}
                placeholder="请输入店铺名称"
                className={errors.store_name ? 'border-red-500' : ''}
              />
              {errors.store_name && (
                <p className="text-red-500 text-xs mt-1">{errors.store_name}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="status">状态</Label>
              <Select 
                value={formData.status} 
                onValueChange={(value: 'active' | 'failed') => handleInputChange('status', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">正常</SelectItem>
                  <SelectItem value="failed">失败</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="site">站点</Label>
              <Input
                id="site"
                value={formData.site}
                onChange={(e) => handleInputChange('site', e.target.value)}
                placeholder="如: US, UK, DE"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="api_key">API Key</Label>
              <Input
                id="api_key"
                value={formData.api_key}
                onChange={(e) => handleInputChange('api_key', e.target.value)}
                placeholder="请输入API Key"
              />
            </div>

            <div>
              <Label htmlFor="token">Token</Label>
              <Input
                id="token"
                value={formData.token}
                onChange={(e) => handleInputChange('token', e.target.value)}
                placeholder="请输入Token"
              />
            </div>
          </div>

          {/* 平台特定配置字段 */}
          {renderPlatformConfigFields()}

          {/* 操作按钮 */}
          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button variant="outline" onClick={onCancel}>
              取消
            </Button>
            <Button onClick={handleSubmit} disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  提交中...
                </>
              ) : (
                <>
                  <Plus className="w-4 h-4 mr-2" />
                  添加店铺
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
