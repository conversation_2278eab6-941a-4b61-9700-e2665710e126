/**
 * Platform Controller
 * 平台管理控制器
 */

import { Request, Response, NextFunction } from 'express';
import { platformService } from '@/services/stores/platformService';
import { logger } from '@/utils/logger';
import { getBeijingTimeISO } from '@/utils/time';

class PlatformController {
  // GET /api/v1/stores/platforms/list - Get platforms list
  public async getPlatforms(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const platforms = await platformService.getPlatforms();

      res.status(200).json({
        code: 200,
        message: 'Platforms retrieved successfully',
        data: platforms,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // GET /api/v1/stores/platforms/:code - Get platform details
  public async getPlatformByCode(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { code } = req.params;
      const platform = await platformService.getPlatformByCode(code);

      res.status(200).json({
        code: 200,
        message: 'Platform retrieved successfully',
        data: platform,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // POST /api/v1/stores/platforms - Create platform
  public async createPlatform(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const platformData = req.body;
      const platform = await platformService.createPlatform(platformData);

      logger.info('Platform created:', { platformCode: platform.platform_code });

      res.status(201).json({
        code: 201,
        message: 'Platform created successfully',
        data: platform,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // PUT /api/v1/stores/platforms/:code - Update platform
  public async updatePlatform(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { code } = req.params;
      const updateData = req.body;
      const platform = await platformService.updatePlatform(code, updateData);

      logger.info('Platform updated:', { platformCode: code });

      res.status(200).json({
        code: 200,
        message: 'Platform updated successfully',
        data: platform,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }

  // PUT /api/v1/stores/platforms/:code/status - Update platform status
  public async updatePlatformStatus(req: Request, res: Response, next: NextFunction): Promise<void> {
    try {
      const { code } = req.params;
      const { status } = req.body;
      const platform = await platformService.updatePlatformStatus(code, status);

      logger.info('Platform status updated:', { platformCode: code, status });

      res.status(200).json({
        code: 200,
        message: 'Platform status updated successfully',
        data: platform,
        timestamp: getBeijingTimeISO()
      });
    } catch (error) {
      next(error);
    }
  }
}

export const platformController = new PlatformController();
