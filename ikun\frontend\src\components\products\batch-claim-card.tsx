'use client'

import { useState, useEffect } from 'react'
import { useStores } from '@/hooks/useStores'
import { useToast } from '@/hooks/use-toast'
import { Store } from '@/types'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, Store as StoreIcon, Check } from 'lucide-react'

interface BatchClaimCardProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  selectedProductIds: number[]
  onSuccess?: () => void
}

export function BatchClaimCard({
  open,
  onOpenChange,
  selectedProductIds,
  onSuccess
}: BatchClaimCardProps) {
  const [selectedStores, setSelectedStores] = useState<number[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  const {
    stores,
    loading: storesLoading,
    fetchStores
  } = useStores()

  // 获取店铺数据
  useEffect(() => {
    if (open) {
      fetchStores()
    }
  }, [open, fetchStores])

  // 重置状态
  useEffect(() => {
    if (open) {
      setSelectedStores([])
      setIsSubmitting(false)
    }
  }, [open])

  const handleStoreToggle = (storeId: number) => {
    setSelectedStores(prev =>
      prev.includes(storeId)
        ? prev.filter(id => id !== storeId)
        : [...prev, storeId]
    )
  }

  const handleSubmit = async () => {
    if (selectedStores.length === 0) {
      toast({
        title: '请选择店铺',
        description: '请至少选择一个店铺进行认领',
        variant: 'destructive'
      })
      return
    }

    if (selectedProductIds.length === 0) {
      toast({
        title: '请选择产品',
        description: '请至少选择一个产品进行认领',
        variant: 'destructive'
      })
      return
    }

    setIsSubmitting(true)

    try {
      const token = localStorage.getItem('auth_token')
      if (!token) {
        throw new Error('请先登录')
      }

      // 调用批量认领API
      const response = await fetch('/api/v1/products/batch-claim', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          product_ids: selectedProductIds,
          store_ids: selectedStores
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.message || '批量认领失败')
      }

      const result = await response.json()

      toast({
        title: '批量认领成功',
        description: `成功认领 ${result.data?.success_count || selectedProductIds.length} 个产品到 ${selectedStores.length} 个店铺`
      })

      onSuccess?.()
      onOpenChange(false)
    } catch (error) {
      console.error('批量认领失败:', error)
      toast({
        title: '批量认领失败',
        description: error instanceof Error ? error.message : '未知错误',
        variant: 'destructive'
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return '正常'
      case 'failed':
        return '失败'
      default:
        return '未知'
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>批量认领产品</DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              已选择 {selectedProductIds.length} 个产品，请选择要认领到的店铺：
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedStores(stores.map(s => s.id))}
                disabled={storesLoading || stores.length === 0}
              >
                全选
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setSelectedStores([])}
                disabled={selectedStores.length === 0}
              >
                取消全选
              </Button>
            </div>
          </div>

          {storesLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-6 h-6 animate-spin" />
              <span className="ml-2">加载店铺列表...</span>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {stores.map((store) => (
                <Card
                  key={store.id}
                  className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
                    selectedStores.includes(store.id)
                      ? 'ring-2 ring-blue-500 bg-blue-50'
                      : 'hover:bg-gray-50'
                  }`}
                  onClick={() => handleStoreToggle(store.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center space-x-3 flex-1">
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          <StoreIcon className="w-5 h-5 text-blue-600" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium text-sm truncate">
                            {store.store_name}
                          </h3>
                          <p className="text-xs text-muted-foreground truncate">
                            {store.platform_name || store.platform_code}
                          </p>
                        </div>
                      </div>
                      {selectedStores.includes(store.id) && (
                        <Check className="w-5 h-5 text-blue-600 flex-shrink-0" />
                      )}
                    </div>
                    
                    <div className="mt-3 flex items-center justify-between">
                      <Badge
                        variant="outline"
                        className={`text-xs ${getStatusColor(store.status)}`}
                      >
                        {getStatusText(store.status)}
                      </Badge>
                      {store.site && (
                        <span className="text-xs text-muted-foreground">
                          {store.site}
                        </span>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {stores.length === 0 && !storesLoading && (
            <div className="text-center py-8 text-muted-foreground">
              暂无可用店铺
            </div>
          )}

          {selectedStores.length > 0 && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
              <div className="text-sm text-blue-800">
                已选择 {selectedStores.length} 个店铺，将认领 {selectedProductIds.length} 个产品
              </div>
            </div>
          )}

          <div className="flex justify-end space-x-2 pt-4 border-t">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              取消
            </Button>
            <Button
              onClick={handleSubmit}
              disabled={isSubmitting || selectedStores.length === 0}
            >
              {isSubmitting && <Loader2 className="w-4 h-4 mr-2 animate-spin" />}
              认领到 {selectedStores.length} 个店铺
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
